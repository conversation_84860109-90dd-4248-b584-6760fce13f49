#!/bin/bash
set -e

echo "📋 Listing Supabase functions..."

while read -r function_name; do
  # Skip empty lines and lines with only dashes
  if [[ -z "$function_name" || "$function_name" =~ ^-+$ ]]; then
    continue
  fi

  echo "⬇️  Downloading function: $function_name"
  npx supabase functions download "$function_name" || echo "   ❌ Failed to download: $function_name"
done < <(npx supabase functions list | tail -n +4 | awk -F'|' '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}')

echo "✅ All functions downloaded."