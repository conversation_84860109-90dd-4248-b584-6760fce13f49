#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Setting up Onboarding Functionality for ROFOF Application ===${NC}"
echo

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}Error: Supabase CLI is not installed.${NC}"
    echo "Please install it first: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're in the project root
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: This script must be run from the project root directory.${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 1: Linking to Supabase project...${NC}"
echo "You'll need to provide your Supabase project details."
echo

# Ask for Supabase project details if not already linked
if [ ! -f "supabase/.env" ]; then
    echo "Please run 'supabase link' to connect to your Supabase project."
    supabase link
else
    echo -e "${GREEN}Already linked to a Supabase project.${NC}"
fi

echo
echo -e "${YELLOW}Step 2: Creating onboarding tables and policies...${NC}"
echo

# Run the SQL script for onboarding tables
echo "Running onboarding tables setup script..."
supabase db execute --file setup-onboarding-tables.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully created onboarding tables and policies.${NC}"
else
    echo -e "${RED}Error running onboarding tables script. Please check the output above for details.${NC}"
    exit 1
fi

echo
echo -e "${YELLOW}Step 3: Installing required dependencies...${NC}"
echo

# Check if framer-motion is installed
if ! npm list framer-motion &> /dev/null; then
    echo "Installing framer-motion for animations..."
    npm install framer-motion
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Successfully installed framer-motion.${NC}"
    else
        echo -e "${RED}Error installing framer-motion. Please install it manually: npm install framer-motion${NC}"
    fi
else
    echo -e "${GREEN}framer-motion is already installed.${NC}"
fi

echo
echo -e "${YELLOW}Step 4: Verifying setup...${NC}"
echo

# Verify the onboarding_progress table exists
echo "Checking if onboarding_progress table exists..."
ONBOARDING_TABLE_EXISTS=$(supabase db execute --raw "SELECT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'onboarding_progress');" | grep -o 't\|f')

if [ "$ONBOARDING_TABLE_EXISTS" = "t" ]; then
    echo -e "${GREEN}onboarding_progress table exists.${NC}"
else
    echo -e "${RED}onboarding_progress table does not exist. Setup may have failed.${NC}"
    exit 1
fi

# Verify the profile_completion table exists
echo "Checking if profile_completion table exists..."
PROFILE_TABLE_EXISTS=$(supabase db execute --raw "SELECT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profile_completion');" | grep -o 't\|f')

if [ "$PROFILE_TABLE_EXISTS" = "t" ]; then
    echo -e "${GREEN}profile_completion table exists.${NC}"
else
    echo -e "${RED}profile_completion table does not exist. Setup may have failed.${NC}"
    exit 1
fi

# Verify RLS is enabled on onboarding_progress
echo "Checking if Row Level Security is enabled on onboarding_progress..."
ONBOARDING_RLS_ENABLED=$(supabase db execute --raw "SELECT relrowsecurity FROM pg_class WHERE relname = 'onboarding_progress' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');" | grep -o 't\|f')

if [ "$ONBOARDING_RLS_ENABLED" = "t" ]; then
    echo -e "${GREEN}Row Level Security is enabled for onboarding_progress table.${NC}"
else
    echo -e "${RED}Row Level Security is NOT enabled for onboarding_progress table. Setup may have failed.${NC}"
    exit 1
fi

# Verify RLS is enabled on profile_completion
echo "Checking if Row Level Security is enabled on profile_completion..."
PROFILE_RLS_ENABLED=$(supabase db execute --raw "SELECT relrowsecurity FROM pg_class WHERE relname = 'profile_completion' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');" | grep -o 't\|f')

if [ "$PROFILE_RLS_ENABLED" = "t" ]; then
    echo -e "${GREEN}Row Level Security is enabled for profile_completion table.${NC}"
else
    echo -e "${RED}Row Level Security is NOT enabled for profile_completion table. Setup may have failed.${NC}"
    exit 1
fi

echo
echo -e "${GREEN}=== Onboarding Functionality Setup Complete! ===${NC}"
echo
echo -e "🎉 ${GREEN}Congratulations!${NC} Your onboarding system is now ready."
echo
echo -e "${BLUE}What was set up:${NC}"
echo "✅ Onboarding progress tracking table"
echo "✅ Profile completion data table"
echo "✅ Row Level Security policies"
echo "✅ Analytics views for administrators"
echo "✅ Helper functions for completion tracking"
echo "✅ Required dependencies (framer-motion)"
echo
echo -e "${YELLOW}Features included:${NC}"
echo "🌟 Welcome screen with dynamic content based on user type"
echo "📝 Step-by-step profile completion process"
echo "🎨 Beautiful animations and transitions"
echo "📱 Mobile-responsive design"
echo "🔒 Secure data storage with RLS policies"
echo "📊 Admin analytics for onboarding insights"
echo "🌍 Full Arabic language support"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Start your application with 'npm run dev'"
echo "2. Register a new store or e-commerce account"
echo "3. Log in and experience the onboarding flow"
echo "4. Check the onboarding_progress and profile_completion tables in Supabase"
echo "5. As an admin, view onboarding analytics in the onboarding_analytics view"
echo
echo -e "${BLUE}Routes added:${NC}"
echo "• /onboarding - Main onboarding flow"
echo "• Automatic redirection from dashboard for incomplete onboarding"
echo
echo -e "${GREEN}Happy onboarding! 🚀${NC}"
