

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."subscription" AS ENUM (
    'percentage',
    'fixed'
);


ALTER TYPE "public"."subscription" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_hosting_request"("request_data" "jsonb", "products_data" "jsonb"[]) RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_request hosting_requests;
  result JSONB;
  store_owner_id UUID;
BEGIN
  -- Validate status value
  IF NOT (request_data->>'status' = ANY(ARRAY['pending', 'accepted', 'rejected', 'ready', 'delivered', 'cancelled', 'awaiting_shipping', 'on_sale', 'expired'])) THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Invalid status value'
    );
  END IF;

  -- Insert hosting request with proper type casting
  INSERT INTO hosting_requests (
    ecommerce_id, ecommerce_name, store_id, store_name,
    status, contract_id, notes, expires_at,
    shipping_confirmed, receipt_confirmed,
    ecommerce_legal_activity, subscription_type
  ) VALUES (
    (request_data->>'ecommerce_id')::UUID,
    request_data->>'ecommerce_name',
    (request_data->>'store_id')::UUID,
    request_data->>'store_name',
    request_data->>'status',
    (request_data->>'contract_id')::UUID,
    request_data->>'notes',
    (request_data->>'expires_at')::TIMESTAMPTZ,
    COALESCE((request_data->>'shipping_confirmed')::BOOLEAN, false),
    COALESCE((request_data->>'receipt_confirmed')::BOOLEAN, false),
    request_data->>'ecommerce_legal_activity',
    COALESCE((request_data->>'subscription_type')::subscription, 'percentage'::subscription)
  ) RETURNING * INTO new_request;

  -- Insert associated products if provided
  IF products_data IS NOT NULL AND array_length(products_data, 1) > 0 THEN
    INSERT INTO hosting_products (
      hosting_request_id, product_id, product_name,
      price, quantity, image
    )
    SELECT
      new_request.id,
      (product->>'product_id')::UUID,
      product->>'product_name',
      (product->>'price')::NUMERIC,
      (product->>'quantity')::INTEGER,
      product->>'image'
    FROM unnest(products_data) AS product;
  END IF;

  -- Get owner_id of the store
  SELECT owner_id INTO store_owner_id
  FROM stores
  WHERE id = new_request.store_id;

  -- Send notification to the store owner
  IF store_owner_id IS NOT NULL THEN
    INSERT INTO notifications (
      user_id,
      title,
      message,
      type,
      link
    ) VALUES (
      store_owner_id,
      'طلب استضافة جديد',
      'تم إرسال طلب استضافة من ' || new_request.ecommerce_name,
      'info',
      '/dashboard/hosting-requests/' || new_request.id
    );
  END IF;

  -- Return operation result
  SELECT jsonb_build_object(
    'success', true,
    'request_id', new_request.id,
    'products_count', COALESCE(array_length(products_data, 1), 0)
  ) INTO result;

  RETURN result;

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$;


ALTER FUNCTION "public"."create_hosting_request"("request_data" "jsonb", "products_data" "jsonb"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_id" "uuid") RETURNS "text"
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$SELECT role FROM public.profiles WHERE id = user_id;$$;


ALTER FUNCTION "public"."get_user_role"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$BEGIN
  INSERT INTO public.profiles (id, name, email, role, avatar)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'ecommerce'),
    COALESCE(NEW.raw_user_meta_data->>'avatar', 'https://ui-avatars.com/api/?name=' || encode(NEW.email::bytea, 'base64') || '&background=random')
  );
  RETURN NEW;
END;$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"("user_id" "uuid") RETURNS boolean
    LANGUAGE "sql" STABLE SECURITY DEFINER
    AS $$
  SELECT (SELECT role FROM public.profiles WHERE id = user_id) = 'admin';
$$;


ALTER FUNCTION "public"."is_admin"("user_id" "uuid") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "role" "text" NOT NULL,
    "avatar" "text",
    "balance" numeric DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "phone_number" "text",
    "address" "text",
    "bank_account" "text",
    "legal_activity" "text",
    "active" boolean DEFAULT true,
    "permissions" "text"[] DEFAULT '{}'::"text"[],
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "iban" "text",
    CONSTRAINT "profiles_role_check" CHECK (("role" = ANY (ARRAY['ecommerce'::"text", 'store'::"text", 'admin'::"text"])))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."admin_public_data" WITH ("security_invoker"='on') AS
 SELECT "profiles"."id",
    "profiles"."name",
    "profiles"."email",
    "profiles"."iban",
    "profiles"."bank_account"
   FROM "public"."profiles"
  WHERE ("profiles"."role" = 'admin'::"text");


ALTER TABLE "public"."admin_public_data" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."branches" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "store_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "address" "text" NOT NULL,
    "city" "text" NOT NULL,
    "phone_number" "text" NOT NULL,
    "manager_name" "text",
    "working_hours" "text" NOT NULL,
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "images" "text"[],
    "legal_activity" "text"
);


ALTER TABLE "public"."branches" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."hosting_products" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "hosting_request_id" "uuid" NOT NULL,
    "product_id" "uuid" NOT NULL,
    "product_name" "text" NOT NULL,
    "price" numeric NOT NULL,
    "quantity" integer NOT NULL,
    "image" "text"
);


ALTER TABLE "public"."hosting_products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."hosting_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "ecommerce_id" "uuid" NOT NULL,
    "ecommerce_name" "text" NOT NULL,
    "store_id" "uuid" NOT NULL,
    "store_name" "text" NOT NULL,
    "status" "text" NOT NULL,
    "contract_id" "uuid",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "expires_at" timestamp with time zone,
    "shipping_confirmed" boolean DEFAULT false,
    "receipt_confirmed" boolean DEFAULT false,
    "ecommerce_legal_activity" "text",
    "subscription_type" "public"."subscription" DEFAULT 'percentage'::"public"."subscription" NOT NULL,
    CONSTRAINT "hosting_requests_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'accepted'::"text", 'rejected'::"text", 'ready'::"text", 'delivered'::"text", 'cancelled'::"text", 'awaiting_shipping'::"text", 'on_sale'::"text", 'expired'::"text"])))
);


ALTER TABLE "public"."hosting_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invoices" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "hosting_request_id" "uuid" NOT NULL,
    "ecommerce_id" "uuid" NOT NULL,
    "store_id" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "amount" numeric NOT NULL,
    "tax_amount" numeric NOT NULL,
    "total_amount" numeric NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "exported_at" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "due_date" timestamp with time zone NOT NULL,
    CONSTRAINT "invoices_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'exported'::"text", 'completed'::"text", 'cancelled'::"text"])))
);


ALTER TABLE "public"."invoices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."legal_details" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "profile_id" "uuid" NOT NULL,
    "legal_name" "text",
    "registration_number" "text",
    "tax_number" "text",
    "contact_phone" "text",
    "bank_account" "text",
    "legal_activity" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "national_id" "text",
    "tax_certificate" "text",
    "commercial_registry" "text"
);


ALTER TABLE "public"."legal_details" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "message" "text" NOT NULL,
    "type" "text" NOT NULL,
    "read" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "link" "text",
    CONSTRAINT "notifications_type_check" CHECK (("type" = ANY (ARRAY['info'::"text", 'success'::"text", 'warning'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."onboarding_progress" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "current_step" integer DEFAULT 0,
    "completed_steps" "text"[] DEFAULT '{}'::"text"[],
    "total_steps" integer DEFAULT 0,
    "is_completed" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."onboarding_progress" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."otps" (
    "id" integer NOT NULL,
    "email" "text" NOT NULL,
    "otp" "text" NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "type" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "otps_type_check" CHECK (("type" = ANY (ARRAY['login'::"text", 'register'::"text"])))
);


ALTER TABLE "public"."otps" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."otps_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."otps_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."otps_id_seq" OWNED BY "public"."otps"."id";



CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "price" numeric NOT NULL,
    "quantity" integer NOT NULL,
    "seller_id" "uuid" NOT NULL,
    "seller_name" "text" NOT NULL,
    "images" "text"[],
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "category" "text",
    "status" "text" DEFAULT 'active'::"text",
    "user_id" "uuid",
    CONSTRAINT "products_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'inactive'::"text", 'deleted'::"text"])))
);


ALTER TABLE "public"."products" OWNER TO "postgres";


COMMENT ON COLUMN "public"."products"."user_id" IS 'Reference to the user who created this product, same as seller_id';



CREATE TABLE IF NOT EXISTS "public"."profile_completion" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "personal_info" "jsonb" DEFAULT '{}'::"jsonb",
    "business_info" "jsonb" DEFAULT '{}'::"jsonb",
    "location_info" "jsonb" DEFAULT '{}'::"jsonb",
    "preferences" "jsonb" DEFAULT '{}'::"jsonb",
    "verification" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."profile_completion" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."store_employees" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "store_id" "uuid" NOT NULL,
    "branch_id" "uuid",
    "name" "text" NOT NULL,
    "email" "text" NOT NULL,
    "phone_number" "text" NOT NULL,
    "position" "text" NOT NULL,
    "permissions" "text"[],
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."store_employees" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."stores" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "owner_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "address" "text" NOT NULL,
    "phone" "text" NOT NULL,
    "email" "text" NOT NULL,
    "hours" "text" NOT NULL,
    "image" "text",
    "gallery" "text"[],
    "description" "text",
    "rating" numeric DEFAULT 0,
    "featured" boolean DEFAULT false,
    "shelf_space" "text",
    "capacity" integer,
    "legal_activity" "text",
    "city" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "subscription" "text"
);


ALTER TABLE "public"."stores" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."transactions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "user_name" "text" NOT NULL,
    "amount" numeric NOT NULL,
    "type" "text" NOT NULL,
    "status" "text" NOT NULL,
    "reference" "text",
    "related_order_id" "uuid",
    "description" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "receipt_url" "text",
    "updated_at" timestamp with time zone,
    "from_user_id" "uuid",
    "to_user_id" "uuid",
    CONSTRAINT "transactions_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'completed'::"text", 'failed'::"text", 'rejected'::"text"]))),
    CONSTRAINT "transactions_type_check" CHECK (("type" = ANY (ARRAY['deposit'::"text", 'withdraw'::"text", 'fee'::"text", 'commission'::"text", 'transfer'::"text"])))
);


ALTER TABLE "public"."transactions" OWNER TO "postgres";


ALTER TABLE ONLY "public"."otps" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."otps_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."branches"
    ADD CONSTRAINT "branches_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."hosting_products"
    ADD CONSTRAINT "hosting_products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."hosting_requests"
    ADD CONSTRAINT "hosting_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."legal_details"
    ADD CONSTRAINT "legal_details_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."legal_details"
    ADD CONSTRAINT "legal_details_profile_id_key" UNIQUE ("profile_id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."onboarding_progress"
    ADD CONSTRAINT "onboarding_progress_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."otps"
    ADD CONSTRAINT "otps_email_type_key" UNIQUE ("email", "type");



ALTER TABLE ONLY "public"."otps"
    ADD CONSTRAINT "otps_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profile_completion"
    ADD CONSTRAINT "profile_completion_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."store_employees"
    ADD CONSTRAINT "store_employees_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."stores"
    ADD CONSTRAINT "stores_owner_id_key" UNIQUE ("owner_id");



ALTER TABLE ONLY "public"."stores"
    ADD CONSTRAINT "stores_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_onboarding_progress_completed" ON "public"."onboarding_progress" USING "btree" ("is_completed");



CREATE INDEX "idx_onboarding_progress_user_id" ON "public"."onboarding_progress" USING "btree" ("user_id");



CREATE INDEX "idx_otps_email_type" ON "public"."otps" USING "btree" ("email", "type");



CREATE INDEX "idx_otps_expires_at" ON "public"."otps" USING "btree" ("expires_at");



CREATE INDEX "idx_profile_completion_user_id" ON "public"."profile_completion" USING "btree" ("user_id");



CREATE INDEX "idx_transactions_from_user_id" ON "public"."transactions" USING "btree" ("from_user_id");



CREATE INDEX "idx_transactions_to_user_id" ON "public"."transactions" USING "btree" ("to_user_id");



ALTER TABLE ONLY "public"."branches"
    ADD CONSTRAINT "branches_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "public"."stores"("id");



ALTER TABLE ONLY "public"."hosting_products"
    ADD CONSTRAINT "hosting_products_hosting_request_id_fkey" FOREIGN KEY ("hosting_request_id") REFERENCES "public"."hosting_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."hosting_products"
    ADD CONSTRAINT "hosting_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."hosting_requests"
    ADD CONSTRAINT "hosting_requests_ecommerce_id_fkey" FOREIGN KEY ("ecommerce_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."hosting_requests"
    ADD CONSTRAINT "hosting_requests_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "public"."stores"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_ecommerce_id_fkey" FOREIGN KEY ("ecommerce_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_hosting_request_id_fkey" FOREIGN KEY ("hosting_request_id") REFERENCES "public"."hosting_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "public"."stores"("id");



ALTER TABLE ONLY "public"."legal_details"
    ADD CONSTRAINT "legal_details_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "public"."profiles"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."onboarding_progress"
    ADD CONSTRAINT "onboarding_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profile_completion"
    ADD CONSTRAINT "profile_completion_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."store_employees"
    ADD CONSTRAINT "store_employees_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "public"."branches"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."store_employees"
    ADD CONSTRAINT "store_employees_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "public"."stores"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."stores"
    ADD CONSTRAINT "stores_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id");



CREATE POLICY "Admin can view all products" ON "public"."products" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can delete" ON "public"."legal_details" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND ("p"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can insert" ON "public"."legal_details" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND ("p"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can manage all products" ON "public"."products" USING ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Admins can select" ON "public"."legal_details" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND ("p"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can update" ON "public"."legal_details" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."profiles" "p"
  WHERE (("p"."id" = "auth"."uid"()) AND ("p"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can update all profiles" ON "public"."profiles" FOR UPDATE USING ("public"."is_admin"("auth"."uid"()));



CREATE POLICY "Admins can view all profiles" ON "public"."profiles" FOR SELECT USING ("public"."is_admin"("auth"."uid"()));



CREATE POLICY "Allow all operations for authenticated users" ON "public"."otps" TO "authenticated" USING (true);



CREATE POLICY "Allow anonymous users to access OTPs" ON "public"."otps" TO "anon" USING (true);



CREATE POLICY "Allow employee or store owner (or admin) to access employee dat" ON "public"."store_employees" TO "authenticated" USING ((("auth"."uid"() = "id") OR (EXISTS ( SELECT 1
   FROM "public"."stores"
  WHERE (("stores"."id" = "store_employees"."store_id") AND ("stores"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))))) WITH CHECK ((("auth"."uid"() = "id") OR (EXISTS ( SELECT 1
   FROM "public"."stores"
  WHERE (("stores"."id" = "store_employees"."store_id") AND ("stores"."owner_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "Anyone can view branches" ON "public"."branches" FOR SELECT USING (true);



CREATE POLICY "Anyone can view stores" ON "public"."stores" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."hosting_products" USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."hosting_requests" USING (true);



CREATE POLICY "Enable users and admins to view/update appropriately" ON "public"."stores" TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))) OR ("owner_id" = "auth"."uid"()))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))) OR ("owner_id" = "auth"."uid"())));



CREATE POLICY "Store owners can manage their branches" ON "public"."branches" USING (("store_id" IN ( SELECT "stores"."id"
   FROM "public"."stores"
  WHERE ("stores"."owner_id" = "auth"."uid"()))));



CREATE POLICY "Store owners can manage their stores" ON "public"."stores" USING (("owner_id" = "auth"."uid"()));



CREATE POLICY "User can update their notifications" ON "public"."notifications" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "User can view their own notifications" ON "public"."notifications" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can delete their own branches" ON "public"."branches" FOR DELETE USING (("auth"."uid"() = "store_id"));



CREATE POLICY "Users can delete their own legal details" ON "public"."legal_details" FOR DELETE USING (("profile_id" = "auth"."uid"()));



CREATE POLICY "Users can delete their own products" ON "public"."products" FOR DELETE USING ((("auth"."uid"() = "seller_id") OR ("auth"."uid"() = "user_id")));



CREATE POLICY "Users can insert own onboarding progress" ON "public"."onboarding_progress" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own profile completion" ON "public"."profile_completion" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert products" ON "public"."products" FOR INSERT WITH CHECK ((("auth"."uid"() = "seller_id") AND ("auth"."uid"() = "user_id")));



CREATE POLICY "Users can insert their own branches" ON "public"."branches" FOR INSERT WITH CHECK (("auth"."uid"() = "store_id"));



CREATE POLICY "Users can insert their own legal details" ON "public"."legal_details" FOR INSERT WITH CHECK (("profile_id" = "auth"."uid"()));



CREATE POLICY "Users can insert their own products" ON "public"."products" FOR INSERT WITH CHECK (("auth"."uid"() = "seller_id"));



CREATE POLICY "Users can insert their own transactions" ON "public"."transactions" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can see their own data" ON "public"."profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update own onboarding progress" ON "public"."onboarding_progress" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own profile" ON "public"."profiles" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update own profile completion" ON "public"."profile_completion" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own branches" ON "public"."branches" FOR UPDATE USING (("auth"."uid"() = "store_id"));



CREATE POLICY "Users can update their own legal details" ON "public"."legal_details" FOR UPDATE USING (("profile_id" = "auth"."uid"()));



CREATE POLICY "Users can update their own products" ON "public"."products" FOR UPDATE USING ((("auth"."uid"() = "seller_id") OR ("auth"."uid"() = "user_id")));



CREATE POLICY "Users can update their own profile" ON "public"."profiles" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view own onboarding progress" ON "public"."onboarding_progress" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own profile" ON "public"."profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view own profile completion" ON "public"."profile_completion" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own branches" ON "public"."branches" FOR SELECT USING (("auth"."uid"() = "store_id"));



CREATE POLICY "Users can view their own legal details" ON "public"."legal_details" FOR SELECT USING (("profile_id" = "auth"."uid"()));



CREATE POLICY "Users can view their own products" ON "public"."products" FOR SELECT USING ((("auth"."uid"() = "seller_id") OR ("auth"."uid"() = "user_id")));



CREATE POLICY "Users can view their own profile" ON "public"."profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view their own transactions" ON "public"."transactions" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "admin role" ON "public"."transactions" TO "authenticated" USING ((("auth"."role"() = 'authenticated'::"text") AND (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))))) WITH CHECK ((("auth"."role"() = 'authenticated'::"text") AND (EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text"))))));



CREATE POLICY "allow access to admin public data" ON "public"."profiles" FOR SELECT TO "authenticated" USING (("role" = 'admin'::"text"));



ALTER TABLE "public"."branches" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."hosting_products" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."hosting_requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."invoices" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."legal_details" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notifications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."onboarding_progress" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."otps" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."products" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profile_completion" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."store_employees" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."stores" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."transactions" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."create_hosting_request"("request_data" "jsonb", "products_data" "jsonb"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."create_hosting_request"("request_data" "jsonb", "products_data" "jsonb"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_hosting_request"("request_data" "jsonb", "products_data" "jsonb"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "service_role";


















GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."admin_public_data" TO "anon";
GRANT ALL ON TABLE "public"."admin_public_data" TO "authenticated";
GRANT ALL ON TABLE "public"."admin_public_data" TO "service_role";



GRANT ALL ON TABLE "public"."branches" TO "anon";
GRANT ALL ON TABLE "public"."branches" TO "authenticated";
GRANT ALL ON TABLE "public"."branches" TO "service_role";



GRANT ALL ON TABLE "public"."hosting_products" TO "anon";
GRANT ALL ON TABLE "public"."hosting_products" TO "authenticated";
GRANT ALL ON TABLE "public"."hosting_products" TO "service_role";



GRANT ALL ON TABLE "public"."hosting_requests" TO "anon";
GRANT ALL ON TABLE "public"."hosting_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."hosting_requests" TO "service_role";



GRANT ALL ON TABLE "public"."invoices" TO "anon";
GRANT ALL ON TABLE "public"."invoices" TO "authenticated";
GRANT ALL ON TABLE "public"."invoices" TO "service_role";



GRANT ALL ON TABLE "public"."legal_details" TO "anon";
GRANT ALL ON TABLE "public"."legal_details" TO "authenticated";
GRANT ALL ON TABLE "public"."legal_details" TO "service_role";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";



GRANT ALL ON TABLE "public"."onboarding_progress" TO "anon";
GRANT ALL ON TABLE "public"."onboarding_progress" TO "authenticated";
GRANT ALL ON TABLE "public"."onboarding_progress" TO "service_role";



GRANT ALL ON TABLE "public"."otps" TO "anon";
GRANT ALL ON TABLE "public"."otps" TO "authenticated";
GRANT ALL ON TABLE "public"."otps" TO "service_role";



GRANT ALL ON SEQUENCE "public"."otps_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."otps_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."otps_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."products" TO "anon";
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."profile_completion" TO "anon";
GRANT ALL ON TABLE "public"."profile_completion" TO "authenticated";
GRANT ALL ON TABLE "public"."profile_completion" TO "service_role";



GRANT ALL ON TABLE "public"."store_employees" TO "anon";
GRANT ALL ON TABLE "public"."store_employees" TO "authenticated";
GRANT ALL ON TABLE "public"."store_employees" TO "service_role";



GRANT ALL ON TABLE "public"."stores" TO "anon";
GRANT ALL ON TABLE "public"."stores" TO "authenticated";
GRANT ALL ON TABLE "public"."stores" TO "service_role";



GRANT ALL ON TABLE "public"."transactions" TO "anon";
GRANT ALL ON TABLE "public"."transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."transactions" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
