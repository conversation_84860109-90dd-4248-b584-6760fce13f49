
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Parse request body
    const transactionData = await req.json();

    // Validate required fields
    const requiredFields = ['user_id', 'user_name', 'amount', 'type'];
    for (const field of requiredFields) {
      if (!transactionData[field]) {
        return new Response(
          JSON.stringify({ success: false, error: `Missing required field: ${field}` }),
          { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
    }

    // Set defaults for optional fields
    const transaction = {
      ...transactionData,
      status: transactionData.status || 'pending',
      description: transactionData.description || '',
      reference: transactionData.reference || `TXN-${Date.now()}`,
      created_at: transactionData.created_at || new Date().toISOString(),
      updated_at: transactionData.updated_at || new Date().toISOString(),
    };

    console.log('Creating transaction:', transaction);

    // Insert the transaction
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select('*')
      .single();

    if (error) {
      console.error('Error creating transaction:', error);
      return new Response(
        JSON.stringify({ success: false, error: error.message }),
        { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // Create a notification for the user
    let notificationTitle = '';
    let notificationMessage = '';
    
    if (transaction.type === 'deposit') {
      notificationTitle = 'تم استلام طلب الإيداع';
      notificationMessage = `تم استلام طلبك لإيداع ${transaction.amount} ر.س وسيتم مراجعته قريباً`;
    } else if (transaction.type === 'withdraw') {
      notificationTitle = 'تم استلام طلب السحب';
      notificationMessage = `تم استلام طلبك لسحب ${transaction.amount} ر.س وسيتم مراجعته قريباً`;
    }

    if (notificationTitle) {
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: transaction.user_id,
          type: 'info',
          title: notificationTitle,
          message: notificationMessage,
          link: '/dashboard/wallet',
        });

      if (notificationError) {
        console.error('Error creating notification:', notificationError);
        // Continue execution, notification is not critical
      }
    }

    // Return success with created transaction
    return new Response(
      JSON.stringify({ success: true, data }),
      { status: 201, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
    );
  }
});
