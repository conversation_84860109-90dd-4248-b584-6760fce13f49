
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is missing");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Create utility functions in the database
    const sql = `
    -- Function to get column names of a table
    CREATE OR REPLACE FUNCTION public.get_column_names(table_name TEXT)
    RETURNS TEXT[] 
    LANGUAGE plpgsql
    AS $$
    DECLARE
      columns TEXT[];
    BEGIN
      SELECT array_agg(column_name::TEXT)
      INTO columns
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = $1;
      
      RETURN columns;
    END;
    $$;
    
    -- Function to check if a column exists in a table
    CREATE OR REPLACE FUNCTION public.column_exists(table_name TEXT, column_name TEXT)
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    AS $$
    DECLARE
      exists_bool BOOLEAN;
    BEGIN
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public' 
          AND table_name = $1
          AND column_name = $2
      ) INTO exists_bool;
      
      RETURN exists_bool;
    END;
    $$;
    
    -- Function to add a column to a table if it doesn't exist
    CREATE OR REPLACE FUNCTION public.add_column_to_table(
      table_name TEXT,
      column_name TEXT,
      column_type TEXT,
      default_value TEXT DEFAULT NULL
    )
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    AS $$
    DECLARE
      column_exists BOOLEAN;
      sql TEXT;
    BEGIN
      -- Check if column already exists
      SELECT public.column_exists(table_name, column_name) INTO column_exists;
      
      IF NOT column_exists THEN
        -- Construct SQL to add column
        sql := format('ALTER TABLE public.%I ADD COLUMN IF NOT EXISTS %I %s', 
                     table_name, column_name, column_type);
                     
        -- Add default value if provided
        IF default_value IS NOT NULL THEN
          IF column_type IN ('text', 'varchar', 'char', 'uuid') THEN
            sql := sql || format(' DEFAULT %L', default_value);
          ELSIF column_type = 'boolean' THEN
            IF default_value = 'true' THEN
              sql := sql || ' DEFAULT true';
            ELSE
              sql := sql || ' DEFAULT false';
            END IF;
          ELSE
            sql := sql || format(' DEFAULT %s', default_value);
          END IF;
        END IF;
        
        -- Execute the SQL
        EXECUTE sql;
        RETURN TRUE;
      ELSE
        RETURN FALSE;
      END IF;
    END;
    $$;
    `;

    const { error: fnError } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (fnError) {
      throw new Error(`Error creating DB functions: ${fnError.message}`);
    }
    
    return new Response(JSON.stringify({
      success: true,
      message: "Database utility functions created successfully"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Error in create-db-functions:", error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message || "Failed to create database functions",
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
