
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.4";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Handle CORS preflight requests
const handleCors = (req: Request): Response | null => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  return null;
};

serve(async (req: Request) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Get request body
    const { email, password, name } = await req.json();
    
    // Create Supabase admin client
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );
    
    // Check if the user already exists
    const { data: existingUsers, error: userError } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .maybeSingle();
    
    let userId;
    let isNewUser = false;
    
    // If user doesn't exist, create a new user
    if (!existingUsers) {
      // Create new user
      const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          name,
          role: 'admin',
          active: true,
          permissions: ['manage_users', 'approve_payments', 'manage_transactions', 'manage_admins']
        }
      });
      
      if (createError) {
        return new Response(
          JSON.stringify({ success: false, error: createError.message }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
        );
      }
      
      userId = newUser.user.id;
      isNewUser = true;
      
      // Create profile for the new user
      await supabaseAdmin
        .from('profiles')
        .insert({
          id: userId,
          name,
          email,
          role: 'admin',
          active: true
        });
        
    } else {
      // User exists, update them to have admin role
      userId = existingUsers.id;
      
      // Update user metadata
      await supabaseAdmin.auth.admin.updateUserById(userId, {
        user_metadata: {
          name,
          role: 'admin',
          active: true,
          permissions: ['manage_users', 'approve_payments', 'manage_transactions', 'manage_admins']
        }
      });
      
      // Update profile
      await supabaseAdmin
        .from('profiles')
        .update({
          name,
          role: 'admin',
          active: true
        })
        .eq('id', userId);
    }
    
    return new Response(
      JSON.stringify({ success: true, userId, isNewUser }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 200 }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});
