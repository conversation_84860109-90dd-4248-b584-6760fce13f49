
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is missing");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Parse the request body
    let body;
    try {
      body = await req.json();
    } catch (error) {
      console.error("Error parsing request body:", error);
      return new Response(
        JSON.stringify({ error: "Invalid JSON in request body" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }
    
    const { table_name } = body || {};

    if (!table_name) {
      return new Response(
        JSON.stringify({ error: "Table name is required" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    console.log(`Fetching columns for table: ${table_name}`);

    // Query to get column information for the table
    const { data, error } = await supabase.rpc('get_table_columns', {
      table_name_param: table_name,
      schema_name_param: 'public'
    });

    if (error) {
      console.error("Database error:", error);
      
      // Fallback to direct information_schema query if RPC fails
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', table_name)
        .eq('table_schema', 'public');
        
      if (fallbackError) {
        throw fallbackError;
      }
      
      const columnNames = fallbackData ? fallbackData.map(col => col.column_name) : [];
      
      return new Response(
        JSON.stringify({ columns: columnNames }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        }
      );
    }

    const columnNames = data ? data.map(col => col.column_name) : [];
    
    return new Response(
      JSON.stringify({ columns: columnNames }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in get-column-names function:", error);
    return new Response(
      JSON.stringify({
        error: error.message || "Failed to get column names",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
