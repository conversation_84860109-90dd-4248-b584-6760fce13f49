
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { email, otp, type = "login" } = await req.json();
    
    if (!email || !otp) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Email and OTP are required" 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, "Content-Type": "application/json" } 
        }
      );
    }

    // Prepare email content based on OTP type
    let subject = "";
    let emailContent = "";
    
    if (type === "login") {
      subject = "رمز تسجيل الدخول إلى RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">رمز تسجيل الدخول</h2>
          <p>مرحباً،</p>
          <p>لقد طلبت رمز تحقق لتسجيل الدخول إلى حسابك في RFOF.</p>
          <div style="background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
            <h1 style="margin: 0; color: #333; letter-spacing: 5px;">${otp}</h1>
          </div>
          <p>يرجى استخدام هذا الرمز للمتابعة مع تسجيل الدخول. هذا الرمز صالح لمدة 10 دقائق.</p>
          <p>إذا لم تطلب هذا الرمز، فيرجى تجاهل هذه الرسالة.</p>
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    } else if (type === "register") {
      subject = "رمز تسجيل حسابك في RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">تأكيد حسابك الجديد</h2>
          <p>مرحباً،</p>
          <p>شكراً لتسجيلك في RFOF. لإكمال عملية التسجيل، يرجى استخدام رمز التحقق التالي:</p>
          <div style="background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
            <h1 style="margin: 0; color: #333; letter-spacing: 5px;">${otp}</h1>
          </div>
          <p>يرجى استخدام هذا الرمز لتأكيد حسابك. هذا الرمز صالح لمدة 10 دقائق.</p>
          <p>إذا لم تطلب هذا الرمز، فيرجى تجاهل هذه الرسالة.</p>
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    } else if (type === "recovery") {
      subject = "رمز استعادة كلمة المرور في RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">استعادة كلمة المرور</h2>
          <p>مرحباً،</p>
          <p>لقد طلبت إعادة تعيين كلمة المرور لحسابك في RFOF. يرجى استخدام رمز التحقق التالي:</p>
          <div style="background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
            <h1 style="margin: 0; color: #333; letter-spacing: 5px;">${otp}</h1>
          </div>
          <p>يرجى استخدام هذا الرمز لإعادة تعيين كلمة المرور. هذا الرمز صالح لمدة 10 دقائق.</p>
          <p>إذا لم تطلب هذا الرمز، فيرجى تجاهل هذه الرسالة.</p>
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    }

    // IMPORTANT: Use the verified domain in the from field
    const data = await resend.emails.send({
      from: "RFOF <<EMAIL>>",
      to: [email],
      subject: subject,
      html: emailContent,
    });

    console.log("Email sent successfully:", data);
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "OTP email sent successfully"
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error sending OTP email:", error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || "Failed to send OTP email"
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
