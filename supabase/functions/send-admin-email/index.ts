
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { name, email, subject, message }: ContactFormData = await req.json();
    
    if (!name || !email || !subject || !message) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { 
          status: 400, 
          headers: { 
            "Content-Type": "application/json",
            ...corsHeaders 
          } 
        }
      );
    }

    // The admin email where contact form submissions will be sent
    const adminEmail = Deno.env.get("ADMIN_EMAIL") || "<EMAIL>";
    
    // IMPORTANT: Updated the from field to use the verified domain
    const emailResponse = await resend.emails.send({
      from: "RFOF نموذج التواصل <<EMAIL>>",
      to: [adminEmail],
      subject: `رسالة جديدة: ${subject}`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">رسالة جديدة من نموذج التواصل</h2>
          
          <div style="margin: 20px 0;">
            <p><strong>الاسم:</strong> ${name}</p>
            <p><strong>البريد الإلكتروني:</strong> ${email}</p>
            <p><strong>الموضوع:</strong> ${subject}</p>
            
            <div style="margin-top: 20px;">
              <p><strong>الرسالة:</strong></p>
              <div style="background: #f9f9f9; padding: 15px; border-radius: 4px; white-space: pre-wrap;">${message}</div>
            </div>
          </div>
          
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>تم إرسال هذه الرسالة من خلال نموذج التواصل في موقع RFOF.</p>
          </div>
        </div>
      `,
      // Also send a plain text version for email clients that don't support HTML
      text: `
        رسالة جديدة من نموذج التواصل
        
        الاسم: ${name}
        البريد الإلكتروني: ${email}
        الموضوع: ${subject}
        
        الرسالة:
        ${message}
        
        تم إرسال هذه الرسالة من خلال نموذج التواصل في موقع RFOF.
      `,
    });

    console.log("Email sent successfully:", emailResponse);

    // Also send a confirmation email to the user
    // IMPORTANT: Updated the from field to use the verified domain
    await resend.emails.send({
      from: "RFOF <<EMAIL>>",
      to: [email],
      subject: "شكراً لتواصلك معنا",
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">شكراً لتواصلك معنا</h2>
          
          <div style="margin: 20px 0;">
            <p>مرحباً ${name}،</p>
            <p>شكراً لتواصلك معنا. لقد استلمنا رسالتك وسنقوم بالرد عليك في أقرب وقت ممكن.</p>
            
            <div style="margin-top: 20px;">
              <p><strong>تفاصيل الرسالة:</strong></p>
              <p><strong>الموضوع:</strong> ${subject}</p>
              <div style="background: #f9f9f9; padding: 15px; border-radius: 4px; white-space: pre-wrap;">${message}</div>
            </div>
          </div>
          
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `,
      text: `
        شكراً لتواصلك معنا
        
        مرحباً ${name}،
        
        شكراً لتواصلك معنا. لقد استلمنا رسالتك وسنقوم بالرد عليك في أقرب وقت ممكن.
        
        تفاصيل الرسالة:
        الموضوع: ${subject}
        ${message}
        
        مع تحيات،
        فريق RFOF
      `,
    });

    return new Response(JSON.stringify(emailResponse), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-admin-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
