
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Parse request body
    const { transaction_id, approved, reason } = await req.json();

    if (!transaction_id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Transaction ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    console.log(`Processing transaction ${transaction_id}, approved: ${approved}`);

    // Get transaction details
    const { data: transaction, error: fetchError } = await supabase
      .from('transactions')
      .select('*')
      .eq('id', transaction_id)
      .single();

    if (fetchError) {
      console.error('Error fetching transaction:', fetchError);
      return new Response(
        JSON.stringify({ success: false, error: 'Transaction not found or could not be accessed' }),
        { status: 404, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    if (transaction.status !== 'pending') {
      return new Response(
        JSON.stringify({ success: false, error: 'Only pending transactions can be approved or rejected' }),
        { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // Start transaction
    const newStatus = approved ? 'completed' : 'rejected';
    
    // Update transaction status
    const { error: updateError } = await supabase
      .from('transactions')
      .update({ 
        status: newStatus, 
        updated_at: new Date().toISOString(),
        description: reason ? `${transaction.description} | ${reason}` : transaction.description
      })
      .eq('id', transaction_id);

    if (updateError) {
      console.error('Error updating transaction:', updateError);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to update transaction status' }),
        { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }

    // If approved and it's a deposit, add to user balance
    if (approved && transaction.type === 'deposit') {
      // Get current user balance
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('balance')
        .eq('id', transaction.user_id)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to fetch user balance' }),
          { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }

      // Update user balance
      const currentBalance = userData.balance || 0;
      const newBalance = currentBalance + parseFloat(transaction.amount);
      
      console.log(`Updating user ${transaction.user_id} balance from ${currentBalance} to ${newBalance}`);

      const { error: balanceError } = await supabase
        .from('profiles')
        .update({ balance: newBalance })
        .eq('id', transaction.user_id);

      if (balanceError) {
        console.error('Error updating balance:', balanceError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to update user balance' }),
          { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
    }
    
    // If approved and it's a withdraw, subtract from user balance
    if (approved && transaction.type === 'withdraw') {
      // Get current user balance
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('balance')
        .eq('id', transaction.user_id)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to fetch user balance' }),
          { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }

      // Check if balance is sufficient
      const currentBalance = userData.balance || 0;
      if (currentBalance < parseFloat(transaction.amount)) {
        return new Response(
          JSON.stringify({ success: false, error: 'Insufficient balance for withdrawal' }),
          { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
      
      // Update user balance - DEDUCT the amount for withdrawals
      const newBalance = currentBalance - parseFloat(transaction.amount);
      
      console.log(`Updating user ${transaction.user_id} balance from ${currentBalance} to ${newBalance} for withdrawal`);

      const { error: balanceError } = await supabase
        .from('profiles')
        .update({ balance: newBalance })
        .eq('id', transaction.user_id);

      if (balanceError) {
        console.error('Error updating balance:', balanceError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to update user balance' }),
          { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
    }

    // Create a notification for the user
    const notificationTitle = approved 
      ? transaction.type === 'deposit' 
        ? 'تمت الموافقة على إيداعك'
        : 'تمت الموافقة على طلب السحب' 
      : transaction.type === 'deposit'
        ? 'تم رفض إيداعك'
        : 'تم رفض طلب السحب';
        
    const notificationMessage = approved 
      ? transaction.type === 'deposit'
        ? `تم اعتماد إيداعك بمبلغ ${transaction.amount} ر.س وإضافته إلى رصيدك`
        : `تم اعتماد طلب السحب بمبلغ ${transaction.amount} ر.س`
      : `تم رفض معاملتك${reason ? `: ${reason}` : ''}`;

    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: transaction.user_id,
        type: approved ? 'success' : 'error',
        title: notificationTitle,
        message: notificationMessage,
        link: '/dashboard/wallet',
      });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Continue execution, notification is not critical
    }

    // Return success
    return new Response(
      JSON.stringify({ success: true, status: newStatus }),
      { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
    );
  }
});
