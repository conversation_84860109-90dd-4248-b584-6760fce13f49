
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@2.0.0";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const resend = new Resend(RESEND_API_KEY);

Deno.serve(async (req) => {
  // Set CORS headers
  const headers = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers,
    });
  }

  try {
    // 1. Validate request method
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({
          error: "Method not allowed",
        }),
        {
          status: 405,
          headers,
        }
      );
    }
    
    // 2. Parse request body
    const { request_id, status } = await req.json();
    if (!request_id || !status) {
      return new Response(
        JSON.stringify({
          error: "Missing required fields",
        }),
        {
          status: 400,
          headers,
        }
      );
    }

    // 3. Fetch request details
    const { data: request, error: requestError } = await supabase
      .from("hosting_requests")
      .select("id, ecommerce_id, ecommerce_name, store_id, store_name")
      .eq("id", request_id)
      .single();
      
    if (requestError || !request) {
      return new Response(
        JSON.stringify({
          error: "Request not found",
        }),
        {
          status: 404,
          headers,
        }
      );
    }

    // 4. Update status
    const { error: updateError } = await supabase
      .from("hosting_requests")
      .update({
        status,
      })
      .eq("id", request_id);
      
    if (updateError) {
      return new Response(
        JSON.stringify({
          error: "Failed to update request",
        }),
        {
          status: 500,
          headers,
        }
      );
    }
    
    // 5. Prepare notification content
    const subject =
      status === "accepted"
        ? "تم قبول طلب استضافة المتجر"
        : "تم رفض طلب استضافة المتجر";
        
    const message =
      status === "accepted"
        ? `مرحباً ${request.ecommerce_name}، تم قبول طلب استضافة المتجر ${request.store_name}.`
        : `مرحباً ${request.ecommerce_name}، نأسف، تم رفض طلب استضافة المتجر ${request.store_name}.`;
    
    // 6. Add notification
    const { error: notificationError } = await supabase
      .from("notifications")
      .insert({
        user_id: request.ecommerce_id,
        title: subject,
        message: message,
        type: status === "accepted" ? "success" : "error",
        link: `/requests/${request_id}`,
      });
      
    if (notificationError) {
      console.error("Notification error:", notificationError);
      // Continue despite notification error
    }
    
    // 7. Get ecommerce user email
    const { data: userData, error: userError } = await supabase
      .from("profiles")
      .select("email")
      .eq("id", request.ecommerce_id)
      .single();
      
    if (!userError && userData?.email) {
      // 8. Send email notification
      try {
        await resend.emails.send({
          from: "RFOF نظام المتاجر <<EMAIL>>",
          to: [userData.email],
          subject: subject,
          html: `
            <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
              <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">${subject}</h2>
              <div style="margin: 20px 0;">
                <p>${message}</p>
                ${status === "accepted" ? 
                  `<p>يمكنك الآن متابعة حالة الطلب والتفاصيل من خلال <a href="${SUPABASE_URL.replace('.supabase.co', '')}/dashboard/hosting-requests/${request_id}">لوحة التحكم</a>.</p>` 
                  : ''}
              </div>
              <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
                <p>مع تحيات فريق RFOF</p>
              </div>
            </div>
          `,
          text: `${subject}\n\n${message}\n\nمع تحيات فريق RFOF`,
        });
        
        console.log("Email sent successfully to ecommerce user");
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
      }
    }
    
    // 9. Get store owner data and send notification email
    const { data: storeData, error: storeError } = await supabase
      .from("stores")
      .select("owner_id")
      .eq("id", request.store_id)
      .single();
      
    if (!storeError && storeData?.owner_id) {
      const { data: storeOwnerData, error: ownerError } = await supabase
        .from("profiles")
        .select("email, name")
        .eq("id", storeData.owner_id)
        .single();
        
      if (!ownerError && storeOwnerData?.email) {
        // Send email to store owner
        const storeSubject = status === "accepted" 
          ? `تم قبول طلب استضافة من ${request.ecommerce_name}`
          : `تم رفض طلب استضافة من ${request.ecommerce_name}`;
          
        const storeMessage = status === "accepted"
          ? `تم قبول طلب استضافة المنتجات من ${request.ecommerce_name} في متجرك ${request.store_name}.`
          : `تم رفض طلب استضافة المنتجات من ${request.ecommerce_name} في متجرك ${request.store_name}.`;
        
        try {
          await resend.emails.send({
            from: "RFOF نظام المتاجر <<EMAIL>>",
            to: [storeOwnerData.email],
            subject: storeSubject,
            html: `
              <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
                <h2 style="color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">${storeSubject}</h2>
                <div style="margin: 20px 0;">
                  <p>${storeMessage}</p>
                  <p>يمكنك متابعة التفاصيل من خلال <a href="${SUPABASE_URL.replace('.supabase.co', '')}/dashboard/hosting-requests/${request_id}">لوحة التحكم</a>.</p>
                </div>
                <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
                  <p>مع تحيات فريق RFOF</p>
                </div>
              </div>
            `,
            text: `${storeSubject}\n\n${storeMessage}\n\nمع تحيات فريق RFOF`,
          });
          
          console.log("Email sent successfully to store owner");
        } catch (emailError) {
          console.error("Failed to send email to store owner:", emailError);
        }
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "Request status updated successfully",
      }),
      {
        headers,
      }
    );
  } catch (error) {
    console.error("Unhandled error:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
      }),
      {
        status: 500,
        headers,
      }
    );
  }
});
