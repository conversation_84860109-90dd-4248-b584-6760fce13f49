import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { v4 as uuidv4 } from "https://esm.sh/uuid@9.0.1";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

const corsHeaders = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

interface TransferRequest {
  fromUserId: string;
  toUserId: string;
  amount: number;
  description: string;
  adminId: string;
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

Deno.serve(async (req) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({ error: "Method not allowed" }),
        { status: 405, headers: corsHeaders }
      );
    }

    const { fromUserId, toUserId, amount, description, adminId } =
      (await req.json()) as TransferRequest;

    if (!fromUserId || !toUserId || !amount || !adminId) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { status: 400, headers: corsHeaders }
      );
    }

    if (amount <= 0) {
      return new Response(
        JSON.stringify({ error: "Amount must be greater than zero" }),
        { status: 400, headers: corsHeaders }
      );
    }

    // Verify admin role
    const { data: adminData, error: adminError } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", adminId)
      .single();

    if (
      adminError ||
      !adminData ||
      (adminData.role !== "admin" && adminData.role !== "sub-admin")
    ) {
      return new Response(
        JSON.stringify({
          error: "Unauthorized: Only admins can perform this operation",
        }),
        { status: 403, headers: corsHeaders }
      );
    }

    // Get source and destination users with balance
    const { data: sourceUser, error: sourceError } = await supabase
      .from("profiles")
      .select("name, email, balance")
      .eq("id", fromUserId)
      .single();
    if (sourceError || !sourceUser) {
      return new Response(
        JSON.stringify({ error: "Source user not found" }),
        { status: 404, headers: corsHeaders }
      );
    }

    const { data: destUser, error: destError } = await supabase
      .from("profiles")
      .select("name, email, balance")
      .eq("id", toUserId)
      .single();
    if (destError || !destUser) {
      return new Response(
        JSON.stringify({ error: "Destination user not found" }),
        { status: 404, headers: corsHeaders }
      );
    }

    if (sourceUser.balance < amount) {
      return new Response(
        JSON.stringify({ error: "Insufficient balance in source account" }),
        { status: 400, headers: corsHeaders }
      );
    }

    const timestamp = new Date().toISOString();
    const referenceNumber = `TRF-${Date.now().toString().slice(-6)}`;

    // Create source transaction (debit) including from, to
    const sourceTransactionId = uuidv4();
    const { error: sourceTransactionError } = await supabase.from("transactions").insert({
      id: sourceTransactionId,
      user_id: fromUserId,
      from_user_id: fromUserId,
      to_user_id: toUserId,
      user_name: sourceUser.name,
      amount: amount,
      type: "transfer",
      status: "completed",
      reference: referenceNumber,
      description: `تحويل إلى ${destUser.name}: ${description}`,
      created_at: timestamp,
      updated_at: timestamp,
    });
    if (sourceTransactionError) {
      return new Response(
        JSON.stringify({
          error: "Failed to create source transaction",
          details: sourceTransactionError.message,
        }),
        { status: 500, headers: corsHeaders }
      );
    }

    // Create destination transaction (credit)
    const destTransactionId = uuidv4();
    const { error: destTransactionError } = await supabase.from("transactions").insert({
      id: destTransactionId,
      user_id: toUserId,
      from_user_id: fromUserId,
      to_user_id: toUserId,
      user_name: destUser.name,
      amount: amount,
      type: "transfer",
      status: "completed",
      reference: referenceNumber,
      description: `تحويل من ${sourceUser.name}: ${description}`,
      created_at: timestamp,
      updated_at: timestamp,
    });
    if (destTransactionError) {
      return new Response(
        JSON.stringify({
          error: "Failed to create destination transaction",
          details: destTransactionError.message,
        }),
        { status: 500, headers: corsHeaders }
      );
    }

    // Update balances
    const { error: sourceUpdateError } = await supabase
      .from("profiles")
      .update({ balance: sourceUser.balance - amount, updated_at: timestamp })
      .eq("id", fromUserId);
    if (sourceUpdateError) {
      return new Response(
        JSON.stringify({ error: "Failed to update source balance", details: sourceUpdateError.message }),
        { status: 500, headers: corsHeaders }
      );
    }
    const { error: destUpdateError } = await supabase
      .from("profiles")
      .update({ balance: destUser.balance + amount, updated_at: timestamp })
      .eq("id", toUserId);
    if (destUpdateError) {
      return new Response(
        JSON.stringify({ error: "Failed to update destination balance", details: destUpdateError.message }),
        { status: 500, headers: corsHeaders }
      );
    }

    // Create in-app notifications
    try {
      await supabase.from("notifications").insert([
        {
          user_id: fromUserId,
          title: `تم خصم مبلغ ${amount} ريال من حسابك`,
          message: `تم خصم مبلغ ${amount} ريال من حسابك. السبب: ${description}. الرقم المرجعي: ${referenceNumber}`,
          type: "info",
          link: "/dashboard/wallet",
        },
        {
          user_id: toUserId,
          title: `تم إضافة مبلغ ${amount} ريال إلى حسابك`,
          message: `تم إضافة مبلغ ${amount} ريال إلى حسابك. السبب: ${description}. الرقم المرجعي: ${referenceNumber}`,
          type: "success",
          link: "/dashboard/wallet",
        },
      ]);
    } catch (notifError) {
      console.error("Error creating in-app notifications:", notifError);
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "Funds transferred successfully",
        reference: referenceNumber,
        sourceTransaction: sourceTransactionId,
        destTransaction: destTransactionId,
      }),
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error("Unhandled error:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error", details: (error as Error).message }),
      { status: 500, headers: corsHeaders }
    );
  }
});
