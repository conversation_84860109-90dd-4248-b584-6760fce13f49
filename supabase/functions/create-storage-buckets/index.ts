
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

serve(async (req) => {
  try {
    // Initialize Supabase Admin Client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    console.log("Creating necessary storage buckets if they don't exist");
    
    // List of buckets we want to ensure exist
    const requiredBuckets = [
      {
        name: 'receipts',
        public: false,
        fileSizeLimit: 5242880, // 5 MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']
      },
      {
        name: 'products',
        public: true,
        fileSizeLimit: 10485760, // 10 MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      },
      {
        name: 'avatars',
        public: true,
        fileSizeLimit: 2097152, // 2 MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      }
    ];

    // Get existing buckets
    const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error("Error listing storage buckets:", listError);
      return new Response(
        JSON.stringify({ success: false, error: listError.message }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const existingBucketNames = existingBuckets?.map(bucket => bucket.name) || [];
    const bucketsToCreate = requiredBuckets.filter(bucket => !existingBucketNames.includes(bucket.name));
    const results = [];

    // Create missing buckets
    for (const bucket of bucketsToCreate) {
      try {
        const { data, error } = await supabase.storage.createBucket(
          bucket.name,
          { 
            public: bucket.public,
            fileSizeLimit: bucket.fileSizeLimit,
            allowedMimeTypes: bucket.allowedMimeTypes
          }
        );
        
        if (error) {
          console.error(`Error creating bucket ${bucket.name}:`, error);
          results.push({ name: bucket.name, success: false, error: error.message });
        } else {
          console.log(`Successfully created bucket: ${bucket.name}`);
          results.push({ name: bucket.name, success: true });
        }
      } catch (err) {
        console.error(`Exception creating bucket ${bucket.name}:`, err);
        results.push({ name: bucket.name, success: false, error: err.message });
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Storage bucket verification complete", 
        existingBuckets: existingBucketNames,
        results 
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error("Unexpected error:", error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});
