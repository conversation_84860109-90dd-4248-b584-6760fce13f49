
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@2.0.0";

serve(async (req) => {
  try {
    // Set up CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    };
    
    // Handle OPTIONS request for CORS
    if (req.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    // Parse the request body
    const requestData = await req.json();
    const { transactionId, notifyUser = true } = requestData;

    if (!transactionId) {
      return new Response(
        JSON.stringify({ success: false, error: 'Transaction ID is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      );
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Create Resend client for email notifications
    const resend = new Resend(Deno.env.get('RESEND_API_KEY') || '');

    // Get transaction details
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .select(`
        id, 
        amount, 
        status,
        created_at,
        user_id,
        receipt_url,
        notes,
        reference,
        profiles:user_id (name, email)
      `)
      .eq('id', transactionId)
      .single();

    if (transactionError || !transaction) {
      console.error('Error fetching transaction:', transactionError);
      return new Response(
        JSON.stringify({ success: false, error: 'Transaction not found' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
      );
    }

    // Get admin email from environment variable or use a default
    const adminEmail = Deno.env.get('ADMIN_EMAIL') || '<EMAIL>';
    
    // Create email content
    const adminEmailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>إشعار إيداع جديد</h2>
        <p>تم إجراء إيداع جديد في النظام:</p>
        <ul>
          <li><strong>المعرف:</strong> ${transaction.id}</li>
          <li><strong>المبلغ:</strong> ${transaction.amount} ريال سعودي</li>
          <li><strong>الحالة:</strong> ${transaction.status}</li>
          <li><strong>تاريخ الإنشاء:</strong> ${new Date(transaction.created_at).toLocaleString('ar-SA')}</li>
          <li><strong>المستخدم:</strong> ${transaction.profiles?.name || 'غير معروف'}</li>
          <li><strong>البريد الإلكتروني:</strong> ${transaction.profiles?.email || 'غير معروف'}</li>
          ${transaction.notes ? `<li><strong>ملاحظات:</strong> ${transaction.notes}</li>` : ''}
          ${transaction.reference ? `<li><strong>المرجع:</strong> ${transaction.reference}</li>` : ''}
        </ul>
        ${transaction.receipt_url ? `<p><a href="${transaction.receipt_url}" target="_blank">عرض الإيصال</a></p>` : ''}
        <p>يرجى مراجعة هذا الإيداع والموافقة عليه من خلال لوحة التحكم.</p>
      </div>
    `;
    
    // Send notification to admin
    const adminEmailResponse = await resend.emails.send({
      from: 'RFOF إشعارات <<EMAIL>>',
      to: [adminEmail],
      subject: `إيداع جديد - ${transaction.id}`,
      html: adminEmailContent,
    });

    // Send notification to user if requested
    let userEmailResponse = null;
    if (notifyUser && transaction.profiles?.email) {
      const userEmailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>تأكيد طلب الإيداع</h2>
          <p>عزيزي ${transaction.profiles.name || 'المستخدم'}،</p>
          <p>تم استلام طلب الإيداع الخاص بك بنجاح وهو قيد المراجعة من قبل فريقنا.</p>
          <p>تفاصيل الطلب:</p>
          <ul>
            <li><strong>المعرف:</strong> ${transaction.id}</li>
            <li><strong>المبلغ:</strong> ${transaction.amount} ريال سعودي</li>
            <li><strong>تاريخ الطلب:</strong> ${new Date(transaction.created_at).toLocaleString('ar-SA')}</li>
            ${transaction.reference ? `<li><strong>المرجع:</strong> ${transaction.reference}</li>` : ''}
          </ul>
          <p>سيتم إعلامك عند اكتمال المراجعة وتحديث حالة الإيداع.</p>
          <p>مع خالص التقدير،<br>فريق RFOF</p>
        </div>
      `;
      
      userEmailResponse = await resend.emails.send({
        from: 'RFOF إشعارات المحفظة <<EMAIL>>',
        to: [transaction.profiles.email],
        subject: 'تأكيد استلام طلب الإيداع',
        html: userEmailContent,
      });
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        adminEmailResponse, 
        userEmailResponse 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    );
  } catch (error) {
    console.error("Error in deposit notification:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message || 'Unknown error' }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});
