
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { email, firstName, type = "register", token } = await req.json();
    
    if (!email) {
      return new Response(
        JSON.stringify({ error: "Email is required" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
    
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY") || "";
    const resendApiKey = Deno.env.get("RESEND_API_KEY") || "";
    
    if (!supabaseUrl || !supabaseAnonKey || !resendApiKey) {
      throw new Error("Missing required environment variables");
    }
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    const resend = new Resend(resendApiKey);
    
    // Generate verification URL with token if provided
    const baseUrl = req.headers.get("origin") || "https://rfof.sa";
    let verificationUrl = `${baseUrl}/auth/verify`;
    
    if (token) {
      verificationUrl = `${verificationUrl}?token=${token}&email=${encodeURIComponent(email)}`;
    }
    
    // Customize the email content based on the type
    let subject = "";
    let emailContent = "";
    const name = firstName || "عزيزي المستخدم";
    
    if (type === "register") {
      subject = "تأكيد حسابك في RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333;">مرحباً ${name}،</h2>
          <p>شكراً لتسجيلك في RFOF. يرجى تأكيد بريدك الإلكتروني للاستمرار.</p>
          
          <div style="margin: 30px 0; text-align: center;">
            <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">تأكيد البريد الإلكتروني</a>
          </div>
          
          <p>أو يمكنك نسخ الرابط التالي ولصقه في متصفحك:</p>
          <p style="background-color: #f9f9f9; border: 1px solid #ddd; padding: 10px; border-radius: 4px; word-break: break-all;">
            ${verificationUrl}
          </p>
          
          <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذه الرسالة.</p>
          
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    } else if (type === "welcome") {
      subject = "مرحباً بك في RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333;">مرحباً ${name}،</h2>
          <p>شكراً لانضمامك إلى RFOF. نحن سعداء بوجودك معنا!</p>
          <p>يمكنك الآن الاستفادة من جميع خدماتنا:</p>
          
          <ul style="list-style-type: none; padding: 0;">
            <li style="margin: 10px 0; padding-right: 20px; position: relative;">✓ تصفح وشراء المنتجات</li>
            <li style="margin: 10px 0; padding-right: 20px; position: relative;">✓ إدارة حسابك بسهولة</li>
            <li style="margin: 10px 0; padding-right: 20px; position: relative;">✓ متابعة طلباتك</li>
          </ul>
          
          <div style="margin: 30px 0; text-align: center;">
            <a href="${baseUrl}/login" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">تسجيل الدخول الآن</a>
          </div>
          
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    } else if (type === "reset_password") {
      subject = "إعادة تعيين كلمة المرور في RFOF";
      emailContent = `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333;">مرحباً ${name}،</h2>
          <p>لقد تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في RFOF.</p>
          
          <div style="margin: 30px 0; text-align: center;">
            <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">إعادة تعيين كلمة المرور</a>
          </div>
          
          <p>أو يمكنك نسخ الرابط التالي ولصقه في متصفحك:</p>
          <p style="background-color: #f9f9f9; border: 1px solid #ddd; padding: 10px; border-radius: 4px; word-break: break-all;">
            ${verificationUrl}
          </p>
          
          <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.</p>
          
          <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 10px;">
            <p>مع تحيات،</p>
            <p>فريق RFOF</p>
          </div>
        </div>
      `;
    }
    
    // IMPORTANT: Use the verified domain in the from field
    const { data, error } = await resend.emails.send({
      from: "RFOF <<EMAIL>>",
      to: [email],
      subject: subject,
      html: emailContent,
    });
    
    if (error) {
      console.error("Error sending email:", error);
      throw error;
    }
    
    console.log("Email sent successfully:", data);
    
    return new Response(
      JSON.stringify({ success: true, message: "Email sent successfully" }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
    
  } catch (error) {
    console.error("Error in send-confirmation-email function:", error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || "Failed to send email" 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
