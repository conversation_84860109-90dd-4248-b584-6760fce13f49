{
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "noImplicitAny": false,
    "noUnusedParameters": false,
    "skipLibCheck": true,
    "allowJs": true,
    "noUnusedLocals": false,
    "strictNullChecks": false,
    "target": "ESNext",
    //"lib": ["deno.ns", "deno.window", "ESNext"],
    "strict": true,
    "esModuleInterop": true,
    "moduleResolution": "nodenext"
  }
}
