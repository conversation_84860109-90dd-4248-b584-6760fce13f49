
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 158 72% 49%;
    --primary-foreground: 210 40% 98%;

    --secondary: 200 55% 35%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 158 72% 49%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* RGB color values for gradients and overlays */
    --rfof-green-rgb: 39, 214, 125;
    --rfof-blue-rgb: 31, 107, 147;
    --rfof-dark-blue-rgb: 2, 28, 51;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 158 72% 49%;
    --primary-foreground: 0 0% 0%;

    --secondary: 200 55% 35%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 158 72% 49%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Noto Sans Arabic', sans-serif;
  }
}

/* Add Noto Sans Arabic font */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Wood texture for shelves */
.bg-wood-texture {
  background-color: #8B5A2B;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='20' viewBox='0 0 100 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.184 20c.357-.13.72-.264.888-.14.56.41 1.012.18 1.68.14.145-.1.143.41.143.41l1.046-.14 1.258.14.895-.28 1.08-.14.485.14h1.154c.376-.18.656-.28 1.013-.14.127.1.176.41.176.41s.338 0 .467-.14c.344-.14.694-.42 1.18-.14.595 0 1.022.42 1.464.14v-.28l.283-.14c.683.28 1.31-.14 1.567-.14.613 0 .894.41 1.464.14v-.28l.42-.14.148.28h1.195l.327-.28.624-.14.89.28.668-.14h1.232c.31.14.06.14.447.14.387 0 .793-.14.793-.14l.714.14h2.215l1.51-.28.368.28h1.195l.327-.28.624-.14v.14h.658l1.452-.28c.194.14.387 0 .77-.14.92-.42 1.525 0 2.21-.28v.28l.786-.14 1.605.42c.522 0 .92.28 1.268.14.524-.14.968-.28 1.678-.28.91 0 2.304.14 3.39-.14l.283.28h1.37c.43 0 .887-.14 1.278-.14.597 0 1.165.14 1.74.14h.787l.275-.14h.422l.133.28h.275l1.463-.28c.29 0 .572.28.716.14l.89-.14v.28c.443 0 .886-.14 1.56-.14.56 0 1.1.14 1.464.14l1.037-.28 2.304-.14 1.344.28h.715c.905-.14 1.153-.14 1.432-.14.42 0 .835.14 1.238.14l.97-.28.283.28h1.546c.602-.42 1.307-.14 2.62-.28l.975.28H76.56v-.28l.412-.14.265.28 1.24-.14.496.14h.44c.55-.14 1.11-.14 1.948-.14.53 0 .743-.14 1.075.14.795.28 1.356-.14 1.976-.14h.894c.06 0 .404.14.5.14.288 0 .677-.14.828-.14.385 0 .702-.28 1.048-.28.875 0 1.674.14 2.34.14.398 0 .778.14 1.183.14.83 0 1.8-.28 2.833-.28.697 0 1.014.28 1.442.28.374 0 .702-.42 1.183-.28z' fill='%237d553a' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* Animation utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out forwards;
  }
  
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }
  
  .animate-bounce-slow {
    animation: bounce 3s ease-in-out infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Add glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl;
}

/* Section styles */
.section-title {
  @apply text-3xl md:text-4xl font-bold mb-6 text-rfof-darkBlue;
}

.section-description {
  @apply text-lg text-gray-600 max-w-3xl mx-auto mb-12;
}

/* Responsive styles */
@media (max-width: 768px) {
  .section-title {
    @apply text-2xl;
  }
  .section-description {
    @apply text-base;
  }
}
