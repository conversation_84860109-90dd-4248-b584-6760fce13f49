import React, { lazy, Suspense, useEffect } from "react";
import { Navigate, Outlet, Route, Routes } from "react-router-dom";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AuthProvider, useAuth } from "@/contexts/auth";
import { BranchProvider } from "@/contexts/BranchContext";
import { FinanceProvider } from "@/contexts/FinanceContext";
import { HostingProvider } from "@/contexts/HostingContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { Toaster } from "sonner";
import ScrollToTop from "@/components/ScrollToTop";

// Import your pages here
import Home from "@/pages/Home";
// Use pages that exist in the project and update paths
import Login from "@/pages/auth/Login";
import Register from "@/pages/auth/Register";
import Dashboard from "@/pages/dashboard/Dashboard";
import Products from "@/pages/dashboard/Products";
import AddProduct from "@/pages/dashboard/AddProduct";
import StoreInventory from "@/pages/dashboard/StoreInventory";
import Profile from "@/pages/dashboard/Profile";
import Settings from "@/pages/dashboard/Settings";
import Wallet from "@/pages/dashboard/Wallet";
import Branches from "@/pages/dashboard/Branches";
import Employees from "@/pages/dashboard/Employees";
import HostingRequests from "@/pages/dashboard/HostingRequests";
import NewHostingRequest from "@/pages/dashboard/NewHostingRequest";
import HostingRequestDetail from "@/pages/dashboard/HostingRequestDetail";
import Orders from "@/pages/dashboard/Orders";
import Stores from "@/pages/dashboard/Stores";
import Invoices from "@/pages/dashboard/Invoices";
import NotFound from "@/pages/NotFound";

// Admin Pages
import AdminDashboard from "@/pages/admin/Dashboard";
import AdminUsers from "@/pages/admin/Users";
import AdminProducts from "@/pages/admin/Products";
import AdminStores from "@/pages/admin/Stores";
import AdminHostingRequests from "@/pages/admin/HostingRequests";
import AdminLogin from "@/pages/auth/AdminLogin";
import VerificationHandler from "@/pages/auth/VerificationHandler";
import UserManagement from "@/pages/admin/UserManagement";
import ProductManagement from "@/pages/admin/ProductManagement";
import Terms from "./pages/Terms";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import Notifications from "./pages/dashboard/Notifications";
import LegalDetails from "./pages/dashboard/LegalDetails";
import Contact from "./pages/Contact";
import About from "./pages/About";
import OTPVerificationPage from "./pages/auth/OTPVerification";
import ResetPass from "./pages/auth/ResetPass";
import AdminDeposits from "./pages/admin/Deposits";
import AdminWithdrawals from "./pages/admin/Withdrawals";
import ProductDetails from "./pages/dashboard/ProductDetails";
import { ProductProvider } from "@/contexts/ProductContext.tsx";
import { addProfileActiveColumn } from "./migrations/addProfileActiveColumn";
import { supabase } from "./integrations/supabase/client";
import AddStoreData from "./pages/dashboard/AddStoreData";
import Deposits from "./pages/dashboard/Deposits";
import Withdrawals from "./pages/dashboard/Withdrawals";
import Transactions from "./pages/admin/Transactions";
import MySpaces from "./pages/MySpaces";
import Onboarding from "./pages/Onboarding";

// Lazy-loaded components
const AdminManagement = lazy(() => import("./pages/admin/AdminManagement"));

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="hadka-ui-theme">
      <>
        <ProductProvider>
          <BranchProvider>
            <FinanceProvider>
              <HostingProvider>
                <NotificationProvider>
                  <ScrollToTop />
                  <Routes>
                    {/* صفحات عامة */}
                    <Route path="/" element={<Home />} />
                    <Route path="/login" element={<Login />} />
                    <Route
                      path="/auth/login"
                      element={<Navigate to="/login" replace />}
                    />
                    <Route path="/register" element={<Register />} />
                    <Route
                      path="/auth/register"
                      element={<Navigate to="/register" replace />}
                    />
                    <Route path="/admin-login" element={<AdminLogin />} />
                    <Route
                      path="/otp-verification"
                      element={<OTPVerificationPage />}
                    />
                    <Route path="/reset-password" element={<ResetPass />} />
                    <Route path="/verify" element={<VerificationHandler />} />
                    <Route path="/terms" element={<Terms />} />
                    {/* <Route path="/contact" element={<Contact />} /> */}
                    <Route path="/about" element={<About />} />
                    <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                    <Route path="/onboarding" element={<Onboarding />} />
                    <Route path="/dashboard/stores" element={<Stores />} />
                    <Route path="/dashboard/invoices" element={<Invoices />} />

                    {/* مسارات لوحة التحكم */}

                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/dashboard/myspaces" element={<MySpaces />} />

                    <Route path="/dashboard/products" element={<Products />} />
                    <Route
                      path="/dashboard/products/:id"
                      element={<ProductDetails />}
                    />
                    <Route
                      path="/dashboard/products/edit/:id"
                      element={<AddProduct />}
                    />
                    <Route
                      path="/dashboard/products/add"
                      element={<AddProduct />}
                    />
                    <Route
                      path="/dashboard/store-inventory"
                      element={<StoreInventory />}
                    />
                    <Route path="/dashboard/profile" element={<Profile />} />
                    <Route path="/dashboard/settings" element={<Settings />} />
                    <Route path="/dashboard/wallet" element={<Wallet />} />
                    <Route
                      path="/dashboard/wallet/deposits"
                      element={<Deposits />}
                    />
                    <Route
                      path="/dashboard/wallet/withdrawals"
                      element={<Withdrawals />}
                    />
                    {/* <Route path="/dashboard/branches" element={<Branches />} /> */}
                    <Route
                      path="/dashboard/store-data"
                      element={<AddStoreData />}
                    />

                    {/* <Route
                      path="/dashboard/employees"
                      element={<Employees />}
                    /> */}
                    <Route path="/dashboard/invoices" element={<Invoices />} />
                    <Route
                      path="/dashboard/hosting-requests"
                      element={<HostingRequests />}
                    />
                    <Route
                      path="/dashboard/hosting-requests/new"
                      element={<NewHostingRequest />}
                    />
                    <Route
                      path="/dashboard/hosting-requests/:id"
                      element={<HostingRequestDetail />}
                    />
                    <Route path="/dashboard/orders" element={<Orders />} />
                    <Route
                      path="/dashboard/notifications"
                      element={<Notifications />}
                    />
                    <Route
                      path="/dashboard/legal-details"
                      element={<LegalDetails />}
                    />

                    {/* مسارات لوحة تحكم الإدارة */}
                    <Route
                      path="/dashboard/admin"
                      element={<AdminDashboard />}
                    />
                    <Route
                      path="/dashboard/admin/users"
                      element={<AdminUsers />}
                    />
                    <Route
                      path="/dashboard/admin/products"
                      element={<AdminProducts />}
                    />
                    <Route
                      path="/dashboard/admin/stores"
                      element={<AdminStores />}
                    />
                    <Route
                      path="/dashboard/admin/hosting-requests"
                      element={<AdminHostingRequests />}
                    />
                    <Route
                      path="/dashboard/admin/user-management"
                      element={<UserManagement />}
                    />
                    <Route
                      path="/dashboard/admin/product-management"
                      element={<ProductManagement />}
                    />
                    <Route
                      path="/dashboard/admin/transactions"
                      element={<Transactions />}
                    />
                    <Route
                      path="/dashboard/admin/transactions/deposits"
                      element={<AdminDeposits />}
                    />
                    <Route
                      path="/dashboard/admin/transactions/withdrawals"
                      element={<AdminWithdrawals />}
                    />
                    <Route
                      path="/dashboard/admin/admins"
                      element={
                        <Suspense fallback={<div>Loading...</div>}>
                          <AdminManagement />
                        </Suspense>
                      }
                    />

                    {/* صفحة 404 */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  <Toaster
                    position="top-left"
                    expand={true}
                    closeButton={true}
                    duration={1500}
                  />
                </NotificationProvider>
              </HostingProvider>
            </FinanceProvider>
          </BranchProvider>
        </ProductProvider>
      </>
    </ThemeProvider>
  );
}

export default App;
