import React from "react";
import { MainLayout } from "@/layouts/MainLayout";
import { HeroSection } from "@/components/home/<USER>";
import { FeaturesSection } from "@/components/home/<USER>";
import { HowItWorksSection } from "@/components/home/<USER>";
import { CallToAction } from "@/components/home/<USER>";
import { Footer } from "@/components/home/<USER>";

export default function Home() {
  // Scroll to top on page load
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <MainLayout>
      <div className="flex flex-col min-h-screen">
        <HeroSection />
        <FeaturesSection />
        <HowItWorksSection />
        <CallToAction />
        <Footer />
      </div>
    </MainLayout>
  );
}
