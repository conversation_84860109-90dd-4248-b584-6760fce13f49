
import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Transaction, TransactionStatus } from "@/types";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAdminTransactionOperations } from "@/hooks/useAdminTransactionOperations";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { Loading } from "@/components/ui/loading";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Check, X } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  formatCurrency,
  getTransactionStatusText,
} from "@/lib/transactionUtils";

export default function AdminWithdrawals() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [withdrawals, setWithdrawals] = useState<Transaction[]>([]);
  const { approveTransaction, rejectTransaction } = useAdminTransactionOperations();
  const [selectedWithdrawal, setSelectedWithdrawal] =
    useState<Transaction | null>(null);
  const [rejectReason, setRejectReason] = useState("");
  const [confirmApproveDialogOpen, setConfirmApproveDialogOpen] =
    useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [userDetails, setUserDetails] = useState<Record<string, any>>({});

  // Make sure admin user
  useEffect(() => {
    if (user && user.role !== "admin" && user.role !== "sub-admin") {
      navigate("/dashboard");
    }
  }, [user, navigate]);

  // Fetch withdrawals
  useEffect(() => {
    const fetchWithdrawals = async () => {
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from("transactions")
          .select("*")
          .eq("type", "withdraw")
          .order("created_at", { ascending: false });

        if (error) throw error;

        setWithdrawals(data as Transaction[]);

        // Fetch user details for all withdrawal requests
        if (data && data.length > 0) {
          const userIds = [...new Set(data.map((w) => w.user_id))];
          const { data: profilesData, error: profilesError } = await supabase
            .from("profiles")
            .select("id, bank_account, name")
            .in("id", userIds);

          if (profilesError) throw profilesError;

          const usersMap = profilesData.reduce((acc, profile) => {
            acc[profile.id] = profile;
            return acc;
          }, {} as Record<string, any>);

          setUserDetails(usersMap);
        }
      } catch (error) {
        console.error("Error fetching withdrawals:", error);
        toast.error("حدث خطأ أثناء جلب طلبات السحب");
      } finally {
        setIsLoading(false);
      }
    };

    fetchWithdrawals();
  }, []);

  // Handle withdrawal approval
  const handleApproveWithdrawal = async () => {
    if (!selectedWithdrawal) return;

    try {
      const result = await approveTransaction(selectedWithdrawal.id);
      if (result) {
        setWithdrawals((prevState) =>
          prevState.map((w) =>
            w.id === selectedWithdrawal.id
              ? { ...w, status: "completed" as TransactionStatus }
              : w
          )
        );
        toast.success("تم اعتماد طلب السحب بنجاح");
        setConfirmApproveDialogOpen(false);
      }
    } catch (error) {
      console.error("Error approving withdrawal:", error);
    }
  };

  // Handle withdrawal rejection
  const handleRejectWithdrawal = async () => {
    if (!selectedWithdrawal) return;

    try {
      const result = await rejectTransaction({ 
        id: selectedWithdrawal.id, 
        reason: rejectReason 
      });
      
      if (result) {
        setWithdrawals((prevState) =>
          prevState.map((w) =>
            w.id === selectedWithdrawal.id
              ? { ...w, status: "rejected" as TransactionStatus }
              : w
          )
        );
        toast.success("تم رفض طلب السحب بنجاح");
        setRejectDialogOpen(false);
        setRejectReason("");
      }
    } catch (error) {
      console.error("Error rejecting withdrawal:", error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("ar-SA", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return dateString;
    }
  };

  const getStatusBadgeColor = (status: TransactionStatus) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6">
        <PageHeader
          title="طلبات السحب"
          description="إدارة ومراجعة طلبات سحب الأموال من المستخدمين"
        />
      </div>

      <div className="mb-4">
        <Button
          variant="ghost"
          onClick={() => navigate("/dashboard/admin/transactions")}
        >
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة إلى جميع المعاملات
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>طلبات السحب</CardTitle>
          <CardDescription>
            قائمة بجميع طلبات سحب الأموال المقدمة من المستخدمين
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>التاريخ</TableHead>
                <TableHead>المستخدم</TableHead>
                <TableHead>المبلغ</TableHead>
                <TableHead>المرجع</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>الحساب البنكي</TableHead>
                <TableHead>إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {withdrawals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-6">
                    لا توجد طلبات سحب حالياً
                  </TableCell>
                </TableRow>
              ) : (
                withdrawals.map((withdrawal) => (
                  <TableRow key={withdrawal.id}>
                    <TableCell className="whitespace-nowrap">
                      {formatDate(withdrawal.created_at)}
                    </TableCell>
                    <TableCell>{withdrawal.user_name}</TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(withdrawal.amount)}
                    </TableCell>
                    <TableCell>{withdrawal.reference || "-"}</TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(withdrawal.status)}>
                        {getTransactionStatusText(withdrawal.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {userDetails[withdrawal.user_id]?.bank_account ||
                        "غير محدد"}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2 space-x-reverse">
                        {withdrawal.status === "pending" && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-green-600 hover:text-green-700"
                              onClick={() => {
                                setSelectedWithdrawal(withdrawal);
                                setConfirmApproveDialogOpen(true);
                              }}
                            >
                              <Check className="h-4 w-4 ml-1" />
                              موافقة
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => {
                                setSelectedWithdrawal(withdrawal);
                                setRejectDialogOpen(true);
                              }}
                            >
                              <X className="h-4 w-4 ml-1" />
                              رفض
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Approve Dialog */}
      <Dialog
        open={confirmApproveDialogOpen}
        onOpenChange={setConfirmApproveDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الموافقة على السحب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من الموافقة على سحب مبلغ {selectedWithdrawal?.amount}{" "}
              ريال للمستخدم {selectedWithdrawal?.user_name}؟
              <br />
              يرجى التأكد من أنك قد قمت بالتحويل إلى حساب المستخدم قبل الموافقة.
              <br />
              الحساب البنكي:{" "}
              {selectedWithdrawal?.user_id
                ? userDetails[selectedWithdrawal.user_id]?.bank_account ||
                  "غير محدد"
                : "غير محدد"}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmApproveDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleApproveWithdrawal}
              className="bg-green-600 hover:bg-green-700"
            >
              تأكيد الموافقة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>رفض طلب السحب</DialogTitle>
            <DialogDescription>
              الرجاء إدخال سبب رفض طلب سحب مبلغ {selectedWithdrawal?.amount}{" "}
              ريال المقدم من {selectedWithdrawal?.user_name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="سبب الرفض (اختياري)"
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRejectDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={handleRejectWithdrawal} variant="destructive">
              تأكيد الرفض
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
