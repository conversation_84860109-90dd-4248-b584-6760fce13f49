import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { DashboardStat } from "@/components/ui/dashboard-stat";
import { PageHeader } from "@/components/ui/page-header";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import {
  LayoutDashboard,
  ShoppingBag,
  Users,
  Layers,
  Store,
  CreditCard,
} from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { useAuthRole } from "@/hooks/useAuthRole";
import { supabase } from "@/integrations/supabase/client";
import { useAdminStats } from "@/utils/admin/useAdminDashboardStats";
import { useUsers } from "@/hooks/useUsers";

export default function AdminDashboard() {
  const { user } = useAuth();
  const { hasAccess } = useAuthRole(["admin", "sub-admin"]);
  const [stats, setStats] = useState({
    users: 0,
    stores: 0,
    products: 0,
    transactions: 0,
  });
  const { data, isLoading: statsLoding, refetch } = useAdminStats();
  const [isLoading, setIsLoading] = useState(statsLoding);

  useEffect(() => {
    if (hasAccess) {
      refetch().then((res) => {
        if (res.data) {
          setStats(res.data);
          setIsLoading(false);
        } else {
          setIsLoading(true);
        }
      });
    }
  }, [hasAccess, refetch]);

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <LayoutDashboard className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">وصول مرفوض</h2>
          <p className="text-muted-foreground">
            ليس لديك صلاحية للوصول إلى هذه الصفحة
          </p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="لوحة تحكم الإدارة"
        description={`مرحبًا ${user?.name || "بك"} في لوحة تحكم الإدارة`}
        icon={LayoutDashboard}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <DashboardStat
          title="المستخدمين"
          value={isLoading ? "..." : String(stats.users)}
          icon={Users}
          description="إجمالي المستخدمين"
          trend="up"
          isLoading={isLoading}
        />

        <DashboardStat
          title="المتاجر"
          value={isLoading ? "..." : String(stats.stores)}
          icon={Store}
          description="إجمالي المتاجر"
          trend="up"
          isLoading={isLoading}
        />

        <DashboardStat
          title="المنتجات"
          value={isLoading ? "..." : String(stats.products)}
          icon={ShoppingBag}
          description="إجمالي المنتجات"
          trend="up"
          isLoading={isLoading}
        />

        <DashboardStat
          title="العمليات المالية"
          value={isLoading ? "..." : String(stats.transactions)}
          icon={CreditCard}
          description="إجمالي المعاملات"
          trend="stable"
          isLoading={isLoading}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentUsers />
        <RecentTransactions />
      </div>
    </DashboardLayout>
  );
}

// مكون لعرض أحدث المستخدمين
function RecentUsers() {
  const { users, isLoading, error } = useUsers();

  return (
    <Card className="p-5">
      <h3 className="text-lg font-semibold mb-4">أحدث المستخدمين</h3>
      {isLoading ? (
        <p className="text-center py-4">جاري التحميل...</p>
      ) : users.length === 0 ? (
        <p className="text-gray-500 text-center py-4">
          لا يوجد مستخدمين حديثين
        </p>
      ) : (
        <div className="space-y-4">
          {users.slice(5).map((user) => (
            <div
              key={user.id}
              className="flex items-center justify-between border-b pb-2"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                  {user.name?.substring(0, 2) || "??"}
                </div>
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-xs text-muted-foreground">{user.email}</p>
                </div>
              </div>
              <div className="text-xs bg-gray-100 px-2 py-1 rounded">
                {user.role === "admin"
                  ? "مدير"
                  : user.role === "sub-admin"
                  ? "مدير فرعي"
                  : user.role === "store"
                  ? "متجر تقليدي"
                  : user.role === "ecommerce"
                  ? "متجر إلكتروني"
                  : "مستخدم"}
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
}

// مكون لعرض أحدث المعاملات المالية
function RecentTransactions() {
  const [transactions, setTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRecentTransactions = async () => {
      try {
        const { data, error } = await supabase
          .from("transactions")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(5);

        if (error) throw error;
        setTransactions(data || []);
      } catch (error) {
        console.error("Error fetching recent transactions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentTransactions();
  }, []);

  return (
    <Card className="p-5">
      <h3 className="text-lg font-semibold mb-4">أحدث العمليات المالية</h3>
      {isLoading ? (
        <p className="text-center py-4">جاري التحميل...</p>
      ) : transactions.length === 0 ? (
        <p className="text-gray-500 text-center py-4">لا توجد معاملات حديثة</p>
      ) : (
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between border-b pb-2"
            >
              <div>
                <p className="font-medium">{transaction.user_name}</p>
                <p className="text-xs text-muted-foreground">
                  {transaction.description}
                </p>
              </div>
              <div
                className={`font-medium ${
                  transaction.type === "deposit"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {transaction.type === "deposit" ? "+" : "-"}{" "}
                {transaction.amount} ر.س
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
}
