
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useAuthRole } from '@/hooks/useAuthRole';
import { ShoppingBag, Search, Filter, Shield } from 'lucide-react';
import { toast } from 'sonner';
import ProductManagementTable from '@/components/admin/ProductManagementTable';
import { useProductManagement } from '@/hooks/admin/useProductManagement';

const ProductManagement = () => {
  const { hasAccess, isAdmin, isSubAdmin } = useAuthRole(['admin', 'sub-admin']);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStore, setFilterStore] = useState<string | 'all'>('all');
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    products,
    stores,
    isLoading,
    error,
    search,
    setSearch,
    storeFilter,
    setStoreFilter,
    refreshProducts,
    updateProduct,
    deleteProduct,
    approveProduct,
    rejectProduct
  } = useProductManagement({
    initialPage: 1,
    pageSize: 50,
    initialSearch: '',
    initialStoreFilter: 'all',
    autoFetch: true,
  });

  // التحقق من الخطأ
  useEffect(() => {
    if (error) {
      toast.error('خطأ في تحميل بيانات المنتجات', { description: error });
    }
  }, [error]);

  // البحث والتصفية
  const handleSearch = () => {
    setSearch(searchTerm);
  };

  const handleFilter = (store: string | 'all') => {
    setFilterStore(store);
    setStoreFilter(store);
  };

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Shield className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">وصول مرفوض</h2>
          <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة المنتجات"
        description="عرض وإدارة جميع المنتجات في النظام"
        icon={ShoppingBag}
      />

      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث عن اسم المنتج..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 items-center">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={filterStore} onValueChange={(value) => handleFilter(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="تصفية حسب المتجر" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المتاجر</SelectItem>
                  {stores?.map((store) => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleSearch}>بحث</Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span>المنتجات</span>
            <Badge className="bg-secondary text-secondary-foreground">
              {products?.length || 0}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ProductManagementTable
            products={products || []}
            isLoading={isLoading}
            onUpdateProduct={updateProduct}
            onDeleteProduct={deleteProduct}
            onApproveProduct={approveProduct}
            onRejectProduct={rejectProduct}
            currentUserRole={isAdmin ? 'admin' : isSubAdmin ? 'sub-admin' : undefined}
          />
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default ProductManagement;
