import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TransactionsList } from "@/components/TransactionsList";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { Loading } from "@/components/ui/loading";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLef<PERSON>, Filter, ArrowRightLeft } from "lucide-react";
import {
  Select,
  <PERSON>Content,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTransactions } from "@/utils/admin/useTransactions";
import ErrorMessage from "@/components/ui/error-message";
import AdminFundsTransfer from "@/components/admin/AdminFundsTransfer";

export default function Transactions() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);

  // Use our custom hook
  const {
    filteredTransactions,
    isLoading,
    isError,
    filters,
    updateFilter,
    resetFilters,
    handleApproveTransaction,
    handleRejectTransaction,
    isApproving,
    isRejecting,
    refetch,
  } = useTransactions();

  // Make sure admin user
  useEffect(() => {
    if (user && user.role !== "admin" && user.role !== "sub-admin") {
      navigate("/dashboard");
    }
  }, [user, navigate]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Update filters based on tab
    if (value !== "all") {
      if (["deposits", "withdrawals"].includes(value)) {
        updateFilter("type", value === "deposits" ? "deposit" : "withdraw");
        updateFilter("status", "all");
      } else {
        updateFilter("status", value);
        updateFilter("type", "all");
      }
    } else {
      // Reset type and status filters when "all" tab is selected
      updateFilter("type", "all");
      updateFilter("status", "all");
    }
  };

  if (isError) {
    return (
      <DashboardLayout>
        <ErrorMessage
          message="حدث خطأ أثناء تحميل البيانات"
          onRetry={() => refetch()}
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6 flex-wrap">
        <PageHeader
          title="المعاملات المالية"
          description="إدارة ومراقبة كافة المعاملات المالية في النظام"
        />

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setFilterDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            <span>فلترة النتائج</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => resetFilters()}
            className="hidden md:flex"
          >
            إعادة تعيين الفلتر
          </Button>
        </div>
      </div>

      <div className="flex justify-between mb-4 flex-wrap items-end">
        <Button variant="ghost" onClick={() => navigate("/dashboard/admin")}>
          <ArrowLeft className="h-4 w-4 ml-2" />
          عودة إلى لوحة التحكم
        </Button>

        <div className="flex gap-2 flex-wrap items-end">
          <Button
            className="flex items-center gap-2"
            variant="default"
            onClick={() => setTransferDialogOpen(true)}
          >
            <ArrowRightLeft className="h-4 w-4" />
            تحويل بين الحسابات
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/dashboard/admin/transactions/deposits")}
          >
            طلبات الإيداع
          </Button>
          <Button
            variant="outline"
            onClick={() =>
              navigate("/dashboard/admin/transactions/withdrawals")
            }
          >
            طلبات السحب
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>كافة المعاملات المالية</CardTitle>
          <CardDescription>
            عرض وإدارة جميع المعاملات المالية في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="all"
            value={activeTab}
            onValueChange={handleTabChange}
          >
            <TabsList className="mb-4">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="deposits">الإيداعات</TabsTrigger>
              <TabsTrigger value="withdrawals">السحوبات</TabsTrigger>
              <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
              <TabsTrigger value="completed">مكتملة</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              <TransactionsList
                emptyStateMessage="لا توجد معاملات مطابقة للمعايير المحددة"
                transactions={filteredTransactions}
                showReceipt={true}
                onApprove={handleApproveTransaction}
                onReject={handleRejectTransaction}
                isLoadingAction={isApproving || isRejecting}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="deposits">
              <TransactionsList
                emptyStateMessage="لا توجد معاملات إيداع مطابقة للمعايير المحددة"
                transactions={filteredTransactions}
                showReceipt={true}
                onApprove={handleApproveTransaction}
                onReject={handleRejectTransaction}
                isLoadingAction={isApproving || isRejecting}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="withdrawals">
              <TransactionsList
                emptyStateMessage="لا توجد معاملات سحب مطابقة للمعايير المحددة"
                transactions={filteredTransactions}
                showReceipt={true}
                onApprove={handleApproveTransaction}
                onReject={handleRejectTransaction}
                isLoadingAction={isApproving || isRejecting}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="pending">
              <TransactionsList
                emptyStateMessage="لا توجد معاملات قيد الانتظار مطابقة للمعايير المحددة"
                transactions={filteredTransactions}
                showReceipt={true}
                onApprove={handleApproveTransaction}
                onReject={handleRejectTransaction}
                isLoadingAction={isApproving || isRejecting}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="completed">
              <TransactionsList
                emptyStateMessage="لا توجد معاملات مكتملة مطابقة للمعايير المحددة"
                transactions={filteredTransactions}
                showReceipt={true}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Fund Transfer Dialog */}
      <Dialog open={transferDialogOpen} onOpenChange={setTransferDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>تحويل الأموال بين الحسابات</DialogTitle>
          </DialogHeader>
          <AdminFundsTransfer
            onSuccess={() => {
              setTransferDialogOpen(false);
              refetch();
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Filter Dialog */}
      <Dialog open={filterDialogOpen} onOpenChange={setFilterDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تصفية المعاملات</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="type">نوع المعاملة</Label>
              <Select
                value={filters.type}
                onValueChange={(value) => updateFilter("type", value)}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="كل الأنواع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل الأنواع</SelectItem>
                  <SelectItem value="deposit">إيداع</SelectItem>
                  <SelectItem value="withdraw">سحب</SelectItem>
                  <SelectItem value="fee">رسوم</SelectItem>
                  <SelectItem value="commission">عمولة</SelectItem>
                  <SelectItem value="transfer">تحويل</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">الحالة</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => updateFilter("status", value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="كل الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل الحالات</SelectItem>
                  <SelectItem value="pending">قيد الانتظار</SelectItem>
                  <SelectItem value="completed">مكتملة</SelectItem>
                  <SelectItem value="approved">معتمدة</SelectItem>
                  <SelectItem value="rejected">مرفوضة</SelectItem>
                  <SelectItem value="failed">فاشلة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">بحث (الاسم أو المرجع)</Label>
              <Input
                id="search"
                value={filters.search}
                onChange={(e) => updateFilter("search", e.target.value)}
                placeholder="ابحث بالاسم أو المرجع"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateFrom">من تاريخ</Label>
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => updateFilter("dateFrom", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateTo">إلى تاريخ</Label>
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => updateFilter("dateTo", e.target.value)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                resetFilters();
                setFilterDialogOpen(false);
              }}
            >
              إعادة تعيين
            </Button>
            <Button onClick={() => setFilterDialogOpen(false)}>
              تطبيق الفلترة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
