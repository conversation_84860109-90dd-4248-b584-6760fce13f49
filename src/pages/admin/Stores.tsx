import React, { useEffect, useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Store } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { StoreCard } from "@/components/stores/StoreCard";
import { StoreDetails } from "@/components/stores/StoreDetails";
import { StoreFilters } from "@/components/stores/StoreFilters";
import EmptyState from "@/components/EmptyState";
import { useToast } from "@/hooks/toast";
import { useNavigate } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Store as StoreType } from "@/types/store";

export default function AdminStores() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCity, setSelectedCity] = useState<string>("");
  const [selectedActivity, setSelectedActivity] = useState<string>("");
  const [shelfSize, setShelfSize] = useState<string>("");
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreType | null>(null);
  const queryClient = useQueryClient();
  const { data: stores = [], isLoading } = useQuery({
    queryKey: ["admin", "stores"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("stores")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching stores:", error);
        throw error;
      }

      return data as StoreType[];
    },
  });
  const { mutate: deleteStores } = useMutation({
    mutationFn: async (storeId: string) => {
      const { error } = await supabase
        .from("stores")
        .delete()
        .eq("id", storeId);
      if (error) {
        console.error("Error deleting store:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["admin", "stores"],
      });
      toast({
        title: "تم الحذف بنجاح",
        description: "تم حذف المحل التجاري بنجاح",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "خطأ",
        description: `حدث خطأ أثناء حذف المحل التجاري: ${error.message}`,
        variant: "destructive",
      });
    },
  });
  const cities = [...new Set(stores.map((store) => store.city))];
  const activities = [
    ...new Set(stores.map((store) => store.legal_activity).filter(Boolean)),
  ];
  const shelfSizes = [
    ...new Set(stores.map((store) => store.shelf_space).filter(Boolean)),
  ];

  const filteredStores = stores.filter((store) => {
    const matchesSearch =
      store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      store.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (store.description?.toLowerCase() || "").includes(
        searchQuery.toLowerCase()
      );

    const matchesCity = selectedCity ? store.city === selectedCity : true;
    const matchesActivity = selectedActivity
      ? store.legal_activity === selectedActivity
      : true;
    const matchesShelfSize = shelfSize ? store.shelf_space === shelfSize : true;

    return matchesSearch && matchesCity && matchesActivity && matchesShelfSize;
  });

  const handleManageStore = (storeId: string) => {
    navigate(`/dashboard/admin/store/${storeId}`);
  };

  const openStoreDetails = (store: StoreType) => {
    setSelectedStore(store);
  };

  const closeStoreDetails = () => {
    setSelectedStore(null);
  };

  const resetFilters = () => {
    setSearchQuery("");
    setSelectedCity("");
    setSelectedActivity("");
    setShelfSize("");
    setIsFilterSheetOpen(false);
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة المحلات التجارية"
        description="إدارة جميع المحلات التجارية في النظام"
        icon={Store}
      />

      <Alert variant="default" className="mb-4 bg-blue-50">
        <AlertDescription>
          يمكنك عرض وإدارة جميع المحلات التجارية المسجلة في النظام
        </AlertDescription>
      </Alert>

      <StoreFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedCity={selectedCity}
        setSelectedCity={setSelectedCity}
        selectedActivity={selectedActivity}
        setSelectedActivity={setSelectedActivity}
        shelfSize={shelfSize}
        setShelfSize={setShelfSize}
        isFilterSheetOpen={isFilterSheetOpen}
        setIsFilterSheetOpen={setIsFilterSheetOpen}
        resetFilters={resetFilters}
        cities={cities}
        activities={activities}
        shelfSizes={shelfSizes}
      />

      {isLoading ? (
        <div className="flex justify-center items-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : filteredStores.length === 0 ? (
        <EmptyState
          title="لا توجد محلات تجارية"
          description="لا توجد محلات تجارية تطابق معايير البحث"
          icon={Store}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
          {filteredStores.map((store) => (
            <StoreCard
              onDelete={() => deleteStores(store.id)}
              key={store.id}
              store={store}
              onViewDetails={() => openStoreDetails(store)}
            />
          ))}
        </div>
      )}

      <StoreDetails
        selectedStore={selectedStore}
        onClose={closeStoreDetails}
        onSendRequest={handleManageStore}
      />
    </DashboardLayout>
  );
}
