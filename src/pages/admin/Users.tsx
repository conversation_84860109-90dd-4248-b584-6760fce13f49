import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Card } from "@/components/ui/card";
import { useUsers } from "@/hooks/useUsers";
import { Loading } from "@/components/ui/loading";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole } from "@/types";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DotSquareIcon,
  MoreVertical,
  PlusIcon,
  SearchIcon,
  UserIcon,
  ShieldIcon,
  Store,
  ShoppingCart,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";

export default function AdminUsers() {
  const [searchInput, setSearchInput] = useState("");

  const {
    users,
    isLoading,
    error,
    page,
    totalPages,
    search,
    roleFilter,
    setPage,
    setSearch,
    setRoleFilter,
    deleteUser,
    changeUserRole,
    refreshUsers,
  } = useUsers({
    initialPage: 1,
    pageSize: 20,
    initialSearch: "",
    initialRole: "all",
    autoFetch: true,
  });

  const handleSearch = () => {
    setSearch(searchInput);
    setPage(1); // Reset to first page when searching
  };

  const handleRoleChange = (role: string) => {
    setRoleFilter(role as UserRole | "all");
    setPage(1); // Reset to first page when filtering
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm("هل أنت متأكد من رغبتك في حذف هذا المستخدم؟")) {
      try {
        await deleteUser(userId);
        toast.success("تم حذف المستخدم بنجاح");
      } catch (error) {
        toast.error("حدث خطأ أثناء حذف المستخدم");
      }
    }
  };

  const handleChangeRole = async (userId: string, newRole: UserRole) => {
    try {
      await changeUserRole(userId, newRole);
      toast.success("تم تغيير دور المستخدم بنجاح");
    } catch (error) {
      toast.error("حدث خطأ أثناء تغيير دور المستخدم");
    }
  };

  // Helper function to get role display information
  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-purple-100 text-purple-800">مدير</Badge>;
      case "store":
        return <Badge className="bg-blue-100 text-blue-800">متجر تقليدي</Badge>;
      case "ecommerce":
        return (
          <Badge className="bg-green-100 text-green-800">متجر إلكتروني</Badge>
        );
      default:
        return <Badge className="bg-gray-100 text-gray-800">غير محدد</Badge>;
    }
  };

  if (isLoading && users.length === 0) {
    return (
      <DashboardLayout>
        <PageHeader title="إدارة المستخدمين" description="جاري التحميل..." />
        <Card className="p-6">
          <Loading />
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة المستخدمين"
        description="عرض وإدارة جميع المستخدمين في النظام"
      />

      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex flex-1 items-center gap-2">
            <Input
              placeholder="البحث عن مستخدم..."
              className="flex-1"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
            <Button variant="outline" onClick={handleSearch}>
              <SearchIcon className="h-4 w-4" />
            </Button>
          </div>

          <Select value={roleFilter} onValueChange={handleRoleChange}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="فلتر حسب الدور" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="admin">مدير</SelectItem>
              <SelectItem value="store">متجر تقليدي</SelectItem>
              <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {error && (
          <div className="p-4 mb-4 text-red-500 bg-red-50 rounded-md">
            <p>حدث خطأ أثناء تحميل المستخدمين: {error}</p>
          </div>
        )}

        {users.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">المستخدم</TableHead>
                <TableHead className="text-right">البريد الإلكتروني</TableHead>
                <TableHead className="text-right">الدور</TableHead>
                <TableHead className="text-right">رقم الهاتف</TableHead>
                <TableHead className="text-right">تاريخ الإنشاء</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3 text-gray-700">
                        {user.name?.substring(0, 2) || "??"}
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{getRoleBadge(user.role as UserRole)}</TableCell>
                  <TableCell>{user.phone_number || "غير متوفر"}</TableCell>
                  <TableCell>
                    {new Date(user.created_at).toLocaleDateString("ar-EG", {
                      year: "numeric",
                      month: "2-digit",
                      day: "2-digit",
                    })}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="py-8">
            <p className="text-center text-gray-500">
              {search ? `لا توجد نتائج لـ "${search}"` : "لا يوجد مستخدمون بعد"}
            </p>
          </div>
        )}

        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              disabled={page <= 1}
              onClick={() => setPage(page - 1)}
            >
              السابق
            </Button>
            <div className="text-sm">
              الصفحة {page} من {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              disabled={page >= totalPages}
              onClick={() => setPage(page + 1)}
            >
              التالي
            </Button>
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
}
