import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { useHostingRequests } from "@/hooks/hosting/useHostingRequests";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { HostingRequest, OrderStatus } from "@/types";
import DashboardLayout from "@/layouts/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import {
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  Dialog,
} from "@/components/ui/dialog";
import { Package, ShoppingBag, Timer } from "lucide-react";

const AdminHostingRequests = () => {
  const { user } = useAuth();
  const {
    requests: hostingRequests,
    isLoading,
    updateStatus,
  } = useHostingRequests();
  const navigate = useNavigate();
  const [selectedRequest, setSelectedRequest] = useState<HostingRequest | null>(
    null
  );
  const [detailsOpen, setDetailsOpen] = useState(false);

  const getStatusBadge = (status: string) => {
    let variant = "";
    let label = status;
    let icon = null;

    switch (status) {
      case "awaiting_shipping":
        variant = "bg-amber-100 text-amber-800 hover:bg-amber-200";
        label = "بأنتظار شحن المنتجات";
        icon = <Package className="h-4 w-4 mr-1" />;
        break;
      case "on_sale":
        variant = "bg-green-100 text-green-800 hover:bg-green-200";
        label = "منتجات معروضة للبيع";
        icon = <ShoppingBag className="h-4 w-4 mr-1" />;
        break;
      case "expired":
        variant = "bg-red-100 text-red-800 hover:bg-red-200";
        label = "اشتراك منتهي";
        icon = <Timer className="h-4 w-4 mr-1" />;
        break;
      case "pending":
        variant = "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
        label = "قيد الانتظار";
        break;
      case "accepted":
        variant = "bg-green-100 text-green-800 hover:bg-green-200";
        label = "مقبول";
        break;
      case "rejected":
        variant = "bg-red-100 text-red-800 hover:bg-red-200";
        label = "مرفوض";
        break;
      default:
        variant = "bg-gray-100 text-gray-800 hover:bg-gray-200";
        break;
    }

    return (
      <Badge className={`flex items-center ${variant}`}>
        {icon}
        {label}
      </Badge>
    );
  };

  console.log(hostingRequests);

  const columns: ColumnDef<HostingRequest>[] = [
    {
      accessorKey: "id",
      header: "المعرف",
      cell: ({ row }) => {
        const id = row.getValue("id") as string;
        return (
          <span className="font-mono text-xs">{id.substring(0, 8)}...</span>
        );
      },
    },
    {
      accessorKey: "ecommerce_name",
      header: "اسم المتجر الإلكتروني",
    },
    {
      accessorKey: "store_name",
      header: "اسم المتجر",
    },
    {
      accessorKey: "status",
      header: "الحالة",
      cell: ({ row }) => getStatusBadge(row.getValue("status") as string),
    },
    {
      accessorKey: "created_at",
      header: "تاريخ الإنشاء",
      cell: ({ row }) =>
        new Date(row.getValue("created_at")).toLocaleDateString(),
    },
    {
      id: "actions",
      header: "إجراءات",
      cell: ({ row }) => {
        const request = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedRequest(request);
                setDetailsOpen(true);
              }}
            >
              التفاصيل
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                navigate(`/dashboard/hosting-requests/${request.id}`)
              }
            >
              عرض
            </Button>
          </div>
        );
      },
    },
  ];

  const handleStatusUpdate = async (newStatus: OrderStatus) => {
    if (!selectedRequest) return;

    await updateStatus(selectedRequest.id!, newStatus);
    setDetailsOpen(false);
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <h1 className="text-2xl font-bold tracking-tight">
            طلبات الاستضافة للمسؤول
          </h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => navigate("/dashboard")}>
              العودة إلى لوحة التحكم
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>طلبات الاستضافة</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={columns}
              data={hostingRequests}
              isLoading={isLoading}
              searchField="ecommerce_name"
            />
          </CardContent>
        </Card>
      </div>

      {selectedRequest && (
        <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>تفاصيل طلب الاستضافة</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">اسم المتجر الإلكتروني</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedRequest.ecommerce_name}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">اسم المتجر</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedRequest.store_name}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">الحالة</p>
                  <div className="mt-1">
                    {getStatusBadge(selectedRequest.status)}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium">تاريخ الإنشاء</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(selectedRequest.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {selectedRequest.notes && (
                <div>
                  <p className="text-sm font-medium">ملاحظات</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    {selectedRequest.notes}
                  </p>
                </div>
              )}

              <div className="flex flex-col gap-2">
                <p className="text-sm font-medium">تحديث الحالة</p>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate("awaiting_shipping")}
                    className="flex items-center gap-2 bg-amber-50 hover:bg-amber-100 text-amber-700 border-amber-200"
                  >
                    <Package className="h-4 w-4" />
                    بأنتظار شحن المنتجات
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate("on_sale")}
                    className="flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                  >
                    <ShoppingBag className="h-4 w-4" />
                    منتجات معروضة للبيع
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleStatusUpdate("expired")}
                    className="flex items-center gap-2 bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                  >
                    <Timer className="h-4 w-4" />
                    اشتراك منتهي
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </DashboardLayout>
  );
};

export default AdminHostingRequests;
