import React from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Plus } from "lucide-react";
import { ProductsTable } from "@/components/products/ProductsTable";
import { ProductProvider } from "@/contexts/ProductContext";

export default function AdminProducts() {
  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة المنتجات"
        description="عرض وإدارة جميع المنتجات في النظام"
        actions={
          <Button asChild>
            <Link to="/dashboard/products/add">
              <Plus className="h-4 w-4 ml-2" />
              إضافة منتج
            </Link>
          </Button>
        }
      />
      <div className="space-y-4">
        <>
          <ProductsTable />
        </>
      </div>
    </DashboardLayout>
  );
}
