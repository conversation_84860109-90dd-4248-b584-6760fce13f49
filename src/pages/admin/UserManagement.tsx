
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { UserRole, User } from '@/types';
import { useAuthRole } from '@/hooks/useAuthRole';
import { useUsers } from '@/hooks/useUsers';
import { Shield, Users, Search, Filter, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import UserManagementTable from '@/components/admin/UserManagementTable';
import { translateRoleToArabic } from '@/utils/roleUtils';

const UserManagement = () => {
  const { hasAccess, isAdmin, isSubAdmin, userRole } = useAuthRole(['admin', 'sub-admin']);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<UserRole | 'all'>('all');
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'ecommerce' as UserRole,
    phone_number: '',
  });

  const {
    users,
    isLoading,
    error,
    search,
    setSearch,
    roleFilter,
    setRoleFilter,
    refreshUsers,
    updateUser,
    deleteUser,
    createUser,
    changeUserRole,
  } = useUsers({
    initialPage: 1,
    pageSize: 50,
    initialSearch: '',
    initialRole: 'all',
    autoFetch: true,
  });

  // التحقق من الخطأ
  useEffect(() => {
    if (error) {
      toast.error('خطأ في تحميل بيانات المستخدمين', { description: error });
    }
  }, [error]);

  // البحث والتصفية
  const handleSearch = () => {
    setSearch(searchTerm);
  };

  const handleFilter = (role: UserRole | 'all') => {
    setFilterRole(role);
    setRoleFilter(role);
  };

  // إضافة مستخدم جديد
  const handleCreateUser = async () => {
    if (!newUser.name || !newUser.email || !newUser.password) {
      toast.error('يرجى إدخال جميع الحقول المطلوبة');
      return;
    }

    if (newUser.password !== newUser.confirmPassword) {
      toast.error('كلمة المرور غير متطابقة');
      return;
    }

    setIsProcessing(true);

    try {
      const success = await createUser({
        email: newUser.email,
        password: newUser.password,
        name: newUser.name,
        role: newUser.role,
        phoneNumber: newUser.phone_number,
      });

      if (success) {
        toast.success('تمت إضافة المستخدم بنجاح');
        setIsCreateDialogOpen(false);
        setNewUser({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          role: 'ecommerce',
          phone_number: '',
        });
        refreshUsers();
      } else {
        toast.error('فشل إضافة المستخدم');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error('حدث خطأ أثناء إضافة المستخدم');
    } finally {
      setIsProcessing(false);
    }
  };

  // تعطيل/تفعيل المستخدم
  const handleToggleActive = async (userId: string, active: boolean) => {
    try {
      const success = await updateUser(userId, { active });
      return success;
    } catch (error) {
      console.error('Error toggling user active state:', error);
      return false;
    }
  };

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Shield className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">وصول مرفوض</h2>
          <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة المستخدمين"
        description="عرض وإدارة جميع المستخدمين في النظام"
        icon={Users}
      />

      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث عن اسم أو بريد إلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 items-center">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={filterRole} onValueChange={(value) => handleFilter(value as UserRole | 'all')}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="تصفية حسب الدور" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المستخدمين</SelectItem>
                  <SelectItem value="admin">مشرفو النظام</SelectItem>
                  <SelectItem value="sub-admin">المشرفون الفرعيون</SelectItem>
                  <SelectItem value="store">المتاجر التقليدية</SelectItem>
                  <SelectItem value="ecommerce">المتاجر الإلكترونية</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleSearch}>بحث</Button>
            {isAdmin && (
              <Button variant="default" onClick={() => setIsCreateDialogOpen(true)}>
                <UserPlus className="h-4 w-4 ml-2" />
                إضافة مستخدم
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span>المستخدمون</span>
            <Badge className="bg-secondary text-secondary-foreground">
              {users?.length || 0}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <UserManagementTable
            users={users || []}
            isLoading={isLoading}
            onUpdateUser={updateUser}
            onDeleteUser={deleteUser}
            onChangeRole={changeUserRole}
            onToggleActive={handleToggleActive}
            currentUserRole={userRole as UserRole}
          />
        </CardContent>
      </Card>

      {/* حوار إضافة مستخدم جديد */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            <DialogDescription>
              أدخل المعلومات المطلوبة لإنشاء مستخدم جديد
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                value={newUser.name}
                onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                البريد الإلكتروني
              </Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                كلمة المرور
              </Label>
              <Input
                id="password"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                تأكيد كلمة المرور
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                value={newUser.confirmPassword}
                onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                الدور
              </Label>
              <Select
                value={newUser.role}
                onValueChange={(value: UserRole) => setNewUser({ ...newUser, role: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر الدور">
                    {translateRoleToArabic(newUser.role as UserRole)}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {isAdmin && (
                    <>
                      <SelectItem value="admin">مشرف النظام</SelectItem>
                      <SelectItem value="sub-admin">مشرف فرعي</SelectItem>
                    </>
                  )}
                  <SelectItem value="store">متجر تقليدي</SelectItem>
                  <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone_number" className="text-right">
                رقم الهاتف
              </Label>
              <Input
                id="phone_number"
                value={newUser.phone_number}
                onChange={(e) => setNewUser({ ...newUser, phone_number: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleCreateUser} disabled={isProcessing}>
              {isProcessing ? 'جاري الإنشاء...' : 'إنشاء المستخدم'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default UserManagement;
