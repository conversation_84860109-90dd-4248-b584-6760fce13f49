
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Shield, Plus, Users } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { useAuthRole } from '@/hooks/useAuthRole';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import AdminRoleAssignForm from '@/components/admin/AdminRoleAssignForm';

export default function AdminManagement() {
  const { user } = useAuth();
  const { hasAccess, isAdmin } = useAuthRole(['admin']);
  const [admins, setAdmins] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (hasAccess) {
      fetchAdmins();
    }
  }, [hasAccess]);

  const fetchAdmins = async () => {
    try {
      setIsLoading(true);
      
      // الحصول على المستخدمين بصلاحيات المدير
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .in('role', ['admin', 'sub-admin']);
      
      if (error) {
        throw error;
      }
      
      setAdmins(data || []);
    } catch (error: any) {
      console.error('Error fetching admins:', error);
      toast.error('فشل في جلب بيانات المدراء');
    } finally {
      setIsLoading(false);
    }
  };

  const updateAdminStatus = async (adminId: string, active: boolean) => {
    try {
      // تحديث حالة النشاط للمدير
      const { error } = await supabase
        .from('profiles')
        .update({ active })
        .eq('id', adminId);

      if (error) {
        throw error;
      }

      // تحديث القائمة المحلية
      setAdmins(admins.map(admin => 
        admin.id === adminId ? { ...admin, active } : admin
      ));

      toast.success(`تم ${active ? 'تنشيط' : 'تعطيل'} حساب المدير بنجاح`);
    } catch (error: any) {
      console.error('Error updating admin status:', error);
      toast.error('فشل في تحديث حالة المدير');
    }
  };

  const removeAdmin = async (adminId: string) => {
    if (!window.confirm('هل أنت متأكد من رغبتك في إزالة صلاحيات المدير من هذا المستخدم؟')) {
      return;
    }

    try {
      // تحديث الصلاحية إلى مستخدم عادي
      const { error } = await supabase
        .from('profiles')
        .update({ 
          role: 'ecommerce',
          permissions: [] 
        })
        .eq('id', adminId);

      if (error) {
        throw error;
      }

      // تحديث المعلومات التعريفية للمستخدم
      await supabase.functions.invoke('update-user-metadata', {
        body: {
          userId: adminId,
          metadata: {
            role: 'ecommerce',
            permissions: []
          }
        }
      });

      // تحديث القائمة المحلية
      setAdmins(admins.filter(admin => admin.id !== adminId));
      toast.success('تم إزالة صلاحيات المدير بنجاح');
    } catch (error: any) {
      console.error('Error removing admin:', error);
      toast.error('فشل في إزالة صلاحيات المدير');
    }
  };

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Shield className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">وصول مرفوض</h2>
          <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader 
        title="إدارة المسؤولين" 
        description="إدارة صلاحيات المسؤولين في النظام"
        icon={Shield}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>قائمة المسؤولين</CardTitle>
              <CardDescription>
                يمكنك إدارة صلاحيات المسؤولين في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-4">جاري التحميل...</div>
              ) : admins.length === 0 ? (
                <div className="text-center py-4">لا يوجد مسؤولين حالياً</div>
              ) : (
                <div className="space-y-4">
                  {admins.map((admin) => (
                    <div key={admin.id} className="flex items-center justify-between border-b pb-4">
                      <div>
                        <p className="font-medium">{admin.name}</p>
                        <p className="text-sm text-muted-foreground">{admin.email}</p>
                        <div className="flex items-center mt-1">
                          <span className={`inline-block w-2 h-2 rounded-full mr-2 ${admin.active ? 'bg-green-500' : 'bg-red-500'}`}></span>
                          <span className="text-xs">{admin.active ? 'نشط' : 'غير نشط'}</span>
                        </div>
                        <p className="text-xs mt-1">
                          الصلاحية: {admin.role === 'admin' ? 'مدير النظام' : 'مدير فرعي'}
                        </p>
                      </div>
                      <div className="flex space-x-2 space-x-reverse">
                        {user?.id !== admin.id && (
                          <>
                            <Button
                              variant={admin.active ? "destructive" : "default"}
                              size="sm"
                              onClick={() => updateAdminStatus(admin.id, !admin.active)}
                            >
                              {admin.active ? 'تعطيل' : 'تنشيط'}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeAdmin(admin.id)}
                            >
                              إزالة الصلاحية
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <AdminRoleAssignForm />
        </div>
      </div>
    </DashboardLayout>
  );
}
