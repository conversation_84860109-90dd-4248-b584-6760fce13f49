
import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Transaction, TransactionStatus } from "@/types";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAdminTransactionOperations } from "@/hooks/useAdminTransactionOperations";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { Loading } from "@/components/ui/loading";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Check, Image, X } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, getTransactionStatusText } from "@/lib/transactionUtils";

export default function AdminDeposits() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [deposits, setDeposits] = useState<Transaction[]>([]);
  const { approveTransaction, rejectTransaction } = useAdminTransactionOperations();
  const [selectedDeposit, setSelectedDeposit] = useState<Transaction | null>(null);
  const [rejectReason, setRejectReason] = useState("");
  const [confirmApproveDialogOpen, setConfirmApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [imagePreviewOpen, setImagePreviewOpen] = useState(false);

  // Make sure admin user
  useEffect(() => {
    if (user && user.role !== "admin" && user.role !== "sub-admin") {
      navigate("/dashboard");
    }
  }, [user, navigate]);

  // Fetch deposits
  useEffect(() => {
    const fetchDeposits = async () => {
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from("transactions")
          .select("*")
          .eq("type", "deposit")
          .order("created_at", { ascending: false });

        if (error) throw error;
        
        setDeposits(data as Transaction[]);
      } catch (error) {
        console.error("Error fetching deposits:", error);
        toast.error("حدث خطأ أثناء جلب طلبات الإيداع");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeposits();
  }, []);

  // Handle deposit approval
  const handleApproveDeposit = async () => {
    if (!selectedDeposit) return;
    
    try {
      const result = await approveTransaction(selectedDeposit.id);
      if (result) {
        setDeposits(prevState =>
          prevState.map(dep =>
            dep.id === selectedDeposit.id ? { ...dep, status: "completed" as TransactionStatus } : dep
          )
        );
        toast.success("تم اعتماد الإيداع بنجاح");
        setConfirmApproveDialogOpen(false);
      }
    } catch (error) {
      console.error("Error approving deposit:", error);
    }
  };

  // Handle deposit rejection
  const handleRejectDeposit = async () => {
    if (!selectedDeposit) return;
    
    try {
      const result = await rejectTransaction({ id: selectedDeposit.id, reason: rejectReason });
      if (result) {
        setDeposits(prevState =>
          prevState.map(dep =>
            dep.id === selectedDeposit.id ? { ...dep, status: "rejected" as TransactionStatus } : dep
          )
        );
        toast.success("تم رفض الإيداع بنجاح");
        setRejectDialogOpen(false);
        setRejectReason("");
      }
    } catch (error) {
      console.error("Error rejecting deposit:", error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("ar-SA", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    } catch (error) {
      return dateString;
    }
  };

  const getStatusBadgeColor = (status: TransactionStatus) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6">
        <PageHeader
          title="طلبات الإيداع"
          description="إدارة ومراجعة طلبات إيداع المستخدمين"
        />
      </div>

      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate("/dashboard/admin/transactions")}>
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة إلى جميع المعاملات
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>طلبات الإيداع</CardTitle>
          <CardDescription>
            قائمة بجميع طلبات الإيداع المقدمة من المستخدمين
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>التاريخ</TableHead>
                <TableHead>المستخدم</TableHead>
                <TableHead>المبلغ</TableHead>
                <TableHead>المرجع</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>إيصال</TableHead>
                <TableHead>إجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {deposits.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-6">
                    لا توجد طلبات إيداع حالياً
                  </TableCell>
                </TableRow>
              ) : (
                deposits.map((deposit) => (
                  <TableRow key={deposit.id}>
                    <TableCell className="whitespace-nowrap">
                      {formatDate(deposit.created_at)}
                    </TableCell>
                    <TableCell>{deposit.user_name}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(deposit.amount)}</TableCell>
                    <TableCell>{deposit.reference || "-"}</TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(deposit.status)}>
                        {getTransactionStatusText(deposit.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {deposit.receipt_url ? (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => {
                            setSelectedDeposit(deposit);
                            setImagePreviewOpen(true);
                          }}
                        >
                          <Image className="h-4 w-4" />
                        </Button>
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2 space-x-reverse">
                        {deposit.status === "pending" && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-green-600 hover:text-green-700"
                              onClick={() => {
                                setSelectedDeposit(deposit);
                                setConfirmApproveDialogOpen(true);
                              }}
                            >
                              <Check className="h-4 w-4 ml-1" />
                              موافقة
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => {
                                setSelectedDeposit(deposit);
                                setRejectDialogOpen(true);
                              }}
                            >
                              <X className="h-4 w-4 ml-1" />
                              رفض
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Approve Dialog */}
      <Dialog open={confirmApproveDialogOpen} onOpenChange={setConfirmApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الموافقة على الإيداع</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من الموافقة على إيداع مبلغ {selectedDeposit?.amount} ريال للمستخدم {selectedDeposit?.user_name}؟
              <br />
              هذا سيضيف المبلغ إلى رصيد المستخدم.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmApproveDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleApproveDeposit} className="bg-green-600 hover:bg-green-700">
              تأكيد الموافقة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>رفض طلب الإيداع</DialogTitle>
            <DialogDescription>
              الرجاء إدخال سبب رفض طلب إيداع مبلغ {selectedDeposit?.amount} ريال المقدم من {selectedDeposit?.user_name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="سبب الرفض (اختياري)"
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleRejectDeposit} variant="destructive">
              تأكيد الرفض
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Preview Dialog */}
      <Dialog open={imagePreviewOpen} onOpenChange={setImagePreviewOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إيصال الإيداع</DialogTitle>
          </DialogHeader>
          {selectedDeposit?.receipt_url && (
            <div className="flex items-center justify-center">
              <img
                src={selectedDeposit.receipt_url}
                alt="إيصال الإيداع"
                className="max-h-[70vh] max-w-full object-contain"
              />
            </div>
          )}
          <DialogFooter>
            {selectedDeposit?.receipt_url && (
              <Button asChild variant="outline">
                <a
                  href={selectedDeposit.receipt_url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  فتح الصورة في نافذة جديدة
                </a>
              </Button>
            )}
            <Button onClick={() => setImagePreviewOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
