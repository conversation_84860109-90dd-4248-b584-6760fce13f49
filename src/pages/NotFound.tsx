
import React from "react";
import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { MainLayout } from "@/layouts/MainLayout";
import { Button } from "@/components/ui/button";
import { Home, ArrowLeft, Search, HelpCircle } from "lucide-react";
import { useAuth } from "@/contexts/auth";

const NotFound = () => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  // Extract the last part of the path for display purposes
  const pathSegments = location.pathname.split('/').filter(Boolean);
  const lastPathSegment = pathSegments.length > 0 ? pathSegments[pathSegments.length - 1] : '';

  return (
    <MainLayout>
      <div className="container mx-auto py-20 px-4">
        <div className="max-w-md mx-auto text-center">
          <div className="mb-6 flex justify-center">
            <div className="h-24 w-24 rounded-full bg-red-100 flex items-center justify-center">
              <Search className="h-12 w-12 text-red-500" />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-4">404</h1>
          <p className="text-xl text-gray-600 mb-2">
            عذراً، الصفحة التي تبحث عنها غير موجودة
          </p>
          {lastPathSegment && (
            <p className="text-gray-500 mb-8">
              لم نتمكن من العثور على: <span className="font-semibold">{lastPathSegment}</span>
            </p>
          )}
          
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg">
                <Link to="/">
                  <Home className="h-5 w-5 ml-2" />
                  العودة للصفحة الرئيسية
                </Link>
              </Button>
              <Button variant="outline" size="lg" onClick={() => window.history.back()}>
                <ArrowLeft className="h-5 w-5 ml-2" />
                الرجوع للصفحة السابقة
              </Button>
            </div>
            
            {isAuthenticated && (
              <Button asChild variant="link" className="mt-4">
                <Link to="/dashboard">
                  الذهاب إلى لوحة التحكم
                </Link>
              </Button>
            )}
            
            <div className="pt-8 border-t mt-8">
              <Link to="/contact" className="flex items-center justify-center text-blue-600 hover:text-blue-800">
                <HelpCircle className="h-4 w-4 ml-1" />
                هل تحتاج إلى مساعدة؟ تواصل معنا
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default NotFound;
