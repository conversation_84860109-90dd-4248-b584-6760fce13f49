import React, { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate, Link } from 'react-router-dom';
import AuthHeader from '@/components/auth/AuthHeader';
import LoginFooter from '@/components/auth/LoginFooter';
import { useAuth } from '@/contexts/auth';
import { MainLayout } from '@/layouts/MainLayout';

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'البريد الإلكتروني غير صالح' }),
});

type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPassword() {
  const { sendPasswordResetOTP } = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const form = useForm<ForgotPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: ForgotPasswordForm) => {
    setIsSubmitting(true);
    sessionStorage.setItem('email', data.email);
    try {
      const result = await sendPasswordResetOTP(data.email);
      if (result) {
        setSuccess(true);
        setTimeout(() => {
          navigate('/reset-password', { state: { email: data.email } });
        }, 2000);
      }
    } catch (error) {
      console.error('Error sending reset OTP:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="flex justify-center items-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <AuthHeader 
              title="استعادة كلمة المرور" 
              subtitle="أدخل بريدك الإلكتروني لاستعادة كلمة المرور" 
            />
            <CardTitle className="text-2xl font-bold text-center">استعادة كلمة المرور</CardTitle>
            <CardDescription className="text-center">
              أدخل بريدك الإلكتروني لإرسال رمز إعادة تعيين كلمة المرور
            </CardDescription>
          </CardHeader>
          <CardContent>
            {success ? (
              <div className="text-center text-green-600 p-4">
                تم إرسال رمز إعادة تعيين كلمة المرور بنجاح. سيتم تحويلك قريبًا.
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="البريد الإلكتروني"
                            {...field}
                            type="email"
                            autoComplete="email"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isSubmitting}>
                    {isSubmitting ? 'جاري الإرسال...' : 'إرسال رمز الإستعادة'}
                  </Button>
                </form>
              </Form>
            )}
          </CardContent>
          <CardFooter>
            <LoginFooter />
          </CardFooter>
        </Card>
      </div>
    </MainLayout>
  );
}
