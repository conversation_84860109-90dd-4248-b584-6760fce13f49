
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import MainLayout from '@/layouts/MainLayout';
import { AlertTriangle } from 'lucide-react';

export default function VerificationError() {
  const navigate = useNavigate();
  const location = useLocation();
  const errorMessage = location.state?.error || 'حدث خطأ أثناء التحقق من البريد الإلكتروني.';

  return (
    <MainLayout>
      <div className="container flex flex-col items-center justify-center py-20 text-center">
        <div className="mx-auto flex max-w-[400px] flex-col items-center justify-center space-y-4">
          <div className="rounded-full bg-red-100 p-3">
            <AlertTriangle className="h-10 w-10 text-red-600" />
          </div>
          
          <h1 className="text-2xl font-bold">فشل التحقق من البريد الإلكتروني</h1>
          
          <p className="text-muted-foreground">
            {errorMessage}
          </p>

          <div className="flex flex-col gap-2 w-full mt-4">
            <Button onClick={() => navigate('/login')} className="w-full">
              تسجيل الدخول مرة أخرى
            </Button>
            
            <Button variant="outline" onClick={() => navigate('/')} className="w-full">
              العودة إلى الصفحة الرئيسية
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
