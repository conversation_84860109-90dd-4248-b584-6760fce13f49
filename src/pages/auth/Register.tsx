import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { useAuth } from "@/contexts/auth";
import { RegisterData } from "@/contexts/auth/types";
import { useToast } from "@/hooks/use-toast";

const formSchema = z
  .object({
    name: z.string().min(3, { message: "الاسم يجب أن يكون 3 أحرف على الأقل" }),
    email: z.string().email({ message: "البريد الإلكتروني غير صالح" }),
    password: z
      .string()
      .min(6, { message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" }),
    confirmPassword: z.string().min(6, {
      message: "تأكيد كلمة المرور يجب أن تكون 6 أحرف على الأقل",
    }),
    role: z.enum(["store", "ecommerce"], { message: "يرجى اختيار نوع الحساب" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "كلمات المرور غير متطابقة",
    path: ["confirmPassword"],
  });

type FormData = z.infer<typeof formSchema>;

export default function Register() {
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { register } = useAuth();
  const { toast } = useToast();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "ecommerce",
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    try {
      // Create RegisterData object that meets the required interface
      const userData: RegisterData = {
        name: data.name,
        email: data.email,
        password: data.password,

        role: data.role, // Cast to match expected UserRole type
      };

      const result = await register(userData);

      if (result) {
        setSuccessMessage(
          "تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك."
        );
        form.reset();
      } else {
        toast({
          variant: "destructive",
          title: "خطأ في التسجيل",
          description: "فشل إنشاء الحساب. يرجى المحاولة مرة أخرى.",
        });
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "خطأ في التسجيل",
        description:
          "حدث خطأ أثناء محاولة إنشاء الحساب. يرجى المحاولة مرة أخرى.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">منصة رفوف للمتاجر الإلكترونية</h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            خدمات استضافة المتاجر الإلكترونية في المحلات التجارية
          </p>
        </div>

        <Card className="shadow-lg">
          <CardContent className="pt-6">
            <div className="space-y-6">
              <div className="space-y-2 text-center">
                <h1 className="text-3xl font-bold">إنشاء حساب جديد</h1>
                <p className="text-gray-500 dark:text-gray-400">
                  أدخل بياناتك لإنشاء حساب جديد
                </p>
              </div>

              {successMessage ? (
                <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-md">
                  {successMessage}
                  <div className="mt-4">
                    <Button
                      onClick={() => (window.location.href = "/auth/login")}
                      className="w-full"
                    >
                      الذهاب إلى صفحة تسجيل الدخول
                    </Button>
                  </div>
                </div>
              ) : (
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>الاسم</FormLabel>
                          <FormControl>
                            <Input placeholder="أدخل اسمك الكامل" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>البريد الإلكتروني</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="<EMAIL>"
                              {...field}
                              type="email"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>كلمة المرور</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder="********"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>تأكيد كلمة المرور</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder="********"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>نوع الحساب</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="اختر نوع الحساب" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="ecommerce">
                                متجر إلكتروني
                              </SelectItem>
                              <SelectItem value="store">محل تجاري</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading}
                    >
                      {isLoading ? "جاري التسجيل..." : "إنشاء حساب"}
                    </Button>
                  </form>
                </Form>
              )}

              <div className="mt-4 text-center text-sm">
                لديك حساب بالفعل؟{" "}
                <a
                  href="/auth/login"
                  className="text-blue-600 hover:text-blue-500"
                >
                  تسجيل الدخول
                </a>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6 text-center text-sm">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
          >
            العودة إلى الصفحة الرئيسية
          </a>
        </div>
      </div>
    </div>
  );
}
