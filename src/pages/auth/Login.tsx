
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tabs, TabsContent } from '@/components/ui/tabs';

import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth';
import LoginForm from '@/components/auth/LoginForm';
import PasswordResetForm from '@/components/auth/PasswordResetForm';

export default function Login() {
  const [activeTab, setActiveTab] = useState<string>('login');
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleSwitchToRegister = () => {
    navigate('/register');
  };

  const handleSwitchToResetPassword = () => {
    setActiveTab('reset-password');
  };

  const handleBackToLogin = () => {
    setActiveTab('login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">منصة رفوف للمتاجر الإلكترونية</h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            خدمات استضافة المتاجر الإلكترونية في المحلات التجارية
          </p>
        </div>
        
        <Card className="shadow-lg">
          <CardContent className="pt-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsContent value="login" className="mt-6">
                <LoginForm
                  switchToRegister={handleSwitchToRegister}
                  switchToResetPassword={handleSwitchToResetPassword}
                />
              </TabsContent>
              <TabsContent value="reset-password" className="mt-6">
                <PasswordResetForm onBackToLogin={handleBackToLogin} /> 
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
        
        <div className="mt-6 text-center text-sm">
          <a href="/" className="text-blue-600 hover:text-blue-500 dark:text-blue-400">
            العودة إلى الصفحة الرئيسية
          </a>
        </div>
      </div>
    </div>
  );
}
