import React, { useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import AuthHeader from '@/components/auth/AuthHeader';
import PasswordResetForm from '@/components/auth/PasswordResetForm';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, Check, AlertTriangle } from 'lucide-react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Eye, EyeOff } from 'lucide-react';

const passwordResetSchema = z.object({
  password: z.string()
    .min(8, { message: "يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل" }),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
});
export default function  ResetPassword(p0: { password: string; confirmPassword: string; }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [isProcessingToken, setIsProcessingToken] = useState(false);
  const [isSettingNewPassword, setIsSettingNewPassword] = useState(false);
  const [showTokenForm, setShowTokenForm] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  // Form for new password
  const passwordForm = useForm<z.infer<typeof passwordResetSchema>>({
    resolver: zodResolver(passwordResetSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    // تحسين كيفية استخراج الرمز من URL
    const extractToken = () => {
      // أولاً نبحث عن الرمز في معلمات البحث (1)
      let token = searchParams.get('token') || searchParams.get('access_token');
      
      // ثم نبحث عن الرمز في hash URL (2)
      if (!token && window.location.hash) {
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        token = hashParams.get('access_token');
      }
      
      // نتحقق من URL المباشر (3) - لبعض إصدارات Supabase
      if (!token) {
        const urlParts = window.location.href.split('#');
        if (urlParts.length > 1) {
          token = urlParts[1];
        }
      }
      
      return token;
    };

    const token = extractToken();
    
    if (token) {
      processResetToken(token);
    } else {
      setShowTokenForm(true);
    }
    
  }, [searchParams, location]);

  const processResetToken = async (token: string) => {
    setIsProcessingToken(true);
    try {
      // تحقق من صحة الرمز
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'recovery',
      });

      if (error) {
        console.error("Token verification error:", error);
        setError(`فشل التحقق من الرمز: ${error.message}`);
        setShowTokenForm(true);
      } else {
        setShowTokenForm(false);
      }
    } catch (err) {
      console.error("Token processing error:", err);
      setError(err.message || "حدث خطأ أثناء معالجة الطلب");
      setShowTokenForm(true);
    } finally {
      setIsProcessingToken(false);
    }
  };

  const handlePasswordReset = async (values: z.infer<typeof passwordResetSchema>) => {
    setIsSettingNewPassword(true);
    setError(null);
    
    try {
      const { error } = await supabase.auth.updateUser({ 
        password: values.password 
      });

      if (error) {
        console.error("Password update error:", error);
        throw error;
      }
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user?.email) {
        // Send password changed confirmation email
        try {
          await supabase.functions.invoke('send-confirmation-email', {
            body: JSON.stringify({
              email: user.email,
              type: 'password-changed'
            })
          });
        } catch (emailError) {
          console.error("Error sending password change confirmation:", emailError);
          // Don't fail the password change just because the email failed
        }
      }
      
      setSuccess(true);
      passwordForm.reset();
      
      toast.success("تم تغيير كلمة المرور بنجاح", {
        description: "يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة",
      });
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
      
    } catch (err) {
      console.error("Password reset error:", err);
      setError(err.message || "حدث خطأ أثناء تغيير كلمة المرور");
      toast.error("فشل تغيير كلمة المرور", {
        description: err.message || "حدث خطأ أثناء محاولة تغيير كلمة المرور، يرجى المحاولة مرة أخرى",
      });
    } finally {
      setIsSettingNewPassword(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  return (
    <MainLayout>
      <div className="container relative flex pt-10 flex-col items-center justify-center lg:px-0">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          {isProcessingToken ? (
            <div className="text-center p-6">
              <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
              <h3 className="text-xl font-medium">جاري التحقق من الرمز...</h3>
              <p className="text-sm text-muted-foreground mt-2">
                نحن نتحقق من صلاحية الرمز الخاص بك. يرجى الانتظار.
              </p>
            </div>
          ) : showTokenForm ? (
            <>
              <AuthHeader
                title="استعادة كلمة المرور"
                subtitle="أدخل بريدك الإلكتروني لاستعادة كلمة المرور"
              />
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>خطأ</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <PasswordResetForm onBackToLogin={handleBackToLogin} />
            </>
          ) : success ? (
            <div className="text-center space-y-4">
              <div className="rounded-full bg-green-50 p-3 w-20 h-20 mx-auto flex items-center justify-center">
                <Check className="h-10 w-10 text-green-500" />
              </div>
              <h2 className="text-2xl font-semibold">تم تغيير كلمة المرور بنجاح</h2>
              <p className="text-muted-foreground">
                تم تغيير كلمة المرور الخاصة بك بنجاح. ستتم إعادة توجيهك إلى صفحة تسجيل الدخول.
              </p>
              <Button 
                className="w-full mt-4" 
                onClick={() => navigate('/login')}
              >
                العودة إلى تسجيل الدخول
              </Button>
            </div>
          ) : (
            <>
              <AuthHeader
                title="تعيين كلمة مرور جديدة"
                subtitle="يرجى إدخال كلمة المرور الجديدة وتأكيدها"
              />
              
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>خطأ</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <Form {...passwordForm}>
                <form onSubmit={passwordForm.handleSubmit(handlePasswordReset)} className="space-y-6">
                  <FormField
                    control={passwordForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>كلمة المرور الجديدة</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder="********"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-2 top-1/2 -translate-y-1/2"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                              <span className="sr-only">عرض كلمة المرور</span>
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={passwordForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>تأكيد كلمة المرور</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showConfirmPassword ? "text" : "password"}
                              placeholder="********"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-2 top-1/2 -translate-y-1/2"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                              <span className="sr-only">عرض كلمة المرور</span>
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="space-y-2">
                    <Button 
                      className="w-full" 
                      type="submit"
                      disabled={isSettingNewPassword}
                    >
                      {isSettingNewPassword ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري المعالجة...
                        </>
                      ) : 'تغيير كلمة المرور'}
                    </Button>
                    <Button 
                      variant="outline" 
                      className="w-full" 
                      type="button"
                      onClick={handleBackToLogin}
                      disabled={isSettingNewPassword}
                    >
                      العودة إلى تسجيل الدخول
                    </Button>
                  </div>
                </form>
              </Form>
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
