
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import MainLayout from '@/layouts/MainLayout';
import { Loader2, Check, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

export default function VerificationHandler() {
  const [isVerifying, setIsVerifying] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const verifyToken = async () => {
      try {
        setIsVerifying(true);
        
        // Improved token extraction with detailed logging
        const extractToken = () => {
          // From query parameters
          const url = new URL(window.location.href);
          let token = url.searchParams.get('token') || url.searchParams.get('access_token');

          
          // From hash
          if (!token && window.location.hash) {
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            token = hashParams.get('access_token');
          }
          
          // From URL fragment
          if (!token) {
            const urlParts = window.location.href.split('#');
            if (urlParts.length > 1) {
              token = urlParts[1];
            }
          }
          
          if (!token) {
            console.error("No token found in URL");
          } else {
            console.log("Token found with length:", token.length);
          }
          
          return token;
        };

        const token = extractToken();
        const type = new URLSearchParams(location.search).get('type');
        
        if (!token) {
          throw new Error("لم يتم العثور على رمز التحقق");
        }

        if (type === 'recovery' || location.pathname.includes('reset-password')) {
          
          // Add timestamp to prevent caching issues
          const timestamp = new Date().getTime();
          navigate(`/reset-password?t=${timestamp}#${token}`);
          return;
        } else {
          console.log("Verifying email token");
          
          // Email verification
          const { data, error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'email',
          });

          if (error) {
            console.error("Verification error from Supabase:", error);
            throw error;
          }
          setSuccess(true);
          toast.success("تم التحقق بنجاح", {
            description: "تم التحقق من بريدك الإلكتروني بنجاح."
          });
          
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        }
      } catch (err) {
        console.error("Verification error:", err);
        setError(err.message || "حدث خطأ أثناء عملية التحقق");
        toast.error("فشل التحقق", {
          description: err.message || "حدث خطأ أثناء عملية التحقق"
        });
      } finally {
        setIsVerifying(false);
      }
    };

    verifyToken();
  }, [location, navigate]);

  return (
    <MainLayout>
      <div className="container relative flex pt-10 flex-col items-center justify-center lg:px-0">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          {isVerifying ? (
            <div className="text-center p-6">
              <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
              <h3 className="text-xl font-medium">جاري التحقق...</h3>
              <p className="text-sm text-muted-foreground mt-2">
                يرجى الانتظار بينما نقوم بمعالجة طلبك.
              </p>
            </div>
          ) : success ? (
            <div className="text-center space-y-4">
              <div className="rounded-full bg-green-50 p-3 w-20 h-20 mx-auto flex items-center justify-center">
                <Check className="h-10 w-10 text-green-500" />
              </div>
              <h2 className="text-2xl font-semibold">تم التحقق بنجاح</h2>
              <p className="text-muted-foreground">
                تمت عملية التحقق بنجاح. ستتم إعادة توجيهك إلى صفحة تسجيل الدخول.
              </p>
              <Button 
                className="w-full mt-4" 
                onClick={() => navigate('/login')}
              >
                العودة إلى تسجيل الدخول
              </Button>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>خطأ في التحقق</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
              <Button 
                className="w-full mt-4" 
                onClick={() => navigate('/login')}
              >
                العودة إلى تسجيل الدخول
              </Button>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
