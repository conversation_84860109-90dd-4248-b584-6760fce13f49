import OTPVerification from "@/components/auth/OTPVerification";
import { useNavigate } from "react-router-dom";

export default function OTPVerificationPage() {
  const navigate = useNavigate();
  const email = sessionStorage.getItem('email');
  const mockProps = {
    email: email,
    type: "recovery" as const,
    onVerified: () => console.log("OTP verified"),
    onBackToLogin: () => window.history.back(),
    onSubmit: (otp: string) => console.log("OTP submitted:", otp),
    onResend: () => console.log("Resend OTP"),
    onBack: () => navigate("/login"),
    isLoading: false,
    error: null,
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <OTPVerification {...mockProps} />
    </div>
  );
}