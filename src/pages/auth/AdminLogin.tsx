
import React from 'react';
import AuthHeader from '@/components/auth/AuthHeader';
import AdminLoginForm from '@/components/auth/AdminLoginForm';
import { Card, CardContent } from '@/components/ui/card';
import LoginFooter from '@/components/auth/LoginFooter';

export default function AdminLogin() {
  return (
    <div className="flex flex-col min-h-screen bg-[#f8f9fa]">
      <AuthHeader 
        title="تسجيل دخول الإدارة" 
        subtitle="منطقة محمية، يرجى تسجيل الدخول للوصول لوحة التحكم" 
      />
      <main className="flex-1 flex items-center justify-center py-10 px-4">
        <Card className="w-full max-w-lg shadow-lg">
          <CardContent className="p-6">
            <AdminLoginForm />
          </CardContent>
        </Card>
      </main>
      <LoginFooter />
    </div>
  );
}
