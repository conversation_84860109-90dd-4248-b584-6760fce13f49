
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import MainLayout from '@/layouts/MainLayout';
import { Mail, ArrowLeft, RefreshCw, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/components/ui/use-toast';

export default function VerificationPending() {
  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';
  const { toast } = useToast();
  const { resendVerificationEmail } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Reset cooldown timer on unmount
  useEffect(() => {
    return () => setResendCooldown(0);
  }, []);

  const handleResendVerification = async () => {
    // Prevent resending if already in progress or in cooldown
    if (isResending || resendCooldown > 0) return;
    
    setIsResending(true);
    
    try {
      
      // Use the resendVerificationEmail function from auth context
      const success = await resendVerificationEmail(email);
      
      if (success) {
        // Set cooldown period to 60 seconds
        setResendCooldown(60);
        
        // Start the countdown
        const countdownInterval = setInterval(() => {
          setResendCooldown((prevTime) => {
            if (prevTime <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prevTime - 1;
          });
        }, 1000);
      }
    } catch (error) {
      console.error("Failed to resend verification email:", error);
    } finally {
      setIsResending(false);
    }
  };

  // Format seconds to MM:SS format
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <MainLayout>
      <div className="container flex flex-col items-center justify-center py-20 text-center">
        <div className="mx-auto flex max-w-[400px] flex-col items-center justify-center space-y-4">
          <div className="rounded-full bg-blue-100 p-3">
            <Mail className="h-10 w-10 text-blue-600" />
          </div>
          
          <h1 className="text-2xl font-bold">تحقق من بريدك الإلكتروني</h1>
          
          <p className="text-muted-foreground">
            لقد أرسلنا رسالة تأكيد إلى
            <span className="block font-medium mt-1">{email}</span>
            انقر على الرابط في الرسالة لتأكيد بريدك الإلكتروني.
          </p>

          <div className="mt-4 text-sm text-muted-foreground">
            <p>لم تصلك الرسالة؟ تحقق من مجلد البريد العشوائي أو الرسائل غير المرغوب فيها.</p>
          </div>

          <div className="flex flex-col gap-2 w-full mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleResendVerification}
              disabled={isResending || resendCooldown > 0}
              className="w-full flex items-center justify-center gap-2"
            >
              {isResending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  جاري إعادة إرسال الرابط...
                </>
              ) : resendCooldown > 0 ? (
                <>
                  <RefreshCw className="h-4 w-4" />
                  إعادة إرسال الرابط ({formatTime(resendCooldown)})
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  إعادة إرسال رابط التحقق
                </>
              )}
            </Button>

            <Button 
              variant="outline" 
              onClick={() => navigate('/login')} 
              className="w-full flex items-center justify-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              العودة إلى تسجيل الدخول
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
