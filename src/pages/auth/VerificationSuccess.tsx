
import React, { useEffect } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import MainLayout from '@/layouts/MainLayout';
import { useAuth } from '@/contexts/auth';
import { MailCheck, Check } from 'lucide-react';

export default function VerificationSuccess() {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Redirect to dashboard if already authenticated after a short delay
  useEffect(() => {
    if (isAuthenticated) {
      const timer = setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, navigate]);

  return (
    <MainLayout>
      <div className="container flex flex-col items-center justify-center py-20 text-center">
        <div className="mx-auto flex max-w-[400px] flex-col items-center justify-center space-y-4">
          <div className="rounded-full bg-green-100 p-3">
            <MailCheck className="h-10 w-10 text-green-600" />
          </div>
          
          <h1 className="text-2xl font-bold">تم التحقق من البريد الإلكتروني</h1>
          
          <p className="text-muted-foreground">
            تم التحقق من بريدك الإلكتروني بنجاح! يمكنك الآن استخدام جميع ميزات منصة رفوف.
          </p>

          <div className="flex flex-col gap-2 w-full mt-4">
            {isAuthenticated ? (
              <div className="flex flex-col items-center gap-4">
                <div className="flex items-center gap-2 text-green-600">
                  <Check className="h-5 w-5" />
                  <span>سيتم تحويلك إلى لوحة التحكم خلال لحظات...</span>
                </div>
                <Button onClick={() => navigate('/dashboard')} className="w-full">
                  الذهاب إلى لوحة التحكم
                </Button>
              </div>
            ) : (
              <Button onClick={() => navigate('/login')} className="w-full">
                تسجيل الدخول
              </Button>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
