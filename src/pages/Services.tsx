
import React from 'react';
import { MainLayout } from '@/layouts/MainLayout';

export default function Services() {
  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8 text-center">خدماتنا</h1>
        
        <div className="max-w-3xl mx-auto">
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">استضافة المنتجات</h2>
            <p className="text-gray-700 mb-4">
              نوفر خدمة استضافة المنتجات للتجار والشركات الصغيرة والمتوسطة،
              حيث يمكنك وضع منتجاتك على أرفف متاجرنا المنتشرة في جميع أنحاء المملكة.
            </p>
            <p className="text-gray-700">
              يمكنك متابعة مبيعات منتجاتك بشكل مباشر من خلال لوحة التحكم الخاصة بك،
              والحصول على تقارير مفصلة عن أداء منتجاتك في كل متجر.
            </p>
          </div>
          
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">إدارة المتاجر</h2>
            <p className="text-gray-700 mb-4">
              نقدم خدمات إدارة المتاجر بشكل كامل، بدءًا من إدارة المخزون وحتى إدارة الموظفين
              والمبيعات والتقارير المالية.
            </p>
            <p className="text-gray-700">
              توفر منصتنا أدوات متكاملة لإدارة متجرك بكفاءة عالية وتحسين أداء مبيعاتك.
            </p>
          </div>
          
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">التجارة الإلكترونية</h2>
            <p className="text-gray-700 mb-4">
              أطلق متجرك الإلكتروني مع منصتنا المتكاملة للتجارة الإلكترونية،
              واستفد من أدوات التسويق والمبيعات المتقدمة لزيادة مبيعاتك عبر الإنترنت.
            </p>
            <p className="text-gray-700">
              نوفر حلولًا متكاملة للدفع الإلكتروني والشحن والتوصيل لتسهيل تجربة التسوق
              لعملائك وزيادة معدلات التحويل.
            </p>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
