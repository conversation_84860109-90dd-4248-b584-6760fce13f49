import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import OnboardingContainer from '@/components/onboarding/OnboardingContainer';

const Onboarding = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [userType, setUserType] = useState<'store' | 'ecommerce' | null>(null);

  useEffect(() => {
    // Redirect if user is not logged in
    if (!user) {
      navigate('/login');
      return;
    }

    // Determine user type based on role
    if (user.role === 'store') {
      setUserType('store');
    } else if (user.role === 'ecommerce') {
      setUserType('ecommerce');
    } else {
      // If user is not a store or shop owner, redirect to dashboard
      toast({
        title: 'خطأ في الوصول',
        description: 'هذه الصفحة مخصصة لأصحاب المتاجر والمحلات فقط.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [user, navigate, toast]);

  if (!userType) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>جاري التحميل...</CardTitle>
            <CardDescription>
              جاري تحضير تجربة الإعداد المخصصة لك
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <OnboardingContainer userType={userType} />;
};

export default Onboarding;
