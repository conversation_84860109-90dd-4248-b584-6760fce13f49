import React from 'react';
import { MainLayout } from '@/layouts/MainLayout';
import { Footer } from '@/components/home/<USER>';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { toast } from '@/hooks/toast';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { 
  Mail, 
  MapPin, 
  Phone,
  Send,
  Loader2
} from 'lucide-react';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from "@/integrations/supabase/client";

const contactFormSchema = z.object({
  name: z.string().min(2, "الاسم يجب أن يكون أطول من حرفين"),
  email: z.string().email("يرجى إدخال بريد إلكتروني صحيح"),
  subject: z.string().min(3, "الموضوع يجب أن يكون أطول من 3 أحرف"),
  message: z.string().min(10, "الرسالة يجب أن تكون أطول من 10 أحرف")
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

const Contact = () => {
  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: ""
    }
  });

  const { isSubmitting } = form.formState;

  const onSubmit = async (data: ContactFormValues) => {
    try {
      // Send form data to admin via email
      const { error } = await supabase.functions.invoke('send-admin-email', {
        body: { 
          name: data.name, 
          email: data.email, 
          subject: data.subject, 
          message: data.message 
        }
      });
      
      if (error) throw error;
      
      console.log('Contact form submitted with data:', data);
      
      toast({
        title: "تم إرسال رسالتك بنجاح",
        description: "سنتواصل معك قريباً",
      });
      
      form.reset();
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast({
        variant: "destructive",
        title: "حدث خطأ",
        description: "لم نتمكن من إرسال رسالتك. يرجى المحاولة مرة أخرى.",
      });
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-8 text-center">تواصل معنا</h1>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div className="order-2 md:order-1">
              <h2 className="text-2xl font-semibold mb-6">معلومات الاتصال</h2>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-rfof-blue ml-4 mt-1 shrink-0" />
                  <div>
                    <h3 className="font-medium text-lg">العنوان</h3>
                    <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                    <p className="text-gray-600">حي الملقا، طريق أنس بن مالك</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Mail className="h-6 w-6 text-rfof-blue ml-4 mt-1 shrink-0" />
                  <div>
                    <h3 className="font-medium text-lg">البريد الإلكتروني</h3>
                    <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-rfof-blue"><EMAIL></a>
                    <br />
                    <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-rfof-blue"><EMAIL></a>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Phone className="h-6 w-6 text-rfof-blue ml-4 mt-1 shrink-0" />
                  <div>
                    <h3 className="font-medium text-lg">الهاتف</h3>
                    <a href="tel:+966123456789" className="text-gray-600 hover:text-rfof-blue">+966 12 345 6789</a>
                  </div>
                </div>
              </div>
              
              <div className="mt-10">
                <h2 className="text-2xl font-semibold mb-6">ساعات العمل</h2>
                <p className="text-gray-600">الأحد - الخميس: 9:00 ص - 5:00 م</p>
                <p className="text-gray-600">الجمعة - السبت: مغلق</p>
              </div>
            </div>
            
            <div className="order-1 md:order-2">
              <h2 className="text-2xl font-semibold mb-6">أرسل لنا رسالة</h2>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الاسم</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل اسمك الكامل" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="بريدك الإلكتروني" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الموضوع</FormLabel>
                        <FormControl>
                          <Input placeholder="موضوع الرسالة" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الرسالة</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="اكتب رسالتك هنا" 
                            className="min-h-[150px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4 ml-2" />
                    )}
                    إرسال الرسالة
                  </Button>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </MainLayout>
  );
};

export default Contact;
