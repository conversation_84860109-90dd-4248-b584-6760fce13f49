
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { 
  Bell,
  CheckCircle,
  AlertCircle,
  InfoIcon,
  MailWarning,
  CheckCheck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useNotifications } from '@/contexts/NotificationContext';
import { Notification } from '@/types';
import EmptyState from '@/components/EmptyState';
import { toast } from '@/hooks/toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';

function NotificationItem({ notification, onMarkAsRead }: { notification: Notification; onMarkAsRead: (id: string) => Promise<void> }) {
  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <MailWarning className="h-5 w-5 text-amber-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-destructive" />;
      case 'info':
      default:
        return <InfoIcon className="h-5 w-5 text-primary" />;
    }
  };

  const handleMarkAsRead = async () => {
    if (!notification.read) {
      await onMarkAsRead(notification.id);
    }
  };

  return (
    <div 
      className={`border-b p-4 hover:bg-muted/10 transition-colors ${notification.read ? 'bg-background' : 'bg-muted/20'}`}
      onClick={handleMarkAsRead}
    >
      <div className="flex items-start gap-3">
        <div className="mt-1">{getIcon()}</div>
        <div className="flex-1">
          <h4 className={`font-medium ${notification.read ? '' : 'text-primary'}`}>
            {notification.title}
          </h4>
          <p className="text-sm text-muted-foreground mt-1">
            {notification.message}
          </p>
          <div className="flex justify-between items-center mt-2">
            <span className="text-xs text-muted-foreground">
              {new Date(notification.created_at).toLocaleDateString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </span>
            <div className="flex gap-2">
              {!notification.read && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 px-2 text-xs flex items-center gap-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    onMarkAsRead(notification.id);
                  }}
                >
                  <CheckCheck className="h-3.5 w-3.5 mr-1" />
                  تعيين كمقروء
                </Button>
              )}
              {notification.link && (
                <Button 
                  variant="link" 
                  size="sm" 
                  className="h-auto p-0 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.location.href = notification.link!;
                  }}
                >
                  عرض التفاصيل
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Notifications() {
  const { userNotifications, markAsRead, markAllAsRead, isLoading } = useNotifications();
  const [activeTab, setActiveTab] = useState<string>("all");
  const hasUnread = userNotifications.some(notification => !notification.read);
  
  // Filter notifications based on active tab
  const filteredNotifications = userNotifications.filter(notification => {
    if (activeTab === "all") return true;
    if (activeTab === "unread") return !notification.read;
    if (activeTab === "read") return notification.read;
    return true;
  });

  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsRead(id);
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تعيين الإشعار كمقروء",
        variant: "destructive",
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
      toast({
        title: "تم",
        description: "تم تعيين جميع الإشعارات كمقروءة",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تعيين الإشعارات كمقروءة",
        variant: "destructive",
      });
    }
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="الإشعارات"
        description="آخر التحديثات والإشعارات الخاصة بك"
        icon={Bell}
        actions={
          hasUnread && (
            <Button variant="outline" onClick={handleMarkAllAsRead}>
              <CheckCheck className="h-4 w-4 mr-1" />
              تعيين الكل كمقروء
            </Button>
          )
        }
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {userNotifications.length === 0 ? (
            <EmptyState
              title="لا توجد إشعارات"
              description="ليس لديك أي إشعارات في الوقت الحالي"
              icon={Bell}
            />
          ) : (
            <Card className="mt-6">
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <div className="border-b px-4">
                  <TabsList className="mt-6">
                    <TabsTrigger value="all">الكل ({userNotifications.length})</TabsTrigger>
                    <TabsTrigger value="unread">غير مقروءة ({userNotifications.filter(n => !n.read).length})</TabsTrigger>
                    <TabsTrigger value="read">مقروءة ({userNotifications.filter(n => n.read).length})</TabsTrigger>
                  </TabsList>
                </div>
                
                <TabsContent value="all" className="mt-0">
                  <ScrollArea className="h-[calc(100vh-20rem)]">
                    <div className="border-0 rounded-md">
                      {filteredNotifications.map((notification) => (
                        <NotificationItem 
                          key={notification.id} 
                          notification={notification}
                          onMarkAsRead={handleMarkAsRead}
                        />
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="unread" className="mt-0">
                  <ScrollArea className="h-[calc(100vh-20rem)]">
                    <div className="border-0 rounded-md">
                      {filteredNotifications.map((notification) => (
                        <NotificationItem 
                          key={notification.id} 
                          notification={notification}
                          onMarkAsRead={handleMarkAsRead}
                        />
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="read" className="mt-0">
                  <ScrollArea className="h-[calc(100vh-20rem)]">
                    <div className="border-0 rounded-md">
                      {filteredNotifications.map((notification) => (
                        <NotificationItem 
                          key={notification.id} 
                          notification={notification}
                          onMarkAsRead={handleMarkAsRead}
                        />
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </Card>
          )}
        </>
      )}
    </DashboardLayout>
  );
}
