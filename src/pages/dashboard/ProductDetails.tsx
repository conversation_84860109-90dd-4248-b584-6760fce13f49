
import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card, CardContent } from '@/components/ui/card';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useProductOperations } from '@/hooks/product/useProductOperations';
import { Product } from '@/types';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loading } from '@/components/ui/loading';
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from '@/components/ui/carousel';
import { ArrowLeft, Edit, Trash2, Package } from 'lucide-react';
import { formatPrice } from '@/utils/formatters';
import { toast } from 'sonner';
import { useProducts } from '@/contexts/ProductContext';
import {DeleteProductDialog}  from '@/components/products/DeleteProductDialog';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {supabase} from "@/integrations/supabase/client.ts";

export default function ProductDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getProductById } = useProductOperations();
  const { deleteProduct } = useProducts();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    async function loadProduct() {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const { data : productData , error } :{
          data: Product | null;
          error: Error | null;
        } = await supabase.from('products').select('*').eq('id', id).single();
        console.log('Product data:', productData);
        setProduct(productData);
        
        if (!productData) {
          setError('لم يتم العثور على المنتج');
        }
      } catch (err) {
        setError(err.message || 'حدث خطأ أثناء تحميل المنتج');
        console.error('Error loading product details:', err);
      } finally {
        setIsLoading(false);
      }
    }
    
    loadProduct();
  }, [id]);

  const handleDelete = async () => {
    if (!product) return;
    
    setIsDeleting(true);
    try {
      const success = await deleteProduct(product.id);
      if (success) {
        toast.success('تم حذف المنتج بنجاح');
        navigate('/dashboard/products');
      }
    } catch (err) {
      console.error('Error deleting product:', err);
      toast.error('حدث خطأ أثناء حذف المنتج');
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Loading text="جاري تحميل بيانات المنتج..." />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !product) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Package className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">لم يتم العثور على المنتج</h2>
          <p className="text-muted-foreground mb-6">{error || 'المنتج غير موجود أو تم حذفه'}</p>
          <Button asChild>
            <Link to="/dashboard/products">العودة إلى المنتجات</Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader 
        title={product.name}
        description="تفاصيل المنتج"
        actions={
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => navigate('/dashboard/products')}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
            <Button 
              variant="outline" 
              asChild
              className="flex items-center"
            >
              <Link to={`/dashboard/products/edit/${product.id}`}>
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Link>
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => setShowDeleteDialog(true)}
              disabled={isDeleting}
              className="flex items-center"
            >
              <Trash2 className="h-4 w-4 ml-2" />
              {isDeleting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardContent className="p-6">
            {product.images && product.images.length > 0 ? (
              <Carousel className="w-full">
                <CarouselContent>
                  {product.images.map((image, index) => (
                    <CarouselItem key={index}>
                      <AspectRatio ratio={16/9}>
                        <img 
                          src={image} 
                          alt={`${product.name} - صورة ${index + 1}`} 
                          className="w-full h-full object-contain rounded-md" 
                        />
                      </AspectRatio>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious />
                <CarouselNext />
              </Carousel>
            ) : (
              <div className="bg-muted flex items-center justify-center h-64 rounded-md">
                <Package className="h-16 w-16 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold">{product.name}</h2>
            <div className="flex items-center mt-2">
              <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                {product.status || 'active'}
              </Badge>
              <span className="text-2xl font-bold text-primary ml-auto">{formatPrice(product.price)}</span>
            </div>

            <Separator className="my-4" />

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">الوصف</h3>
                <p className="mt-1">{product.description || 'لا يوجد وصف لهذا المنتج'}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">الكمية المتاحة</h3>
                  <p className="mt-1">{product.quantity ?? product.stock ?? 0}</p>
                </div>

                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">التصنيف</h3>
                  <p className="mt-1">{product.category || 'بدون تصنيف'}</p>
                </div>
                
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">البائع</h3>
                  <p className="mt-1">{product.seller_name || product.store_name || 'غير معروف'}</p>
                </div>
                
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">تاريخ الإضافة</h3>
                  <p className="mt-1">{new Date(product.created_at).toLocaleDateString('ar-SA')}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <DeleteProductDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDelete}
      />
    </DashboardLayout>
  );
}