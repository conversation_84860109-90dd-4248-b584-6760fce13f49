import React, { useEffect, useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import { User } from "@/types";
import AvatarUpload from "@/components/AvatarUpload";
import { Switch } from "@/components/ui/switch";
import { useTheme } from "@/contexts/ThemeContext";
import { MoonIcon, SunIcon, MonitorIcon } from "lucide-react";

const Settings = () => {
  const { user, updateProfile } = useAuth();

  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<User>>(
    user
      ? {
          name: user.name,
          email: user.email,
          phone_number: user.phone_number || "",
          address: user.address || "",
          bank_account: user.bank_account || "",
          legal_activity: user.legal_activity || "",
          iban: user?.iban || "",
        }
      : {}
  );
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        phone_number: user.phone_number || "",
        address: user.address || "",
        bank_account: user.bank_account || "",
        legal_activity: user.legal_activity || "",
        iban: user?.iban || "",
      });
    }
  }, [user]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!user) {
        throw new Error("User not found");
      }

      const updates: Partial<User> = {};

      if (formData.name !== user.name) updates.name = formData.name || "";
      if (formData.phone_number !== user.phone_number)
        updates.phone_number = formData.phone_number || "";
      if (formData.address !== user.address)
        updates.address = formData.address || "";
      if (formData.bank_account !== user.bank_account)
        updates.bank_account = formData.bank_account || "";
      if (formData.iban !== user.iban) updates.iban = formData.iban || "";
      if (formData.legal_activity !== user.legal_activity)
        updates.legal_activity = formData.legal_activity || "";

      console.log("Updating user profile with:", updates);

      // Only update if there are changes
      if (Object.keys(updates).length > 0) {
        if (updateProfile) {
          const success = await updateProfile({
            ...updates,
            id: user.id,
          });
          if (success) {
            toast.success("تم تحديث الملف الشخصي بنجاح");
          } else {
            toast.error("فشل تحديث الملف الشخصي");
            console.error("Profile update returned false");
          }
        } else {
          toast.error("وظيفة تحديث الملف الشخصي غير متوفرة");
          console.error("updateProfile function is not available");
        }
      } else {
        toast.info("لا توجد تغييرات للحفظ");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(`فشل تحديث الملف الشخصي: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarChange = (url: string) => {
    if (updateProfile) {
      updateProfile({ avatar: url, id: user?.id })
        .then((success) => {
          if (success) {
            toast.success("تم تحديث الصورة الشخصية بنجاح");
          }
        })
        .catch((error) => {
          console.error("Error updating avatar:", error);
          toast.error("فشل تحديث الصورة الشخصية");
        });
    }
  };

  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    setTheme(newTheme);
    const themeNames = {
      light: "الفاتح",
      dark: "المظلم",
      system: "النظام",
    };
    toast.success(`تم التبديل إلى الوضع ${themeNames[newTheme]}`);
  };

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <p>يرجى تسجيل الدخول لعرض الإعدادات</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="الإعدادات"
        description="تحديث ملفك الشخصي وإعدادات الحساب"
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>الصورة الشخصية</CardTitle>
            <CardDescription>
              قم بتحديث صورتك الشخصية التي ستظهر للآخرين
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <AvatarUpload
              initialAvatar={user.avatar || ""}
              onAvatarChange={handleAvatarChange}
            />
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>إعدادات الواجهة</CardTitle>
            <CardDescription>تخصيص مظهر التطبيق حسب تفضيلاتك</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <SunIcon className="h-5 w-5 ml-2 text-yellow-500" />
                  <div>
                    <p className="font-medium">الوضع الفاتح</p>
                    <p className="text-xs text-muted-foreground">
                      سطوع عالي، مناسب للاستخدام النهاري
                    </p>
                  </div>
                </div>
                <Switch
                  checked={theme === "light"}
                  onCheckedChange={() => handleThemeChange("light")}
                  aria-label="تبديل الوضع الفاتح"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <MoonIcon className="h-5 w-5 ml-2 text-blue-700 dark:text-blue-400" />
                  <div>
                    <p className="font-medium">الوضع المظلم</p>
                    <p className="text-xs text-muted-foreground">
                      سطوع منخفض، مناسب للاستخدام الليلي
                    </p>
                  </div>
                </div>
                <Switch
                  checked={theme === "dark"}
                  onCheckedChange={() => handleThemeChange("dark")}
                  aria-label="تبديل الوضع المظلم"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <MonitorIcon className="h-5 w-5 ml-2 text-gray-500" />
                  <div>
                    <p className="font-medium">وضع النظام</p>
                    <p className="text-xs text-muted-foreground">
                      استخدام إعدادات جهازك للوضع الفاتح/المظلم
                    </p>
                  </div>
                </div>
                <Switch
                  checked={theme === "system"}
                  onCheckedChange={() => handleThemeChange("system")}
                  aria-label="تبديل وضع النظام"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-3">
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>المعلومات الشخصية</CardTitle>
              <CardDescription>
                تحديث معلوماتك الشخصية وكيفية الاتصال بك
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">الاسم</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name || ""}
                    onChange={handleChange}
                    placeholder="اسمك"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    name="email"
                    value={formData.email || ""}
                    onChange={handleChange}
                    readOnly
                    disabled
                    placeholder="بريدك الإلكتروني"
                  />
                  <p className="text-xs text-muted-foreground">
                    لا يمكن تغيير البريد الإلكتروني
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">رقم الهاتف</Label>
                  <Input
                    id="phoneNumber"
                    name="phone_number"
                    value={formData.phone_number || ""}
                    onChange={handleChange}
                    placeholder="رقم هاتفك"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bankAccount">الحساب البنكي</Label>
                  <Input
                    id="bankAccount"
                    name="bank_account"
                    value={formData.bank_account || ""}
                    onChange={handleChange}
                    placeholder="رقم الحساب البنكي"
                  />
                </div>
              </div>
              <div className="space-y-2 w-full">
                <Label htmlFor="iban">رقم IBAN</Label>
                <Input
                  id="iban"
                  name="iban"
                  value={formData.iban || ""}
                  onChange={handleChange}
                  placeholder="************************"
                  dir="ltr"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">العنوان</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address || ""}
                  onChange={handleChange}
                  placeholder="عنوانك"
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="legal_activity">النشاط التجاري</Label>
                <Textarea
                  id="legal_activity"
                  name="legal_activity"
                  value={formData.legal_activity || ""}
                  onChange={handleChange}
                  placeholder="وصف النشاط التجاري"
                  rows={3}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "جاري الحفظ..." : "حفظ التغييرات"}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
