import React, {useEffect, useState} from "react";
import {DashboardLayout} from "@/layouts/DashboardLayout";
import {PageHeader} from "@/components/ui/page-header";
import {HostingProduct} from "@/types";
import {Card, CardContent, CardDescription, CardHeader, CardTitle,} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/components/ui/table";
import {Button} from "@/components/ui/button";
import {Loading} from "@/components/ui/loading";
import {AlertCircle, Package, RefreshCw, Search} from "lucide-react";
import {useStoreInventory} from "@/hooks/query/useStoreInventory";

const StoreInventory: React.FC = () => {
  // const [products, setProducts] = useState<Partial<Product>[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<
    Partial<{ seller_name: string } & HostingProduct>[]
  >([]);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const {
    products,
    isLoading,
    error,
    refetch,
    isMutating: isUpdating,
    updateProduct,
  } = useStoreInventory();

  useEffect(() => {
    if (isLoading || !products?.length ) return;
    if (products) {
      setFilteredProducts(products);
    }
  }, [products, isLoading]);

  const handleQuantityChange = async (
    productId: string,
    newQuantity: number
  ) => {
    if (newQuantity < 0) return;
    updateProduct(productId, newQuantity);
  };

  const refetchPage = () => {
    refetch();
  };
  console.log(error)
  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <PageHeader
          title="مخزون المنتجات"
          description="إدارة مخزون المنتجات في محلك التجاري"
          icon={Package}
          actions={
            <Button
              onClick={refetchPage}
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
          }
        />

        {/* بحث عن المنتجات */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="ابحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="h-[60vh] flex items-center justify-center">
            <Loading text="جاري تحميل بيانات المخزون..." size="md" />
          </div>
        ) : error ? (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center p-4">
                <AlertCircle className="h-12 w-12 text-destructive mb-2" />
                <h3 className="text-xl font-semibold mb-2">
                  حدث خطأ أثناء تحميل المخزون
                </h3>
                <p className="text-muted-foreground mb-4">{error.message}</p>
                <Button onClick={refetchPage} variant="outline">
                  <RefreshCw className="h-4 w-4 ml-2" />
                  إعادة المحاولة
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : filteredProducts.length === 0 ? (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center p-6">
                <Package className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="text-xl font-semibold mb-2">
                  لا توجد منتجات في المخزون
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery.trim() !== ""
                    ? "لا توجد منتجات تطابق بحثك."
                    : "لا توجد منتجات في محلك التجاري حالياً."}
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>قائمة المنتجات</CardTitle>
              <CardDescription>
                يمكنك تحديث كمية المنتجات المتوفرة في المخزون
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table className="text-center">
                <TableHeader>
                  <TableRow>
                    <TableHead>المنتج</TableHead>
                    <TableHead>البائع</TableHead>
                    <TableHead>السعر</TableHead>
                    <TableHead>الكمية المتوفرة</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="flex items-center gap-3">
                        {product.image ? (
                          <a
                            href={product.image}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <img
                              src={product.image}
                              alt={product.name}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          </a>
                        ) : (
                          <div className="h-10 w-10 rounded-md bg-muted flex items-center justify-center">
                            <Package className="h-5 w-5" />
                          </div>
                        )}
                        <span>{product.name}</span>
                      </TableCell>
                      <TableCell>{product.seller_name}</TableCell>
                      <TableCell>{product.price} ريال</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 max-w-[150px]">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() =>
                              handleQuantityChange(
                                product.id,
                                Math.max(0, product.quantity - 1)
                              )
                            }
                            disabled={
                              isUpdating[product.id] || product.quantity <= 0
                            }
                          >
                            -
                          </Button>
                          <Input
                            className="h-8 text-center"
                            type="number"
                            min="0"
                            value={product.quantity}
                            max={product.quantity}
                            onChange={(e) => {
                              const value = parseInt(e.target.value);
                              if (!isNaN(value) && value >= 0) {
                                handleQuantityChange(product.id, value);
                              }
                            }}
                            disabled={isUpdating[product.id]}
                          />
                          {/* <Button
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() =>
                              handleQuantityChange(
                                product.id,
                                product.quantity + 1
                              )
                            }
                            disabled={isUpdating[product.id]}
                          >
                            +
                          </Button> */}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant={
                            product.quantity === 0 ? "destructive" : "default"
                          }
                          size="sm"
                          onClick={() =>
                            handleQuantityChange(
                              product.id,
                              product.quantity === 0 ? 1 : 0
                            )
                          }
                          disabled={isUpdating[product.id]}
                        >
                          {product.quantity === 0
                            ? "إعادة للمخزون"
                            : "نفدت الكمية"}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default StoreInventory;
