import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { HostingRequestsList } from "@/components/hosting/HostingRequestsList";
import { useHostingRequests } from "@/hooks/hosting/useHostingRequests";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Store } from "lucide-react";

const HostingRequestsPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { requests, isLoading } = useHostingRequests();

  const handleCreateRequest = () => {
    navigate("/dashboard/stores");
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="طلبات العرض المرسلة"
        description={
          user?.role === "ecommerce"
            ? "إدارة طلبات عرض منتجاتك في المحلات التجارية"
            : "إدارة طلبات عرض المنتجات في محلك التجاري"
        }
        actions={
          user?.role === "ecommerce" ? (
            <Button onClick={handleCreateRequest} className="gap-2">
              <Store className="h-4 w-4" />
              استعراض المحلات التجارية
            </Button>
          ) : undefined
        }
      />

      <HostingRequestsList
        requests={requests}
        isLoading={isLoading}
        onCreateRequest={handleCreateRequest}
        userRole={user?.role}
      />
    </DashboardLayout>
  );
};

export default HostingRequestsPage;
