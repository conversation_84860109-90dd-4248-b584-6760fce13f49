import React, { useState, useEffect } from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Plus } from "lucide-react";
import { Transaction, TransactionType } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import { Loading } from "@/components/ui/loading";
import { useNavigate } from "react-router-dom";
import EmptyState from "@/components/EmptyState";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  useTransactionOperations,
  DepositParams,
} from "@/hooks/useTransactionOperations";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  formatCurrency,
  getTransactionStatusColor,
  getTransactionStatusText,
} from "@/lib/transactionUtils";

function Deposits() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [deposits, setDeposits] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [depositAmount, setDepositAmount] = useState("");
  const [depositReceipt, setDepositReceipt] = useState<File | null>(null);
  const [depositNotes, setDepositNotes] = useState("");
  const [depositReferenceNumber, setDepositReferenceNumber] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [depositDialogOpen, setDepositDialogOpen] = useState(false);
  const [platformNumber, setPlatformNumber] = useState("");
  const { depositMoney } = useTransactionOperations(user?.id, user?.name);

  // Fetch deposits
  useEffect(() => {
    const fetchDeposits = async () => {
      try {
        if (!user?.id) return;

        setIsLoading(true);
        const { data, error } = await supabase
          .from("transactions")
          .select("*")
          .eq("user_id", user.id)
          .eq("type", "deposit")
          .order("created_at", { ascending: false });

        if (error) throw error;

        setDeposits(data as Transaction[]);
      } catch (error) {
        console.error("Error fetching deposits:", error);
        toast.error("حدث خطأ أثناء جلب بيانات الإيداعات");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeposits();
  }, [user?.id]);

  // Fetch platform account number
  useEffect(() => {
    const fetchPlatformData = async () => {
      try {
        const { data, error } = await supabase
          .from("admin_public_data")
          .select("bank_account")
          .single();

        if (error) throw error;

        setPlatformNumber(data.bank_account || "");
      } catch (error) {
        console.error("Error fetching platform data:", error);
      }
    };

    fetchPlatformData();
  }, []);

  const handleDeposit = async () => {
    if (!validateAmount(depositAmount)) return;

    // Check if receipt image is provided
    if (!depositReceipt) {
      toast.error("إثبات الدفع مطلوب");
      return;
    }

    // Check if reference number is provided
    if (!depositReferenceNumber) {
      toast.error("رقم المرجع مطلوب");
      return;
    }

    setIsSubmitting(true);

    try {
      const depositParams: DepositParams = {
        amount: Number(depositAmount),
        description: depositNotes,
        receipts: [depositReceipt],
      };

      const success = await depositMoney(depositParams);

      if (success) {
        toast.success("تم إرسال طلب الإيداع بنجاح");
        // Add temporary transaction to the list until page refresh
        const newDeposit: Transaction = {
          id: `temp-${Date.now()}`,
          user_id: user?.id || "",
          user_name: user?.name || "",
          type: "deposit" as TransactionType,
          status: "pending",
          amount: Number(depositAmount),
          description: depositNotes || "طلب إيداع",
          reference: depositReferenceNumber,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        setDeposits([newDeposit, ...deposits]);

        setDepositDialogOpen(false);
        setDepositAmount("");
        setDepositReceipt(null);
        setDepositNotes("");
        setDepositReferenceNumber("");
      }
    } catch (error) {
      console.error("Error processing deposit:", error);
      toast.error("فشل في إرسال طلب الإيداع");
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateAmount = (amount: string): boolean => {
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error("الرجاء إدخال مبلغ صحيح");
      return false;
    }

    return true;
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("ar-SA", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6">
        <PageHeader
          title="الإيداعات"
          description="إدارة ومتابعة إيداعاتك المالية"
        />

        <Dialog open={depositDialogOpen} onOpenChange={setDepositDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 ml-2" />
              إيداع جديد
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>إيداع مبلغ</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4 max-h-[70vh] overflow-auto">
              <div className="p-4 bg-blue-50 rounded-lg mb-4">
                <h3 className="text-lg font-bold text-blue-800 mb-2">
                  معلومات التحويل
                </h3>
                <div className="flex justify-between flex-col gap-2">
                  <p className="text-sm text-blue-600">
                    رقم الحساب: {platformNumber || "جاري التحميل..."}
                  </p>
                  <p className="text-sm text-blue-600">
                    الوقت المتوقع: من 24 إلى 48 ساعة عمل
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="deposit-amount">المبلغ (ريال سعودي)</Label>
                <Input
                  id="deposit-amount"
                  placeholder="أدخل المبلغ"
                  type="number"
                  min={1}
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deposit-reference">
                  رقم المرجع <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="deposit-reference"
                  placeholder="أدخل رقم العملية أو المرجع"
                  type="text"
                  value={depositReferenceNumber}
                  onChange={(e) => setDepositReferenceNumber(e.target.value)}
                  required
                />
                {!depositReferenceNumber && (
                  <p className="text-xs text-red-500">
                    يجب إدخال رقم مرجعي للتحويل
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="deposit-receipt">
                  إثبات الدفع <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="deposit-receipt"
                  type="file"
                  accept="image/*"
                  onChange={(e) =>
                    setDepositReceipt(e.target.files ? e.target.files[0] : null)
                  }
                  required
                />
                {!depositReceipt && (
                  <p className="text-xs text-red-500">
                    يجب إرفاق صورة إثبات الدفع
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="deposit-notes">ملاحظات إضافية (اختياري)</Label>
                <Textarea
                  id="deposit-notes"
                  placeholder="أي معلومات إضافية عن عملية الإيداع"
                  value={depositNotes}
                  onChange={(e) => setDepositNotes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDepositDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button onClick={handleDeposit} disabled={isSubmitting}>
                {isSubmitting ? "جاري المعالجة..." : "تأكيد الإيداع"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate("/dashboard/wallet")}>
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة إلى المحفظة
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>سجل الإيداعات</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
              <TabsTrigger value="completed">مكتملة</TabsTrigger>
              <TabsTrigger value="rejected">مرفوضة</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              {deposits.length === 0 ? (
                <EmptyState
                  title="لا توجد إيداعات"
                  description="لم تقم بأي عمليات إيداع بعد"
                  action={{
                    label: "إضافة إيداع",
                    onClick: () => setDepositDialogOpen(true),
                  }}
                />
              ) : (
                <div className="space-y-4">
                  {deposits.map((deposit) => (
                    <Card key={deposit.id} className="overflow-hidden">
                      <div className="flex items-start p-6 justify-between">
                        <div>
                          <h3 className="font-medium">
                            إيداع {formatCurrency(deposit.amount)}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {deposit.description || "إيداع"}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDate(deposit.created_at)}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <span
                            className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                            style={{
                              backgroundColor: getTransactionStatusColor(
                                deposit.status
                              ),
                            }}
                          >
                            {getTransactionStatusText(deposit.status)}
                          </span>
                          {deposit.receipt_url && (
                            <Button
                              variant="outline"
                              size="sm"
                              asChild
                              className="text-xs"
                            >
                              <a
                                href={deposit.receipt_url}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                عرض الإيصال
                              </a>
                            </Button>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="pending">
              {deposits.filter((d) => d.status === "pending").length === 0 ? (
                <EmptyState
                  title="لا توجد إيداعات قيد الانتظار"
                  description="ليس لديك أي إيداعات قيد الانتظار حاليًا"
                />
              ) : (
                <div className="space-y-4">
                  {deposits
                    .filter((d) => d.status === "pending")
                    .map((deposit) => (
                      <Card key={deposit.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              إيداع {formatCurrency(deposit.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {deposit.description || "إيداع"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(deposit.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  deposit.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(deposit.status)}
                            </span>
                            {deposit.receipt_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                                className="text-xs"
                              >
                                <a
                                  href={deposit.receipt_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  عرض الإيصال
                                </a>
                              </Button>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="completed">
              {deposits.filter((d) => d.status === "completed").length === 0 ? (
                <EmptyState
                  title="لا توجد إيداعات مكتملة"
                  description="لم يتم اكتمال أي إيداعات بعد"
                />
              ) : (
                <div className="space-y-4">
                  {deposits
                    .filter((d) => d.status === "completed")
                    .map((deposit) => (
                      <Card key={deposit.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              إيداع {formatCurrency(deposit.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {deposit.description || "إيداع"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(deposit.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  deposit.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(deposit.status)}
                            </span>
                            {deposit.receipt_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                                className="text-xs"
                              >
                                <a
                                  href={deposit.receipt_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  عرض الإيصال
                                </a>
                              </Button>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="rejected">
              {deposits.filter((d) => d.status === "rejected").length === 0 ? (
                <EmptyState
                  title="لا توجد إيداعات مرفوضة"
                  description="لم يتم رفض أي إيداعات"
                />
              ) : (
                <div className="space-y-4">
                  {deposits
                    .filter((d) => d.status === "rejected")
                    .map((deposit) => (
                      <Card key={deposit.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              إيداع {formatCurrency(deposit.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {deposit.description || "إيداع"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(deposit.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  deposit.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(deposit.status)}
                            </span>
                            {deposit.receipt_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                                className="text-xs"
                              >
                                <a
                                  href={deposit.receipt_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  عرض الإيصال
                                </a>
                              </Button>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}

export default Deposits;
