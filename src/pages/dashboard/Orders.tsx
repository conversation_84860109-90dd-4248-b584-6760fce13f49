
import React, { useState } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/contexts/auth';
import { useOrders } from '@/hooks/useOrders';
import { OrdersList } from '@/components/orders/OrdersList';
import { OrderFilters } from '@/components/orders/OrderFilters';
import { OrderStatus } from '@/types';
import { ShoppingBag } from 'lucide-react';

const Orders = () => {
  const { user } = useAuth();
  const { orders, isLoading } = useOrders();
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const filteredOrders = selectedStatus
    ? orders.filter(order => order.status === selectedStatus as OrderStatus)
    : orders;

  // Sort orders by date
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
  });

  const handleFilterChange = (status: string | null) => {
    setSelectedStatus(status);
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="الطلبات"
        description={
          user?.role === 'ecommerce'
            ? "إدارة طلبات العملاء لمنتجاتك"
            : "إدارة طلبات المنتجات المستضافة في محلك التجاري"
        }
        icon={ShoppingBag}
      />

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle>قائمة الطلبات</CardTitle>
                <CardDescription>
                  عرض وإدارة جميع الطلبات
                </CardDescription>
              </div>
              <OrderFilters 
                selectedStatus={selectedStatus}
                sortOrder={sortOrder}
                onFilterChange={handleFilterChange}
                onSortOrderChange={toggleSortOrder}
              />
            </div>
          </CardHeader>
          <CardContent>
            <OrdersList 
              orders={sortedOrders} 
              isLoading={isLoading} 
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Orders;
