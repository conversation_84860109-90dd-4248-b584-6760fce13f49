import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  ArrowLeft, 
  Download, 
  Printer, 
  ExternalLink,
  CreditCard,
  Check,
  X 
} from 'lucide-react';
import { InvoiceRoadmap } from '@/components/invoices/InvoiceRoadmap';
import { InvoiceTransactions } from '@/components/invoices/InvoiceTransactions';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/auth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Loading } from '@/components/ui/loading';
import { InvoiceStage } from '@/types';

const InvoiceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [invoice, setInvoice] = useState(null);
  const [hostingRequest, setHostingRequest] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState([]);

  // Define the invoice stages
  const stages: InvoiceStage[] = [
    {
      id: 1,
      title: 'إنشاء الفاتورة',
      name: 'إنشاء الفاتورة',
      description: 'تم إنشاء الفاتورة وإرسالها للمتجر الإلكتروني',
      completed: true,
      current: false
    },
    {
      id: 2,
      title: 'في انتظار الدفع',
      name: 'في انتظار الدفع',
      description: 'في انتظار دفع قيمة الفاتورة من المتجر الإلكتروني',
      completed: false,
      current: true
    },
    {
      id: 3,
      title: 'تأكيد الدفع',
      name: 'تأكيد الدفع',
      description: 'تم تأكيد دفع قيمة الفاتورة',
      completed: false,
      current: false
    },
    {
      id: 4,
      title: 'إكمال الطلب',
      name: 'إكمال الطلب',
      description: 'تم إتمام الطلب وإغلاق الفاتورة',
      completed: false,
      current: false
    }
  ];

  useEffect(() => {
    if (!id || !user) return;

    const fetchInvoiceDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch invoice data
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('invoices')
          .select('*')
          .eq('id', id)
          .single();

        if (invoiceError) throw invoiceError;
        
        if (!invoiceData) {
          toast.error('الفاتورة غير موجودة');
          navigate('/dashboard/invoices');
          return;
        }

        setInvoice(invoiceData);

        // Fetch associated hosting request
        if (invoiceData.hosting_request_id) {
          const { data: hostingData, error: hostingError } = await supabase
            .from('hosting_requests')
            .select('*')
            .eq('id', invoiceData.hosting_request_id)
            .single();

          if (hostingError) throw hostingError;
          setHostingRequest(hostingData);

          // Fetch products for this hosting request
          const { data: productsData, error: productsError } = await supabase
            .from('hosting_products')
            .select('*')
            .eq('hosting_request_id', invoiceData.hosting_request_id);

          if (productsError) throw productsError;
          setProducts(productsData || []);
        }

      } catch (error) {
        console.error('Error fetching invoice details:', error);
        toast.error('خطأ في تحميل بيانات الفاتورة', {
          description: error.message
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceDetails();
  }, [id, user, navigate]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge className="bg-gray-50 text-gray-600 border-gray-200">مسودة</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-50 text-yellow-600 border-yellow-200">قيد الانتظار</Badge>;
      case 'paid':
        return <Badge className="bg-green-50 text-green-600 border-green-200">مدفوعة</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-50 text-red-600 border-red-200">ملغية</Badge>;
      default:
        return <Badge>غير معروفة</Badge>;
    }
  };

  const handlePrintInvoice = () => {
    window.print();
  };

  const handleDownloadInvoice = () => {
    // This would typically generate a PDF and trigger download
    toast.info('جاري إنشاء ملف PDF للفاتورة');
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="h-[60vh] flex items-center justify-center">
          <Loading size="lg" text="جاري تحميل بيانات الفاتورة..." />
        </div>
      </DashboardLayout>
    );
  }

  if (!invoice) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <FileText className="h-16 w-16 text-gray-300 mb-4" />
          <h2 className="text-2xl font-bold mb-4">الفاتورة غير موجودة</h2>
          <p className="text-muted-foreground mb-6">لم يتم العثور على الفاتورة المطلوبة</p>
          <Button onClick={() => navigate('/dashboard/invoices')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            العودة للفواتير
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="print:hidden">
        <PageHeader
          title="تفاصيل الفاتورة"
          description={`فاتورة رقم #${id?.slice(0, 8)}`}
          icon={FileText}
          actions={
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => navigate('/dashboard/invoices')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للفواتير
              </Button>
              <Button variant="outline" size="sm" onClick={handlePrintInvoice}>
                <Printer className="h-4 w-4 mr-2" />
                طباعة
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownloadInvoice}>
                <Download className="h-4 w-4 mr-2" />
                تحميل PDF
              </Button>
            </div>
          }
        />

        <div className="mb-6">
          <InvoiceRoadmap stages={stages} currentStage={2} />
        </div>
      </div>

      <div className="space-y-6">
        {/* Invoice Header */}
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="print:pb-2">
            <div className="flex flex-col md:flex-row justify-between">
              <div>
                <CardTitle className="text-xl font-bold">فاتورة استضافة منتجات</CardTitle>
                <p className="text-muted-foreground mt-1">رقم الفاتورة: {id?.slice(0, 8)}</p>
              </div>
              <div className="mt-4 md:mt-0 text-right">
                <div className="flex items-center justify-end">
                  {getStatusBadge(invoice.status)}
                </div>
                <p className="text-muted-foreground mt-1">
                  تاريخ الإصدار: {new Date(invoice.created_at).toLocaleDateString('ar-SA')}
                </p>
                <p className="text-muted-foreground">
                  تاريخ الاستحقاق: {new Date(invoice.due_date).toLocaleDateString('ar-SA')}
                </p>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">من</h3>
                <p className="font-medium">{hostingRequest?.store_name || 'المتجر التقليدي'}</p>
                <p className="text-sm text-muted-foreground mt-1">هاتف: {hostingRequest?.store_phone || 'غير متوفر'}</p>
              </div>
              <div className="text-left md:text-right">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">إلى</h3>
                <p className="font-medium">{hostingRequest?.ecommerce_name || 'المتجر الإلكتروني'}</p>
                <p className="text-sm text-muted-foreground mt-1">هاتف: {hostingRequest?.ecommerce_phone || 'غير متوفر'}</p>
              </div>
            </div>
            
            <Separator className="my-6" />
            
            {/* Invoice Items */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-right">المنتج</th>
                    <th className="px-4 py-3 text-right">السعر</th>
                    <th className="px-4 py-3 text-right">الكمية</th>
                    <th className="px-4 py-3 text-right">المجموع</th>
                  </tr>
                </thead>
                <tbody>
                  {products.length > 0 ? (
                    products.map((product) => (
                      <tr key={product.id} className="border-b">
                        <td className="px-4 py-3">
                          <div className="font-medium">{product.product_name}</div>
                        </td>
                        <td className="px-4 py-3">{product.price} ر.س</td>
                        <td className="px-4 py-3">{product.quantity}</td>
                        <td className="px-4 py-3">{product.price * product.quantity} ر.س</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-4 py-3 text-center text-muted-foreground">
                        لا توجد منتجات مضافة لهذه الفاتورة
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            
            <div className="mt-6 flex justify-end">
              <div className="w-full md:w-1/3">
                <div className="flex justify-between py-2">
                  <span className="text-muted-foreground">المجموع الفرعي:</span>
                  <span>{invoice.amount} ر.س</span>
                </div>
                <div className="flex justify-between py-2">
                  <span className="text-muted-foreground">الضريبة (15%):</span>
                  <span>{invoice.tax_amount} ر.س</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between py-2 font-bold">
                  <span>الإجمالي:</span>
                  <span>{invoice.total_amount} ر.س</span>
                </div>
              </div>
            </div>
          </CardContent>
          
          <CardFooter className="flex flex-col md:flex-row justify-between gap-4">
            <div className="text-sm text-muted-foreground">
              <p>شكراً لاختيارك خدمة استضافة المنتجات لدينا</p>
              <p>يرجى سداد المبلغ قبل تاريخ الاستحقاق</p>
            </div>
            
            {invoice.status === 'pending' && user?.role === 'ecommerce' && (
              <Button className="min-w-40">
                <CreditCard className="h-4 w-4 mr-2" />
                دفع الفاتورة الآن
              </Button>
            )}
          </CardFooter>
        </Card>
        
        {/* Transactions */}
        <div className="print:hidden">
          <h2 className="text-lg font-semibold mb-3">سجل المعاملات</h2>
          <InvoiceTransactions invoiceId={id} />
        </div>
        
        {/* Actions */}
        <div className="print:hidden">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">الإجراءات المتاحة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                {user?.role === 'ecommerce' && invoice.status === 'pending' && (
                  <Button>
                    <CreditCard className="h-4 w-4 mr-2" />
                    دفع الفاتورة
                  </Button>
                )}
                
                {user?.role === 'store' && invoice.status === 'pending' && (
                  <Button variant="outline" className="text-yellow-600 border-yellow-200 bg-yellow-50">
                    <X className="h-4 w-4 mr-2" />
                    إلغاء الفاتورة
                  </Button>
                )}
                
                {user?.role === 'store' && invoice.status === 'paid' && (
                  <Button variant="outline" className="text-green-600 border-green-200 bg-green-50">
                    <Check className="h-4 w-4 mr-2" />
                    تأكيد الدفع
                  </Button>
                )}
                
                {hostingRequest && (
                  <Button variant="outline" asChild>
                    <a href={`/dashboard/hosting-requests/${hostingRequest.id}`} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      عرض طلب الاستضافة
                    </a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default InvoiceDetail;

