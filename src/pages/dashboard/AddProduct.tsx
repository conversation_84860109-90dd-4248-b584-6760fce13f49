import React, { useEffect, useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { ProductForm } from "@/components/products/ProductForm";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link, useParams } from "react-router-dom";
import { ProductProvider } from "@/contexts/ProductContext";
import { useProducts } from "@/contexts/ProductContext";
import { toast } from "@/hooks/toast";
import { useAuth } from "@/contexts/auth";
import { PageHeader } from "@/components/ui/page-header";
import { Loading } from "@/components/ui/loading";
import { Product } from "@/types";

const AddProductContent = () => {
  const { id } = useParams<{ id: string }>();
  const { addProduct, updateProduct, getProductById } = useProducts();
  const { user } = useAuth();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isEditMode = !!id;

  useEffect(() => {
    const fetchProduct = async () => {
      if (isEditMode && id) {
        setLoading(true);
        try {
          const productData = await getProductById(id);
          if (productData) {
            setProduct(productData);
          } else {
            setError("المنتج غير موجود");
          }
        } catch (err) {
          console.error("Error fetching product:", err);
          setError("فشل في تحميل بيانات المنتج");
        } finally {
          setLoading(false);
        }
      }
    };

    fetchProduct();
  }, [id, getProductById, isEditMode]);

  const handleSubmit = async (data: any, imageFiles: File[]) => {
    try {
      if (isEditMode && product) {
        await updateProduct(
          product.id,
          {
            ...data,
            quantity: data.quantity || 0,
          },
          imageFiles
        );
        toast.success("تم تحديث المنتج بنجاح");
      } else {
        await addProduct({
          productData: {
            name: data.name,
            description: data.description,
            price: data.price,
            quantity: data.quantity || 0,
            category: data.category,
            user_id: user?.id || "",
            seller_id: user?.id || "",
            seller_name: user?.name || "",
            status: "active",
          },
          imageFiles,
        });
        toast.success("تمت إضافة المنتج بنجاح");
      }
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error(
        isEditMode ? "حدث خطأ أثناء تحديث المنتج" : "حدث خطأ أثناء إضافة المنتج"
      );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-40">
        <Loading text="جاري تحميل بيانات المنتج..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-4">
        <p className="text-red-500 mb-2">{error}</p>
        <Button variant="outline" asChild>
          <Link to="/dashboard/products" className="flex items-center">
            <ArrowRight className="ml-2 h-4 w-4" />
            العودة إلى المنتجات
          </Link>
        </Button>
      </div>
    );
  }

  // Prepare initialValues for the form
  const initialValues = product
    ? {
        name: product.name,
        description: product.description || "",
        price: product.price,
        quantity: product.quantity,
        category: product.category || "",
        images: product.images || [],
      }
    : undefined;

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {isEditMode ? "تعديل المنتج" : "إضافة منتج جديد"}
        </h1>
        <Button variant="outline" asChild>
          <Link to="/dashboard/products" className="flex items-center">
            <ArrowRight className="ml-2 h-4 w-4" />
            العودة إلى المنتجات
          </Link>
        </Button>
      </div>
      <div className="bg-card shadow rounded-lg p-6">
        <ProductForm
          onSubmit={handleSubmit}
          initialValues={initialValues}
          isEditing={isEditMode}
        />
      </div>
    </div>
  );
};

export default function AddProduct() {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  return (
    <DashboardLayout>
      <PageHeader
        title={isEditMode ? "تعديل المنتج" : "إضافة منتج جديد"}
        description={
          isEditMode
            ? "تعديل بيانات المنتج الحالي"
            : "إنشاء منتج جديد وإضافته إلى قائمة منتجاتك"
        }
      />
      <ProductProvider>
        <AddProductContent />
      </ProductProvider>
    </DashboardLayout>
  );
}
