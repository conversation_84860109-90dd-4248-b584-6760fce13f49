
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/auth';
import { useAuthRole } from '@/hooks/useAuthRole';
import { UserRole } from '@/types';
import { toast } from 'sonner';
import { Users, UserPlus, Filter, User, Trash2, Edit, Shield } from 'lucide-react';
import { useUsers } from '@/hooks/useUsers';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

export default function UserManagement() {
  const { user } = useAuth();
  const authRole = useAuthRole();
  const { userRole } = authRole;
  const { users, createUser, updateUser, deleteUser, isLoading } = useUsers();
  const [selectedUser, setSelectedUser] = useState<any | null>(null);
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [filter, setFilter] = useState<string | null>(null);

  useEffect(() => {
    if (!authRole.hasAccess) {
      toast.error("ليس لديك صلاحية للوصول إلى هذه الصفحة");
    }
  }, [authRole.hasAccess]);

  const handleAddUser = async (userData: { 
    name?: string; 
    email?: string;
    permissions?: string[];
    role?: UserRole;
    active?: boolean;
  }) => {
    try {
      // In a real app, we would validate the data and create the user
      // This is a mock implementation
      toast.success("تم إضافة المستخدم بنجاح");
      setIsAddingUser(false);
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error("حدث خطأ أثناء إضافة المستخدم");
    }
  };

  const handleUpdateUser = async (userId: string, userData: Partial<{
    name?: string;
    email?: string;
    permissions?: string[];
    role?: UserRole;
    active?: boolean;
  }>) => {
    try {
      // In a real app, we would validate the data and update the user
      // This is a mock implementation
      toast.success("تم تحديث المستخدم بنجاح");
      setSelectedUser(null);
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error("حدث خطأ أثناء تحديث المستخدم");
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟')) {
      try {
        // In a real app, we would delete the user
        // This is a mock implementation
        toast.success("تم حذف المستخدم بنجاح");
      } catch (error) {
        console.error('Error deleting user:', error);
        toast.error("حدث خطأ أثناء حذف المستخدم");
      }
    }
  };

  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive" className="mr-2">مدير النظام</Badge>;
      case 'sub-admin':
        return <Badge variant="outline" className="mr-2">مدير فرعي</Badge>;
      case 'store':
        return <Badge variant="secondary" className="mr-2">متجر تقليدي</Badge>;
      case 'ecommerce':
        return <Badge variant="default" className="mr-2">متجر إلكتروني</Badge>;
      default:
        return <Badge className="mr-2">{role}</Badge>;
    }
  };

  const getStatusBadge = (active: boolean) => {
    return active ? 
      <Badge className="bg-green-100 text-green-800 hover:bg-green-200">نشط</Badge> : 
      <Badge variant="destructive">غير نشط</Badge>;
  };

  const filteredUsers = users?.filter(user => {
    if (!filter) return true;
    return user.role === filter;
  });

  if (!authRole.hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <Shield className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">وصول مرفوض</h2>
          <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader 
        title="إدارة المستخدمين"
        description="عرض وإدارة المستخدمين في النظام"
        icon={Users}
      />

      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex flex-wrap items-center gap-2">
            <Button
              variant={!filter ? "default" : "outline"} 
              onClick={() => setFilter(null)}
              className="text-xs md:text-sm"
            >
              الكل
            </Button>
            <Button
              variant={filter === 'admin' ? "default" : "outline"} 
              onClick={() => setFilter('admin')}
              className="text-xs md:text-sm"
            >
              <Shield className="h-4 w-4 mr-1" />
              مدير النظام
            </Button>
            <Button
              variant={filter === 'store' ? "default" : "outline"} 
              onClick={() => setFilter('store')}
              className="text-xs md:text-sm"
            >
              <User className="h-4 w-4 mr-1" />
              متجر تقليدي
            </Button>
            <Button
              variant={filter === 'ecommerce' ? "default" : "outline"} 
              onClick={() => setFilter('ecommerce')}
              className="text-xs md:text-sm"
            >
              <Users className="h-4 w-4 mr-1" />
              متجر إلكتروني
            </Button>
          </div>
          
          {authRole.isAdmin && (
            <Button onClick={() => setIsAddingUser(true)} className="whitespace-nowrap">
              <UserPlus className="h-4 w-4 mr-2" />
              إضافة مستخدم
            </Button>
          )}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>المستخدمين</CardTitle>
            <CardDescription>
              إجمالي المستخدمين: {users?.length || 0}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="divide-y">
              {isLoading ? (
                <div className="py-4 text-center">جاري التحميل...</div>
              ) : filteredUsers?.length === 0 ? (
                <div className="py-4 text-center">لا يوجد مستخدمين</div>
              ) : (
                filteredUsers?.map((user) => (
                  <div key={user.id} className="py-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <Avatar>
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback>{user.name?.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="mr-3">
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getRoleBadge(user.role as UserRole)}
                      {getStatusBadge(user.active)}
                      
                      {authRole.isAdmin && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">تعديل</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                              تعديل المستخدم
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteUser(user.id)} className="text-red-600">
                              حذف المستخدم
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {isAddingUser && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
            <Card className="w-full max-w-md mx-auto">
              <CardHeader>
                <CardTitle>إضافة مستخدم جديد</CardTitle>
                <p className="text-sm text-muted-foreground">
                  أدخل بيانات المستخدم الجديد
                </p>
              </CardHeader>
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const userData = {
                  name: formData.get('name') as string,
                  email: formData.get('email') as string,
                  role: formData.get('role') as UserRole,
                };
                handleAddUser(userData);
              }}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <FormLabel htmlFor="name">الإسم الكامل</FormLabel>
                    <Input id="name" name="name" required />
                    <p className="text-xs text-muted-foreground">
                      أدخل الإسم الكامل للمستخدم
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel htmlFor="email">البريد الإلكتروني</FormLabel>
                    <Input id="email" name="email" type="email" required />
                    <p className="text-xs text-muted-foreground">
                      سيتم استخدام هذا البريد لتسجيل الدخول
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel htmlFor="role">الصلاحية</FormLabel>
                    <Select name="role" required defaultValue="ecommerce">
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الصلاحية" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">مدير النظام</SelectItem>
                        <SelectItem value="sub-admin">مدير فرعي</SelectItem>
                        <SelectItem value="store">متجر تقليدي</SelectItem>
                        <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      حدد صلاحية المستخدم في النظام
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsAddingUser(false)}
                  >
                    إلغاء
                  </Button>
                  <Button type="submit">إضافة المستخدم</Button>
                </CardFooter>
              </form>
            </Card>
          </div>
        )}

        {selectedUser && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
            <Card className="w-full max-w-md mx-auto">
              <CardHeader>
                <CardTitle>تعديل المستخدم</CardTitle>
                <p className="text-sm text-muted-foreground">
                  تعديل بيانات {selectedUser.name}
                </p>
              </CardHeader>
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const userData = {
                  name: formData.get('name') as string,
                  email: formData.get('email') as string,
                  role: formData.get('role') as UserRole,
                  active: formData.get('active') === 'on',
                };
                handleUpdateUser(selectedUser.id, userData);
              }}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <FormLabel htmlFor="name">الإسم الكامل</FormLabel>
                    <Input id="name" name="name" defaultValue={selectedUser.name} required />
                    <p className="text-xs text-muted-foreground">
                      أدخل الإسم الكامل للمستخدم
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel htmlFor="email">البريد الإلكتروني</FormLabel>
                    <Input id="email" name="email" type="email" defaultValue={selectedUser.email} required />
                    <p className="text-xs text-muted-foreground">
                      سيتم استخدام هذا البريد لتسجيل الدخول
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel htmlFor="role">الصلاحية</FormLabel>
                    <Select name="role" required defaultValue={selectedUser.role}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الصلاحية" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">مدير النظام</SelectItem>
                        <SelectItem value="sub-admin">مدير فرعي</SelectItem>
                        <SelectItem value="store">متجر تقليدي</SelectItem>
                        <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      حدد صلاحية المستخدم في النظام
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Checkbox id="active" name="active" defaultChecked={selectedUser.active} />
                    <div className="grid gap-1.5 leading-none">
                      <label
                        htmlFor="active"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        نشط
                      </label>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setSelectedUser(null)}
                  >
                    إلغاء
                  </Button>
                  <div className="flex gap-2">
                    <Button 
                      type="button" 
                      variant="destructive"
                      onClick={() => {
                        handleDeleteUser(selectedUser.id);
                        setSelectedUser(null);
                      }}
                    >
                      حذف
                    </Button>
                    <Button type="submit">حفظ التغييرات</Button>
                  </div>
                </CardFooter>
              </form>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
