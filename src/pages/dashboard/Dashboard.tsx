import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { PageHeader } from "@/components/ui/page-header";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { UserRoleDashboardStats } from "@/components/dashboard/UserRoleDashboardStats";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useOrders } from "@/hooks/useOrders";
import { useDashboardStats } from "@/hooks/useDashboardStats";

export default function AdminDashboard() {
  const { user } = useAuth();
  const { stats, isLoading } = useDashboardStats();

  const getWelcomeMessage = () => {
    if (!user) return "مرحبًا بك في لوحة التحكم";

    switch (user.role) {
      case "admin":
        return "مرحبًا بك في لوحة تحكم الإدارة";
      case "sub-admin":
        return "مرحبًا بك في لوحة تحكم الإدارة الفرعية";
      case "store":
        return "مرحبًا بك في لوحة تحكم المتجر التقليدي";
      case "ecommerce":
        return "مرحبًا بك في لوحة تحكم المتجر الإلكتروني";
      default:
        return "مرحبًا بك في لوحة التحكم";
    }
  };

  return (
    <DashboardLayout>
      <PageHeader title="لوحة التحكم" description={getWelcomeMessage()} />

      <UserRoleDashboardStats userCounts={stats} isLoading={isLoading} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-5">
          <h3 className="text-lg font-semibold mb-4">أحدث النشاطات</h3>
          <p className="text-gray-500 text-center py-4">لا توجد نشاطات حديثة</p>
        </Card>

        <Card className="p-5">
          <h3 className="text-lg font-semibold mb-4">إحصائيات سريعة</h3>
          <p className="text-gray-500 text-center py-4">
            لا توجد إحصائيات متاحة
          </p>
        </Card>
      </div>
    </DashboardLayout>
  );
}
