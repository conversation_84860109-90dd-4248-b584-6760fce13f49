
import React from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card } from '@/components/ui/card';
import { ProductForm } from '@/components/products/ProductForm';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ProductProvider } from '@/contexts/ProductContext';
import { useProducts } from '@/contexts/ProductContext';
import { toast } from '@/hooks/toast';
import { useAuth } from '@/contexts/auth';

const ProductCreateContent = () => {
  const { addProduct } = useProducts();
  const { user } = useAuth();

  const handleSubmit = async (data: any) => {
    try {
      await addProduct({
        name: data.name,
        description: data.description,
        price: data.price,
        quantity: data.quantity || 0,
        category: data.category,
        images: data.images || [],
        user_id: user?.id || '',
        seller_id: user?.id || '',
        seller_name: user?.name || '',
        status: 'active'
      });
      toast.success('تمت إضافة المنتج بنجاح');
    } catch (error) {
      console.error('Error adding product:', error);
      toast.error('حدث خطأ أثناء إضافة المنتج');
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إضافة منتج جديد</h1>
        <Button variant="outline" asChild>
          <Link to="/dashboard/products" className="flex items-center">
            <ArrowRight className="ml-2 h-4 w-4" />
            العودة إلى المنتجات
          </Link>
        </Button>
      </div>
      <div className="bg-white shadow rounded-lg p-6">
        <ProductForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
};

export default function ProductCreate() {
  return (
    <DashboardLayout>
      <ProductProvider>
        <ProductCreateContent />
      </ProductProvider>
    </DashboardLayout>
  );
}
