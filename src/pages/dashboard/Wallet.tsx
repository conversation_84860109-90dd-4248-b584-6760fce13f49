import React, { useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFinance } from "@/contexts/FinanceContext";
import { TransactionsList } from "@/components/TransactionsList";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  ArrowDown,
  ArrowUp,
  CreditCard,
  Clock,
  Wallet as WalletIcon,
} from "lucide-react";
import { Transaction } from "@/types";
import { useAuth } from "@/contexts/auth";
import { Loading } from "@/components/ui/loading";
import { formatCurrency } from "@/lib/transactionUtils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";

const WalletPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    balance,
    transactions,
    isLoading: isSubmitting,
    addDeposit,
    addWithdrawal,
    platformNumber,
  } = useFinance();

  const [depositAmount, setDepositAmount] = useState("");
  const [withdrawalAmount, setWithdrawalAmount] = useState("");
  const [depositReceipt, setDepositReceipt] = useState<File | null>(null);
  const [depositNotes, setDepositNotes] = useState("");
  const [depositDialogOpen, setDepositDialogOpen] = useState(false);
  const [withdrawalDialogOpen, setWithdrawalDialogOpen] = useState(false);
  const [withdrawalNotes, setWithdrawalNotes] = useState("");

  const filteredTransactions = {
    all: transactions,
    deposits: transactions.filter((tx) => tx.type === "deposit"),
    withdrawals: transactions.filter((tx) => tx.type === "withdraw"),
  };

  const validateAmount = (
    amount: string,
    type: "deposit" | "withdraw"
  ): boolean => {
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error("الرجاء إدخال مبلغ صحيح");
      return false;
    }

    if (type === "withdraw" && Number(amount) > (balance || 0)) {
      toast.error("المبلغ المطلوب للسحب أكبر من رصيدك الحالي");
      return false;
    }

    return true;
  };

  const handleDeposit = async () => {
    if (!validateAmount(depositAmount, "deposit")) return;

    // Check if receipt image is provided
    if (!depositReceipt) {
      toast.error("إثبات الدفع مطلوب");
      return;
    }

    try {
      const success = await addDeposit(
        Number(depositAmount),
        depositReceipt,
        depositNotes
      );

      if (success) {
        toast.success("تم إرسال طلب الإيداع بنجاح");
        setDepositDialogOpen(false);
        setDepositAmount("");
        setDepositReceipt(null);
        setDepositNotes("");
      }
    } catch (error) {
      console.error("Error processing deposit:", error);
      toast.error("فشل في إرسال طلب الإيداع");
    }
  };

  const handleWithdrawal = async () => {
    if (!validateAmount(withdrawalAmount, "withdraw")) return;

    try {
      const success = await addWithdrawal(
        Number(withdrawalAmount),
        withdrawalNotes
      );

      if (success) {
        toast.success("تم إرسال طلب السحب بنجاح");
        setWithdrawalDialogOpen(false);
        setWithdrawalAmount("");
        setWithdrawalNotes("");
      }
    } catch (error) {
      console.error("Error processing withdrawal:", error);
      toast.error("فشل في إرسال طلب السحب");
    }
  };

  if (isSubmitting) {
    return (
      <DashboardLayout>
        <PageHeader
          title="المحفظة"
          description="إدارة رصيدك والمعاملات المالية"
        />
        <div className="flex items-center justify-center h-64">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="المحفظة"
        description="إدارة رصيدك والمعاملات المالية"
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Balance Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">الرصيد الحالي</CardTitle>
            <CardDescription>رصيدك المتاح للاستخدام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="p-2 bg-primary/10 rounded-full">
                <WalletIcon className="h-8 w-8 text-primary" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {formatCurrency(balance || 0)}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <div className="grid grid-cols-2 gap-4 w-full">
              <Dialog
                open={depositDialogOpen}
                onOpenChange={setDepositDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button className="w-full" variant="default">
                    <ArrowDown className="mr-2 h-4 w-4" />
                    إيداع
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>إيداع مبلغ</DialogTitle>
                    <DialogDescription>
                      أدخل المبلغ الذي ترغب بإيداعه في محفظتك
                    </DialogDescription>
                    <div>
                      <Alert
                        variant="default"
                        className="mb-4 bg-blue-50 text-right"
                      >
                        <AlertDescription>
                          <h3 className="text-xl font-bold">
                            معلومات التحويل الخاصه بالمنصه
                          </h3>
                          <div className="flex justify-between flex-col gap-4 mt-2">
                            <div className="flex items-center space-x-2 gap-2">
                              <CreditCard className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium">
                                رقم الحساب:
                              </span>
                              <span>{platformNumber.bank_account}</span>
                            </div>
                            <div className="flex items-center space-x-2 gap-2">
                              <CreditCard className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium">
                                رقم iban:
                              </span>
                              <span>{platformNumber.iban}</span>
                            </div>
                            <div className="flex items-center space-x-2 gap-2">
                              <Clock className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium">
                                الوقت المتوقع:
                              </span>
                              <span className="text-sm">
                                من 24 الي 48 ساعة عمل
                              </span>
                            </div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  </DialogHeader>

                  <div className="space-y-4 py-4 max-h-[50vh] overflow-auto">
                    <div className="space-y-2">
                      <Label htmlFor="deposit-amount">
                        المبلغ (ريال سعودي)
                      </Label>
                      <Input
                        id="deposit-amount"
                        placeholder="أدخل المبلغ"
                        type="number"
                        min={1}
                        value={depositAmount}
                        onChange={(e) => setDepositAmount(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deposit-receipt">
                        إثبات الدفع <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="deposit-receipt"
                        type="file"
                        accept="image/*"
                        onChange={(e) =>
                          setDepositReceipt(
                            e.target.files ? e.target.files[0] : null
                          )
                        }
                        required
                      />
                      {!depositReceipt && (
                        <p className="text-xs text-red-500">
                          يجب إرفاق صورة إثبات الدفع
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deposit-notes">
                        ملاحظات إضافية (اختياري)
                      </Label>
                      <Textarea
                        id="deposit-notes"
                        placeholder="أي معلومات إضافية عن عملية الإيداع"
                        value={depositNotes}
                        onChange={(e) => setDepositNotes(e.target.value)}
                      />
                    </div>

                    <Alert>
                      <AlertDescription>
                        يرجى التحويل إلى حساب الشركة ثم إرفاق إثبات الدفع.
                      </AlertDescription>
                    </Alert>
                  </div>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setDepositDialogOpen(false)}
                    >
                      إلغاء
                    </Button>
                    <Button onClick={handleDeposit} disabled={isSubmitting}>
                      {isSubmitting ? "جاري المعالجة..." : "تأكيد الإيداع"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog
                open={withdrawalDialogOpen}
                onOpenChange={setWithdrawalDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button className="w-full" variant="outline">
                    <ArrowUp className="mr-2 h-4 " />
                    سحب
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>سحب مبلغ</DialogTitle>
                    <DialogDescription>
                      أدخل المبلغ الذي ترغب بسحبه من محفظتك
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    <div className="text-sm">
                      <span className="font-medium">الرصيد المتاح: </span>
                      <span>{formatCurrency(balance || 0)}</span>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="withdrawal-amount">
                        المبلغ (ريال سعودي)
                      </Label>
                      <Input
                        id="withdrawal-amount"
                        placeholder="أدخل المبلغ"
                        type="number"
                        min={1}
                        max={balance || 0}
                        value={withdrawalAmount}
                        onChange={(e) => setWithdrawalAmount(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="withdrawal-notes">
                        ملاحظات إضافية (اختياري)
                      </Label>
                      <Textarea
                        id="withdrawal-notes"
                        placeholder="أي معلومات إضافية عن عملية السحب"
                        value={withdrawalNotes}
                        onChange={(e) => setWithdrawalNotes(e.target.value)}
                      />
                    </div>

                    {user?.bank_account ? (
                      <div className="p-4 bg-muted rounded-lg text-sm">
                        <div className="mb-2 font-medium">
                          سيتم التحويل إلى الحساب البنكي التالي:
                        </div>
                        <div>
                          <span className="font-medium">رقم الحساب: </span>
                          {user.bank_account || "غير محدد"}
                        </div>
                        <div className="flex gap-4">
                          <span className="font-medium">رقم iban: </span>
                          {user.iban || "غير محدد"}
                        </div>
                      </div>
                    ) : (
                      <Alert>
                        <AlertDescription>
                          لم تقم بإضافة معلومات الحساب البنكي.{" "}
                          <Button
                            variant="link"
                            className="h-auto p-0"
                            onClick={() => navigate("/dashboard/profile")}
                          >
                            أضف معلومات الحساب البنكي
                          </Button>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setWithdrawalDialogOpen(false)}
                    >
                      إلغاء
                    </Button>
                    <Button
                      onClick={handleWithdrawal}
                      disabled={isSubmitting || !user?.bank_account}
                    >
                      {isSubmitting ? "جاري المعالجة..." : "تأكيد السحب"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardFooter>
        </Card>

        {/* Recent Deposits */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">الإيداعات الأخيرة</CardTitle>
            <CardDescription>آخر عمليات الإيداع في محفظتك</CardDescription>
          </CardHeader>
          <CardContent>
            {filteredTransactions.deposits.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                لا توجد عمليات إيداع
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTransactions.deposits
                  .slice(0, 3)
                  .map((tx: Transaction) => (
                    <div
                      key={tx.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center">
                        <div className="p-2 bg-green-100 rounded-full mr-3">
                          <ArrowDown className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {tx.description || "إيداع"}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(tx.created_at).toLocaleDateString(
                              "ar-SA"
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="font-medium text-green-600">
                          {formatCurrency(tx.amount)}
                        </div>
                        <Badge
                          variant="outline"
                          className={
                            tx.status === "completed"
                              ? "bg-green-50 text-green-600 border-green-200"
                              : tx.status === "pending"
                              ? "bg-yellow-50 text-yellow-600 border-yellow-200"
                              : "bg-red-50 text-red-600 border-red-200"
                          }
                        >
                          {tx.status === "completed"
                            ? "مكتمل"
                            : tx.status === "pending"
                            ? "قيد الانتظار"
                            : "مرفوض"}
                        </Badge>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="w-full"
              onClick={() => navigate("/dashboard/wallet/deposits")}
            >
              عرض كل الإيداعات
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Withdrawals */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">عمليات السحب الأخيرة</CardTitle>
            <CardDescription>آخر عمليات سحب من محفظتك</CardDescription>
          </CardHeader>
          <CardContent>
            {filteredTransactions.withdrawals.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                لا توجد عمليات سحب
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTransactions.withdrawals
                  .slice(0, 3)
                  .map((tx: Transaction) => (
                    <div
                      key={tx.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center">
                        <div className="p-2 bg-red-100 rounded-full mr-3">
                          <ArrowUp className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {tx.description || "سحب"}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(tx.created_at).toLocaleDateString(
                              "ar-SA"
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="font-medium text-red-600">
                          {formatCurrency(tx.amount)}
                        </div>
                        <Badge
                          variant="outline"
                          className={
                            tx.status === "completed"
                              ? "bg-green-50 text-green-600 border-green-200"
                              : tx.status === "pending"
                              ? "bg-yellow-50 text-yellow-600 border-yellow-200"
                              : "bg-red-50 text-red-600 border-red-200"
                          }
                        >
                          {tx.status === "completed"
                            ? "مكتمل"
                            : tx.status === "pending"
                            ? "قيد الانتظار"
                            : "مرفوض"}
                        </Badge>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="w-full"
              onClick={() => navigate("/dashboard/wallet/withdrawals")}
            >
              عرض كل عمليات السحب
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Transactions History */}
      <Card>
        <CardHeader>
          <CardTitle>سجل المعاملات</CardTitle>
          <CardDescription>جميع المعاملات المالية في محفظتك</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="deposits">الإيداعات</TabsTrigger>
              <TabsTrigger value="withdrawals">عمليات السحب</TabsTrigger>
              <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              <TransactionsList
                transactions={filteredTransactions.all}
                emptyStateMessage="لا توجد معاملات"
              />
            </TabsContent>

            <TabsContent value="deposits">
              <TransactionsList
                transactions={filteredTransactions.deposits}
                emptyStateMessage="لا توجد إيداعات"
              />
            </TabsContent>

            <TabsContent value="withdrawals">
              <TransactionsList
                transactions={filteredTransactions.withdrawals}
                emptyStateMessage="لا توجد عمليات سحب"
              />
            </TabsContent>

            <TabsContent value="pending">
              <TransactionsList
                transactions={transactions.filter(
                  (tx) => tx.status === "pending"
                )}
                emptyStateMessage="لا توجد معاملات قيد الانتظار"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default WalletPage;
