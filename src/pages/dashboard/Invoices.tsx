
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { 
  FileText,
  Search,
  Download,
  ExternalLink,
  ArrowUpDown,
  Filter,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Card, 
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useHosting } from '@/contexts/HostingContext';
import { Loading } from '@/components/ui/loading';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>L<PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useFinance } from '@/contexts/FinanceContext';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';

export default function InvoicesPage() {
  const { user } = useAuth();
  const { hostingRequests, isLoading: hostingLoading } = useHosting();
  const { balance, isLoading: financeLoading } = useFinance();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState('all');

  // Filter invoices based on search query and status
  const filteredInvoices = hostingRequests.filter(request => {
    // Search filter
    const matchesSearch =
      request.ecommerce_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.store_name?.toLowerCase().includes(searchQuery.toLowerCase());
      
    // Status filter
    const matchesStatus = 
      statusFilter === 'all' || 
      request.status === statusFilter;

    // Tab filter
    const matchesTab = activeTab === 'all' || 
      (activeTab === 'pending' && request.status === 'pending') ||
      (activeTab === 'active' && (request.status === 'accepted' || request.status === 'ready')) ||
      (activeTab === 'completed' && request.status === 'delivered') ||
      (activeTab === 'cancelled' && request.status === 'cancelled' || request.status === 'rejected');
      
    return matchesSearch && matchesStatus && matchesTab;
  });

  // Sort invoices by created date
  const sortedInvoices = [...filteredInvoices].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
  });

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <FileText className="h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-4">غير مصرح</h2>
          <p className="text-muted-foreground mb-6">يجب تسجيل الدخول لرؤية هذه الصفحة</p>
          <Button asChild>
            <a href="/login">تسجيل الدخول</a>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (hostingLoading || financeLoading) {
    return (
      <DashboardLayout>
        <div className="h-[60vh] flex items-center justify-center">
          <Loading text="جاري تحميل الفواتير..." size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  // Get counts for tabs
  const pendingCount = hostingRequests.filter(r => r.status === 'pending').length;
  const activeCount = hostingRequests.filter(r => r.status === 'accepted' || r.status === 'ready').length;
  const completedCount = hostingRequests.filter(r => r.status === 'delivered').length;
  const cancelledCount = hostingRequests.filter(r => r.status === 'cancelled' || r.status === 'rejected').length;

  return (
    <DashboardLayout>
      <PageHeader
        title="الفواتير"
        description="عرض وإدارة فواتير طلبات الاستضافة"
        icon={FileText}
      />

      {user.role === 'ecommerce' && balance !== null && (
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div>
                <p className="text-muted-foreground">رصيد المحفظة الحالي</p>
                <p className="text-2xl font-bold">{balance.toFixed(2)} ر.س</p>
              </div>
              <Button asChild>
                <Link to="/dashboard/wallet">إدارة المحفظة</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 mb-6">
          <TabsTrigger value="all">
            الكل ({hostingRequests.length})
          </TabsTrigger>
          <TabsTrigger value="pending">
            قيد الانتظار ({pendingCount})
          </TabsTrigger>
          <TabsTrigger value="active">
            نشطة ({activeCount})
          </TabsTrigger>
          <TabsTrigger value="completed">
            مكتملة ({completedCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card className="mt-0">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="relative w-full sm:w-72">
                  <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="بحث عن اسم المتجر أو اسم التاجر..."
                    className="pr-9"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-40">
                      <Filter className="ml-2 h-4 w-4" />
                      <span>{statusFilter === 'all' ? 'كل الحالات' : 
                        statusFilter === 'pending' ? 'قيد الانتظار' :
                        statusFilter === 'accepted' ? 'مقبولة' :
                        statusFilter === 'ready' ? 'جاهزة' :
                        statusFilter === 'delivered' ? 'مكتملة' :
                        statusFilter === 'cancelled' ? 'ملغية' :
                        statusFilter === 'rejected' ? 'مرفوضة' : 'كل الحالات'
                      }</span>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">كل الحالات</SelectItem>
                      <SelectItem value="pending">قيد الانتظار</SelectItem>
                      <SelectItem value="accepted">مقبولة</SelectItem>
                      <SelectItem value="ready">جاهزة</SelectItem>
                      <SelectItem value="delivered">مكتملة</SelectItem>
                      <SelectItem value="rejected">مرفوضة</SelectItem>
                      <SelectItem value="cancelled">ملغية</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button 
                    variant="outline" 
                    onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                    className="flex items-center"
                  >
                    <ArrowUpDown className="h-4 w-4 ml-2" />
                    {sortOrder === 'desc' ? 'الأحدث أولاً' : 'الأقدم أولاً'}
                  </Button>
                  {user.role === 'ecommerce' && (
                    <Button asChild>
                      <Link to="/dashboard/new-hosting-request">طلب استضافة جديد</Link>
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المتجر</TableHead>
                    <TableHead className="hidden md:table-cell">المنتجات</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedInvoices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <FileText className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد فواتير مطابقة لبحثك</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedInvoices.map(request => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{request.ecommerce_name || request.store_name}</p>
                            <p className="text-xs text-muted-foreground hidden sm:block">
                              {user.role === 'ecommerce' ? `المتجر: ${request.store_name}` : `المورد: ${request.ecommerce_name}`}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="max-w-48 truncate">
                            {request.products && request.products.length > 0 ? (
                              <span>{request.products.length} منتجات</span>
                            ) : (
                              <span className="text-sm text-muted-foreground">لا توجد منتجات</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {new Date(request.created_at).toLocaleDateString('ar-SA')}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button size={isMobile ? "sm" : "default"} variant="outline" asChild>
                            <a href={`/dashboard/invoices/${request.id}`}>
                              <ExternalLink className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between pt-6">
              <div className="text-sm text-muted-foreground">
                إجمالي الفواتير: {sortedInvoices.length}
              </div>
              {user.role === 'ecommerce' && (
                <Button asChild variant="outline">
                  <a href="/dashboard/hosting-requests">
                    عرض كل طلبات الاستضافة
                  </a>
                </Button>
              )}
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          {/* Same table but filtered for pending status */}
          <Card className="mt-0">
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المتجر</TableHead>
                    <TableHead className="hidden md:table-cell">المنتجات</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedInvoices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Clock className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد فواتير قيد الانتظار</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // Same row rendering logic
                    sortedInvoices.map(request => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{request.ecommerce_name || request.store_name}</p>
                            <p className="text-xs text-muted-foreground hidden sm:block">
                              {user.role === 'ecommerce' ? `المتجر: ${request.store_name}` : `المورد: ${request.ecommerce_name}`}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="max-w-48 truncate">
                            {request.products && request.products.length > 0 ? (
                              <span>{request.products.length} منتجات</span>
                            ) : (
                              <span className="text-sm text-muted-foreground">لا توجد منتجات</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {new Date(request.created_at).toLocaleDateString('ar-SA')}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button size={isMobile ? "sm" : "default"} variant="outline" asChild>
                            <a href={`/dashboard/invoices/${request.id}`}>
                              <ExternalLink className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Similar content for active and completed tabs */}
        <TabsContent value="active" className="space-y-4">
          <Card className="mt-0">
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المتجر</TableHead>
                    <TableHead className="hidden md:table-cell">المنتجات</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedInvoices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <FileText className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد فواتير نشطة حالياً</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedInvoices.map(request => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{request.ecommerce_name || request.store_name}</p>
                            <p className="text-xs text-muted-foreground hidden sm:block">
                              {user.role === 'ecommerce' ? `المتجر: ${request.store_name}` : `المورد: ${request.ecommerce_name}`}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="max-w-48 truncate">
                            {request.products && request.products.length > 0 ? (
                              <span>{request.products.length} منتجات</span>
                            ) : (
                              <span className="text-sm text-muted-foreground">لا توجد منتجات</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {new Date(request.created_at).toLocaleDateString('ar-SA')}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button size={isMobile ? "sm" : "default"} variant="outline" asChild>
                            <a href={`/dashboard/invoices/${request.id}`}>
                              <ExternalLink className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card className="mt-0">
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المتجر</TableHead>
                    <TableHead className="hidden md:table-cell">المنتجات</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedInvoices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <CheckCircle className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد فواتير مكتملة</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedInvoices.map(request => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{request.ecommerce_name || request.store_name}</p>
                            <p className="text-xs text-muted-foreground hidden sm:block">
                              {user.role === 'ecommerce' ? `المتجر: ${request.store_name}` : `المورد: ${request.ecommerce_name}`}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="max-w-48 truncate">
                            {request.products && request.products.length > 0 ? (
                              <span>{request.products.length} منتجات</span>
                            ) : (
                              <span className="text-sm text-muted-foreground">لا توجد منتجات</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {new Date(request.created_at).toLocaleDateString('ar-SA')}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button size={isMobile ? "sm" : "default"} variant="outline" asChild>
                            <a href={`/dashboard/invoices/${request.id}`}>
                              <ExternalLink className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  );
}

// Helper function to get status badge
function getStatusBadge(status: string) {
  switch (status) {
    case 'pending':
      return <Badge className="bg-yellow-50 text-yellow-600 border-yellow-200">قيد الانتظار</Badge>;
    case 'accepted':
      return <Badge className="bg-blue-50 text-blue-600 border-blue-200">مقبولة</Badge>;
    case 'ready':
      return <Badge className="bg-purple-50 text-purple-600 border-purple-200">جاهزة</Badge>;
    case 'delivered':
      return <Badge className="bg-green-50 text-green-600 border-green-200">مكتملة</Badge>;
    case 'cancelled':
      return <Badge className="bg-red-50 text-red-600 border-red-200">ملغية</Badge>;
    case 'rejected':
      return <Badge className="bg-red-50 text-red-600 border-red-200">مرفوضة</Badge>;
    default:
      return <Badge>غير معروفة</Badge>;
  }
}
