
import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DashboardStat } from '@/components/ui/dashboard-stat';
import { Users, ShoppingBag, CreditCard, Store } from 'lucide-react';
import { useProductManagement } from '@/hooks/admin/useProductManagement';
import AdminProductStatistics from '@/components/admin/AdminProductStatistics';
import { supabase } from '@/integrations/supabase/client';

export default function AdminDashboard() {
  const [counts, setCounts] = useState({
    users: 0,
    products: 0,
    transactions: 0,
    stores: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const { products } = useProductManagement({
    pageSize: 100, // Fetch more products for better statistics
    autoFetch: true, // Auto fetch products on load
  });

  useEffect(() => {
    const fetchCounts = async () => {
      setIsLoading(true);
      try {
        // Fetch users count
        const { count: usersCount, error: usersError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });
        
        // Fetch products count
        const { count: productsCount, error: productsError } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });
        
        // Fetch transactions count
        const { count: transactionsCount, error: transactionsError } = await supabase
          .from('transactions')
          .select('*', { count: 'exact', head: true });
        
        // Fetch stores count
        const { count: storesCount, error: storesError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'store');
        
        // Update state with counts
        setCounts({
          users: usersCount || 0,
          products: productsCount || 0,
          transactions: transactionsCount || 0,
          stores: storesCount || 0,
        });
        
        if (usersError || productsError || transactionsError || storesError) {
          console.error('Error fetching counts:', { 
            usersError, productsError, transactionsError, storesError 
          });
        }
      } catch (error) {
        console.error('Error in fetchCounts:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCounts();
  }, []);

  return (
    <DashboardLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6">لوحة تحكم المسؤول</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <DashboardStat
            title="المستخدمين"
            value={isLoading ? "..." : counts.users.toString()}
            description="إجمالي المستخدمين"
            icon={Users}
            trend="up"
          />
          <DashboardStat
            title="المنتجات"
            value={isLoading ? "..." : counts.products.toString()}
            description="إجمالي المنتجات"
            icon={ShoppingBag}
            trend="up"
          />
          <DashboardStat
            title="المعاملات"
            value={isLoading ? "..." : counts.transactions.toString()}
            description="إجمالي المعاملات"
            icon={CreditCard}
            trend="stable"
          />
          <DashboardStat
            title="المتاجر"
            value={isLoading ? "..." : counts.stores.toString()}
            description="إجمالي المتاجر"
            icon={Store}
            trend="up"
          />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AdminProductStatistics products={products} isLoading={isLoading} />
          
          <Card>
            <CardHeader>
              <CardTitle>آخر المعاملات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-muted-foreground">لم يتم العثور على أي معاملات جديدة.</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
