import { useEffect, useState } from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { Store } from "@/types/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useStoreData } from "@/hooks/store/useStoreData";
import { X, Upload, Image as ImageIcon } from "lucide-react";

function addInitialState(storeData: Store) {
  return storeData
    ? { ...storeData }
    : {
        name: "",
        address: "",
        phone: "",
        email: "",
        hours: "",
        image: "",
        gallery: [""],
        description: "",
        rating: 0,
        featured: false,
        shelfSpace: "",
        capacity: 0,
        legalActivity: "",
        city: "",
      };
}

function AddStoreData() {
  const { storeData, submitData, isLoading } = useStoreData();
  const [store, setStore] = useState<Partial<Store>>(
    addInitialState(storeData)
  );
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [galleryFiles, setGalleryFiles] = useState<File[]>([]);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [galleryPreviews, setGalleryPreviews] = useState<string[]>([]);

  useEffect(() => {
    if (storeData && Object.keys(storeData).length > 0) {
      setStore({
        ...storeData,
        legalActivity: storeData.legal_activity || "",
        shelfSpace: storeData.shelf_space || "",
        image: storeData.image || "",
        gallery: storeData.gallery || [""],
      });

      if (
        storeData.image &&
        typeof storeData.image === "string" &&
        storeData.image.startsWith("http")
      ) {
        setImagePreview(storeData.image);
      }

      if (storeData.gallery && Array.isArray(storeData.gallery)) {
        const previews = storeData.gallery
          .filter((url) => typeof url === "string" && url.startsWith("http"))
          .map((url) => url);
        setGalleryPreviews(previews);
      }
    }
  }, [storeData]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      setStore((prev) => ({ ...prev, image: file.name }));

      const reader = new FileReader();
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGalleryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setGalleryFiles((prev) => [...prev, ...files]);
      setStore((prev) => ({
        ...prev,
        gallery: [
          ...(prev.gallery || []),
          ...files.map((file) => file.name),
        ].filter(Boolean),
      }));

      files.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          setGalleryPreviews((prev) => [
            ...prev,
            event.target?.result as string,
          ]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleDeleteImage = () => {
    setImageFile(null);
    setImagePreview(null);
    setStore((prev) => ({ ...prev, image: "" }));
  };

  const handleDeleteGalleryImage = (index: number) => {
    setGalleryFiles((prev) => prev.filter((_, i) => i !== index));
    setGalleryPreviews((prev) => prev.filter((_, i) => i !== index));
    setStore((prev) => ({
      ...prev,
      gallery: (prev.gallery || []).filter((_, i) => i !== index),
    }));
  };

  const handleReplaceGalleryImage = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const newGalleryFiles = [...galleryFiles];
      newGalleryFiles[index] = file;
      setGalleryFiles(newGalleryFiles);

      const newGallery = [...(store.gallery || [])];
      newGallery[index] = file.name;
      setStore((prev) => ({ ...prev, gallery: newGallery }));

      const reader = new FileReader();
      reader.onload = (event) => {
        const newPreviews = [...galleryPreviews];
        newPreviews[index] = event.target?.result as string;
        setGalleryPreviews(newPreviews);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setStore((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setStore((prev) => ({ ...prev, [name]: Number(value) }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (storeData) {
      setStore({ ...storeData, ...store });
    }
    submitData(store, imageFile, galleryFiles);
  };

  return (
    <DashboardLayout>
      <Card className="w-full max-w-3xl mx-auto" dir="rtl">
        <CardHeader>
          <CardTitle>إضافة متجر جديد</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم المتجر</Label>
              <Input
                id="name"
                name="name"
                value={store.name}
                onChange={handleChange}
                placeholder="أدخل اسم المتجر"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">العنوان</Label>
              <Input
                id="address"
                name="address"
                value={store.address}
                onChange={handleChange}
                placeholder="أدخل عنوان المتجر"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">المدينة</Label>
              <Input
                id="city"
                name="city"
                value={store.city}
                onChange={handleChange}
                placeholder="أدخل المدينة"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">رقم الهاتف</Label>
              <Input
                type="tel"
                id="phone"
                name="phone"
                value={store.phone}
                onChange={handleChange}
                placeholder="أدخل رقم الهاتف"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={store.email}
                onChange={handleChange}
                placeholder="أدخل البريد الإلكتروني"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hours">ساعات العمل</Label>
              <Input
                id="hours"
                name="hours"
                value={store.hours}
                onChange={handleChange}
                placeholder="أدخل ساعات العمل"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">صورة المتجر</Label>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  id="image"
                  name="image"
                  onChange={handleImageChange}
                  accept="image/*"
                  className="cursor-pointer"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById("image")?.click()}
                >
                  <Upload className="h-4 w-4 mr-1" />
                  اختر
                </Button>
              </div>

              {imagePreview && (
                <div className="relative mt-2 border rounded-md p-1 w-40 h-40">
                  <img
                    src={imagePreview}
                    alt="Store preview"
                    className="w-full h-full object-cover rounded"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                    onClick={handleDeleteImage}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="gallery">معرض الصور</Label>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  id="gallery"
                  name="gallery"
                  onChange={handleGalleryChange}
                  accept="image/*"
                  multiple
                  className="cursor-pointer"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById("gallery")?.click()}
                >
                  <Upload className="h-4 w-4 mr-1" />
                  اختر
                </Button>
              </div>

              {galleryPreviews.length > 0 && (
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {galleryPreviews.map((preview, index) => (
                    <div
                      key={index}
                      className="relative border rounded-md p-1 w-full aspect-square"
                    >
                      <img
                        src={preview}
                        alt={`Gallery preview ${index + 1}`}
                        className="w-full h-full object-cover rounded"
                      />
                      <div className="absolute top-1 right-1 flex gap-1">
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="h-6 w-6 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100"
                          onClick={() => handleDeleteGalleryImage(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <div className="relative">
                          <Button
                            type="button"
                            variant="secondary"
                            size="icon"
                            className="h-6 w-6 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100"
                            onClick={() =>
                              document
                                .getElementById(`gallery-replace-${index}`)
                                ?.click()
                            }
                          >
                            <ImageIcon className="h-4 w-4" />
                          </Button>
                          <Input
                            type="file"
                            id={`gallery-replace-${index}`}
                            accept="image/*"
                            className="hidden"
                            onChange={(e) =>
                              handleReplaceGalleryImage(index, e)
                            }
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">الوصف</Label>
              <Textarea
                id="description"
                name="description"
                value={store.description}
                onChange={handleChange}
                placeholder="أدخل وصف المتجر"
                rows={4}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="shelf_space">مساحة الرفوف</Label>
              <Input
                id="shelfSpace"
                name="shelfSpace"
                value={store.shelfSpace}
                onChange={handleChange}
                placeholder="ex :  0.5m x 0.5m & 1m x 1m "
                className="ltr"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">السعة</Label>
              <Input
                type="number"
                id="capacity"
                name="capacity"
                value={store.capacity}
                onChange={handleNumberChange}
                placeholder="أدخل سعة المتجر"
                min="0"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="legal_activity">النشاط القانوني</Label>
              <Input
                id="legalActivity"
                name="legalActivity"
                value={store.legalActivity}
                onChange={handleChange}
                placeholder="أدخل النشاط القانوني"
                required
              />
            </div>

            <div className="flex justify-start mt-6">
              <Button type="submit" disabled={isLoading}>
                حفظ المتجر
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}

export default AddStoreData;
