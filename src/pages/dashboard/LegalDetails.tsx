import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { Upload, FileText, ExternalLink, Check, Trash2 } from "lucide-react";
import useLegalDetails, { LegalDetailsState } from "@/utils/useLegalDetails";

const LegalDetails = () => {
  const { user } = useAuth();
  const {
    legalDetails: fetchedDetails,
    isLoading: isLoadingDetails,
    uploadDocument,
    removeDocument,
    isUploadingDocument,
    isRemovingDocument,
    saveLegalDetails: saveDetails,
    isSavingLegalDetails,
  } = useLegalDetails();

  const [legalDetails, setLegalDetails] = useState<LegalDetailsState>({
    legalName: "",
    registrationNumber: "",
    taxNumber: "",
    contactPhone: "",
    bankAccount: "",
    legalActivity: "",
    national_id: "",
    tax_certificate: "",
    commercial_registry: "",
  });

  const [isLoading, setIsLoading] = useState(false);

  // Update local state when fetched data changes
  useEffect(() => {
    if (fetchedDetails) {
      setLegalDetails({
        legalName: fetchedDetails.legal_name || "",
        registrationNumber: fetchedDetails.registration_number || "",
        taxNumber: fetchedDetails.tax_number || "",
        contactPhone: fetchedDetails.contact_phone || "",
        bankAccount: fetchedDetails.bank_account || "",
        legalActivity: fetchedDetails.legal_activity || "",
        national_id: fetchedDetails.national_id || "",
        tax_certificate: fetchedDetails.tax_certificate || "",
        commercial_registry: fetchedDetails.commercial_registry || "",
      });
      setIsLoading(false);
    }
  }, [fetchedDetails]);

  useEffect(() => {
    if (
      !fetchedDetails ||
      Object.values(fetchedDetails).every((value) => value === "")
    ) {
      toast.info("يجب عليك اكمال البيانات القانونية", {
        description: "الرجاء اكمال البيانات القانونية قبل المتابعة",
      });
    }
  }, [fetchedDetails]);

  const handleFileUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    documentType: string
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error(
        `File size exceeds the 2MB limit. Please choose a smaller file.`
      );
      return;
    }

    let documentExists = false;
    if (
      documentType === "commercialRegistry" &&
      legalDetails.commercial_registry
    ) {
      documentExists = true;
    } else if (
      documentType === "taxCertificate" &&
      legalDetails.tax_certificate
    ) {
      documentExists = true;
    } else if (documentType === "nationalId" && legalDetails.national_id) {
      documentExists = true;
    }

    if (documentExists) {
      const confirmReplace = window.confirm(
        "A document of this type already exists. Do you want to replace it?"
      );
      if (confirmReplace) {
        removeDocument(documentType);
        uploadDocument(
          { documentType, file },
          {
            onSuccess: (data) => {
              // Update local state after upload
              setLegalDetails((prev) => ({
                ...prev,
                [data.field]: data.publicUrl,
              }));
            },
          }
        );
      }
    } else {
      uploadDocument(
        { documentType, file },
        {
          onSuccess: (data) => {
            // Update local state after upload
            setLegalDetails((prev) => ({
              ...prev,
              [data.field]: data.publicUrl,
            }));
          },
        }
      );
    }
  };

  const handleRemoveDocument = (documentType: string) => {
    removeDocument(documentType, {
      onSuccess: (data) => {
        // Update local state after removal
        setLegalDetails((prev) => ({
          ...prev,
          [data.field]: "",
        }));
      },
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    saveDetails(legalDetails);
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>البياانات القانونية</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div>
                  <Label htmlFor="legalName">الاسم القانوني</Label>
                  <Input
                    id="legalName"
                    value={legalDetails.legalName}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        legalName: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="registrationNumber">رقم السجل التجاري</Label>
                  <Input
                    id="registrationNumber"
                    value={legalDetails.registrationNumber}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        registrationNumber: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="taxNumber">رقم الضريبة</Label>
                  <Input
                    id="taxNumber"
                    value={legalDetails.taxNumber}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        taxNumber: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="contactPhone">رقم الهاتف</Label>
                  <Input
                    id="contactPhone"
                    value={legalDetails.contactPhone}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        contactPhone: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="bankAccount">رقم الحساب البنكي</Label>
                  <Input
                    id="bankAccount"
                    value={legalDetails.bankAccount}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        bankAccount: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="legalActivity">النشاط القانوني</Label>
                  <Input
                    id="legalActivity"
                    value={legalDetails.legalActivity}
                    onChange={(e) =>
                      setLegalDetails({
                        ...legalDetails,
                        legalActivity: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div>
                  <Label className="mb-3 block">المستندات</Label>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      {
                        id: "commercialRegistry",
                        label: "وثيقة العمل الحر / السجل التجاري ",
                        existingDoc: legalDetails.commercial_registry,
                      },
                      {
                        id: "taxCertificate",
                        label: "شهادة التسجيل في الضريبة (إن وجدت)",
                        existingDoc: legalDetails.tax_certificate,
                      },
                    ].map((doc) => {
                      const { existingDoc } = doc;

                      return (
                        <div key={doc.id} className="relative">
                          <Input
                            type="file"
                            className="hidden"
                            onChange={(e) => handleFileUpload(e, doc.id)}
                            accept="image/*,application/pdf"
                            id={doc.id}
                          />
                          <Label
                            htmlFor={doc.id}
                            className="cursor-pointer border border-dashed border-gray-300 rounded-md aspect-square flex flex-col items-center justify-center text-center p-4 hover:bg-gray-50 transition-colors"
                          >
                            {existingDoc ? (
                              <>
                                {existingDoc.endsWith(".pdf") ? (
                                  <div className="w-16 h-16 mb-2 flex justify-center">
                                    <FileText className="w-full h-full text-primary" />
                                  </div>
                                ) : (
                                  <div className="w-full aspect-square mb-2 overflow-hidden rounded bg-gray-100">
                                    <img
                                      src={existingDoc}
                                      alt={doc.label}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                )}
                                <div className="flex items-center text-sm font-medium text-green-600 mt-2">
                                  <Check className="w-4 h-4 mr-1" />
                                  Uploaded
                                </div>
                                <div className="text-xs text-gray-500 mt-1 truncate w-full">
                                  {existingDoc.split("/").at(-1)}
                                </div>
                              </>
                            ) : (
                              <>
                                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                                  <Upload className="w-6 h-6 text-gray-500" />
                                </div>
                                <span className="text-sm font-medium text-gray-900">
                                  {doc.label}
                                </span>
                                <span className="text-xs text-gray-500 mt-1">
                                  Click to upload
                                </span>
                                <span className="text-xs text-gray-400 mt-1">
                                  Max 2MB
                                </span>
                              </>
                            )}
                          </Label>
                          {existingDoc && (
                            <>
                              <a
                                href={existingDoc}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <ExternalLink className="w-4 h-4" />
                              </a>
                              <button
                                type="button"
                                className="absolute top-2 left-2 bg-white rounded-full p-1 shadow-sm text-red-500 hover:bg-red-50"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleRemoveDocument(doc.id);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
              <Button
                type="submit"
                className="mt-4"
                disabled={
                  isLoadingDetails ||
                  isUploadingDocument ||
                  isRemovingDocument ||
                  isSavingLegalDetails
                }
              >
                {isLoadingDetails ||
                isUploadingDocument ||
                isRemovingDocument ||
                isSavingLegalDetails
                  ? "جاري الحفظ..."
                  : "حفظ التفاصيل"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default LegalDetails;
