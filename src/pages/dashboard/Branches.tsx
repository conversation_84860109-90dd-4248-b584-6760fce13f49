import React, { useCallback, useEffect, useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2 } from "lucide-react";
import { toast } from "sonner";

import { Branch } from "@/types/branch";
import { useAuth } from "@/contexts/auth";
import BranchCard from "@/components/branches/BranchCard";
import BranchDialog from "@/components/branches/BranchDialog";
import DeleteBranchDialog from "@/components/branches/DeleteBranchDialog";
import { useBranch } from "@/contexts/BranchContext";
import { BranchFormValues } from "@/components/branches/BranchForm";

const Branches = () => {
  const { user } = useAuth();
  const {
    branches,
    isLoading,
    error,
    createBranch,
    updateBranch,
    deleteBranch,
    loadBranches,
  } = useBranch();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentBranch, setCurrentBranch] = useState<Branch | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handelLoadBranches = useCallback(async () => {
    try {
      await loadBranches();
    } catch (error) {
      console.error("Error loading branches:", error);
      toast.error("حدث خطأ أثناء تحميل الفروع");
    }
  }, [loadBranches]);

  useEffect(() => {
    if (user) {
      handelLoadBranches();
    }
  }, [user, handelLoadBranches]);

  const handleAddBranch = () => {
    setCurrentBranch(null);
    setIsDialogOpen(true);
  };

  const handleEditBranch = (branch: Branch) => {
    setCurrentBranch(branch);
    setIsDialogOpen(true);
  };

  const handleDeleteBranch = (branch: Branch) => {
    setCurrentBranch(branch);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteBranch = async () => {
    if (!currentBranch) return;

    try {
      setIsSubmitting(true);
      await deleteBranch(currentBranch.id);
      toast.success("تم حذف الفرع بنجاح");
    } catch (err) {
      console.error("Error deleting branch:", err);
      toast.error("فشل في حذف الفرع");
    } finally {
      setIsSubmitting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleSubmitBranch = async (
    values: BranchFormValues & { images: string[] }
  ) => {
    setIsSubmitting(true);

    try {
      if (currentBranch) {
        // تحديث الفرع الحالي
        await updateBranch(currentBranch.id, {
          store_id: currentBranch.store_id,
          name: values.name,
          address: values.address,
          city: values.city,
          phone_number: values.phoneNumber,
          manager_name: values.managerName,
          working_hours: values.workingHours,
          legal_activity: values.legalActivity,
          active: values.active,
          images: values.images,
        });
        toast.success("تم تحديث الفرع بنجاح");
      } else {
        // إنشاء فرع جديد
        await createBranch({
          name: values.name,
          address: values.address,
          city: values.city,
          phone_number: values.phoneNumber,
          manager_name: values.managerName,
          working_hours: values.workingHours,
          legal_activity: values.legalActivity,
          active: values.active,
          images: values.images,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        toast.success("تم إنشاء الفرع بنجاح");
      }

      return true;
    } catch (err) {
      console.error("Error saving branch:", err);
      toast.error("فشل في حفظ الفرع");
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isLoading && branches.length === 0 && error?.includes("متجر")) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <h1 className="text-2xl font-bold tracking-tight mb-4">
            إدارة الفروع
          </h1>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center p-6">
                <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">لا توجد متاجر</h3>
                <p className="text-muted-foreground mb-4">
                  يجب إنشاء متجر أولاً قبل إضافة فروع. الرجاء الذهاب إلى صفحة
                  المتاجر لإنشاء متجر جديد.
                </p>
                <Button onClick={handleAddBranch}>إنشاء متجر جديد</Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <BranchDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          onSubmit={handleSubmitBranch}
          branch={currentBranch}
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold tracking-tight">إدارة الفروع</h1>
          <Button onClick={handleAddBranch}>
            <Building2 className="h-4 w-4 ml-2" />
            إضافة فرع جديد
          </Button>
        </div>

        {isLoading ? (
          <p className="text-center p-6">جاري التحميل...</p>
        ) : error && !error.includes("متجر") ? (
          <Card>
            <CardContent className="p-6">
              <p className="text-red-500">حدث خطأ: {error}</p>
              <Button onClick={handelLoadBranches} className="mt-4">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        ) : branches.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>قائمة الفروع</CardTitle>
            </CardHeader>
            <CardContent
              className={"flex flex-col items-center justify-center gap-14"}
            >
              <p className="text-center">
                لا توجد فروع. يمكنك إضافة فرع جديد بالضغط على زر "إضافة فرع
                جديد".
              </p>
              <Button onClick={handleAddBranch}>إنشاء متجر جديد</Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {branches.map((branch) => (
              <BranchCard
                key={branch.id}
                branch={branch}
                onEdit={() => handleEditBranch(branch)}
                onDelete={() => handleDeleteBranch(branch)}
              />
            ))}
          </div>
        )}

        <BranchDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          onSubmit={handleSubmitBranch}
          branch={currentBranch}
        />

        <DeleteBranchDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={confirmDeleteBranch}
          branchName={currentBranch?.name || ""}
        />
      </div>
    </DashboardLayout>
  );
};

export default Branches;
