import React from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { User as UserIcon, Mail, Phone, MapPin, Building2, Receipt, BanknoteIcon, Activity } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/contexts/auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { formatDate } from '@/lib/utils';

export default function Profile() {
  const { user } = useAuth();
  console.log(user)
  const navigate = useNavigate();

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-[50vh]">
          <p>جاري التحميل...</p>
        </div>
      </DashboardLayout>
    );
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-red-100 text-red-800 border-red-200">مدير</Badge>;
      case 'sub-admin':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">مساعد مدير</Badge>;
      case 'store':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">محل تجاري</Badge>;
      case 'ecommerce':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">متجر إلكتروني</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{role}</Badge>;
    }
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="الملف الشخصي"
        description="عرض معلوماتك الشخصية وبيانات الحساب"
        icon={UserIcon}
        actions={
          <Button onClick={() => navigate('/dashboard/settings')}>
            تعديل البيانات
          </Button>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarImage
                  src={user.avatar}
                  alt={user.name}
                />
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-xl">{user.name}</CardTitle>
            <div className="mt-2 flex justify-center">
              {getRoleBadge(user.role)}
            </div>
            <CardDescription className="mt-2">
              مسجل منذ {user.created_at ? formatDate(user.created_at) : '-'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start">
                <Mail className="h-5 w-5 ml-3 mt-0.5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-500">البريد الإلكتروني</p>
                  <p className="text-sm">{user.email}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <Phone className="h-5 w-5 ml-3 mt-0.5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-500">رقم الهاتف</p>
                  <p className="text-sm">{user.phone_number || 'غير محدد'}</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <MapPin className="h-5 w-5 ml-3 mt-0.5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-500">العنوان</p>
                  <p className="text-sm">{user.address || 'غير محدد'}</p>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex items-start">
                <BanknoteIcon className="h-5 w-5 ml-3 mt-0.5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-500">الرصيد</p>
                  <p className="text-base font-semibold text-green-600">
                    {user.balance?.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR' }) || '0 ريال'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>معلومات المنشأة</CardTitle>
            <CardDescription>معلومات المنشأة والبيانات القانونية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {user.role === 'store' && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6">
                  <div>
                    <p className="text-sm font-medium text-gray-500">اسم المنشأة</p>
                    <p className="text-base">{user.companyName || user.name}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">الرقم الضريبي</p>
                    <p className="text-base">{user.taxNumber || 'غير محدد'}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">السجل التجاري</p>
                    <p className="text-base">{user.commercialRegistration || 'غير محدد'}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">النشاط التجاري</p>
                    <p className="text-base">{user.activity || user.legal_activity || 'غير محدد'}</p>
                  </div>
                </div>
                
                <Separator />
              </>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6">
              {user.legal_activity && (
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-500">وصف النشاط</p>
                  <p className="text-base">{user.legal_activity}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm font-medium text-gray-500">اسم البنك</p>
                <p className="text-base">{user.bankName || 'غير محدد'}</p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-500">رقم الحساب</p>
                <p className="text-base">{user.accountNumber || 'غير محدد'}</p>
              </div>
              
              <div className="md:col-span-2">
                <p className="text-sm font-medium text-gray-500">رقم الآيبان (IBAN)</p>
                <p className="text-base font-mono">{user.iban || user.bank_account || 'غير محدد'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* الأنشطة الحديثة يمكن إضافتها هنا */}
    </DashboardLayout>
  );
}
