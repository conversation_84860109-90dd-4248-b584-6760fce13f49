import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { HostingRequest } from "@/types";
import { HostingRequestDetailCard } from "@/components/hosting/HostingRequestDetailCard";
import { useHostingRequests } from "@/hooks/hosting/useHostingRequests";
import { Loading } from "@/components/ui/loading";
import { useAuth } from "@/contexts/auth";
import { OrderStatus } from "@/types/order";
import { EmptyState } from "@/components/EmptyState";
import { FileText } from "lucide-react"; // Import a specific icon
import { useHostingRequestsQuery } from "@/hooks/query/useHostingRequestsQuery";

const HostingRequestDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const {
    requests,
    getRequestById,
    updateStatus,
    isMutating,
    selectedHostingRequest,
    currentReqLoading: isLoading,
  } = useHostingRequests();

  const [request, setRequest] = useState<HostingRequest | null>(
    selectedHostingRequest
  );

  useEffect(() => {
    if (selectedHostingRequest) {
      setRequest(selectedHostingRequest);
    }
  }, [selectedHostingRequest, request]);

  const handleUpdateStatus = async (status: OrderStatus) => {
    if (id && updateStatus) {
      await updateStatus(id, status);
      // Refresh request data after update
      const updatedRequest = getRequestById(id);
      setRequest(updatedRequest);
    }
  };

  const canApprove = user?.role === "store" && request?.status === "pending";
  const canCancel =
    (user?.role === "store" || user?.id === request?.ecommerce_id) &&
    ["pending", "processing"].includes(request?.status || "");

  if (isLoading && !isMutating) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-96">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  if (!request) {
    return (
      <DashboardLayout>
        <EmptyState
          title="الطلب غير موجود"
          description="لم يتم العثور على طلب العرض المطلوب"
          icon={FileText} // Use an actual Lucide icon component here
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="تفاصيل طلب العرض"
        description={`استعراض تفاصيل طلب عرض المنتجات رقم ${id?.substring(
          0,
          8
        )}`}
      />

      <div className="space-y-6">
        <HostingRequestDetailCard
          request={request}
          onStatusUpdate={handleUpdateStatus}
          canApprove={canApprove}
          canCancel={canCancel}
          isMutating={isMutating}
        />
      </div>
    </DashboardLayout>
  );
};

export default HostingRequestDetailPage;
