
import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { 
  Wallet, 
  Search, 
  Download, 
  ArrowUpDown, 
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Card, 
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/auth';
import { Loading } from '@/components/ui/loading';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useFinance } from '@/contexts/FinanceContext';
import { Transaction, TransactionStatus } from '@/types';
import { formatDate } from '@/lib/utils';
import { toast } from 'sonner';

export default function AdminDepositsPage() {
  const { user } = useAuth();
  const { transactions, isLoading: financeLoading, updateTransactionStatus } = useFinance();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState('all');

  // Filter transactions based on search query and status
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch =
      transaction.user_name?.toLowerCase().includes(searchQuery.toLowerCase());
      
    const matchesStatus = 
      statusFilter === 'all' || 
      transaction.status === statusFilter;

    const matchesTab = activeTab === 'all' || 
      (activeTab === 'pending' && transaction.status === 'pending') ||
      (activeTab === 'completed' && transaction.status === 'completed') ||
      (activeTab === 'failed' && (transaction.status === 'failed' || transaction.status === 'rejected' || transaction.status === 'cancelled'));
      
    return matchesSearch && matchesStatus && matchesTab;
  });

  // Sort transactions by created date
  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
  });

  if (!user?.permissions?.includes('approve_payments')) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <AlertTriangle className="h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-4">غير مصرح</h2>
          <p className="text-muted-foreground mb-6">ليس لديك صلاحية لرؤية هذه الصفحة</p>
        </div>
      </DashboardLayout>
    );
  }

  if (financeLoading) {
    return (
      <DashboardLayout>
        <div className="h-[60vh] flex items-center justify-center">
          <Loading text="جاري تحميل المعاملات..." size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  const pendingCount = transactions.filter(t => t.status === 'pending').length;
  const completedCount = transactions.filter(t => t.status === 'completed').length;
  const failedCount = transactions.filter(t => t.status === 'failed' || t.status === 'rejected' || t.status === 'cancelled').length;

  const updateStatus = (id: string, status: TransactionStatus) => {
    try {
      updateTransactionStatus(id, status);
      toast.success(`تم تحديث حالة المعاملة إلى ${status}`);
    } catch (error) {
      console.error('Error updating transaction status:', error);
      toast.error('حدث خطأ أثناء تحديث حالة المعاملة');
    }
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="إدارة الإيداعات"
        description="عرض وإدارة طلبات الإيداع"
        icon={Wallet}
      />

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="all">
            الكل ({transactions.length})
          </TabsTrigger>
          <TabsTrigger value="pending">
            قيد الانتظار ({pendingCount})
          </TabsTrigger>
          <TabsTrigger value="completed">
            مكتملة ({completedCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card className="mt-0">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="relative w-full sm:w-72">
                  <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="بحث عن اسم المستخدم..."
                    className="pr-9"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-40">
                      <Filter className="ml-2 h-4 w-4" />
                      <span>{statusFilter === 'all' ? 'كل الحالات' : 
                        statusFilter === 'pending' ? 'قيد الانتظار' :
                        statusFilter === 'completed' ? 'مكتملة' :
                        statusFilter === 'failed' ? 'فاشلة' :
                        statusFilter === 'rejected' ? 'مرفوضة' :
                        statusFilter === 'cancelled' ? 'ملغية' : 'كل الحالات'
                      }</span>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">كل الحالات</SelectItem>
                      <SelectItem value="pending">قيد الانتظار</SelectItem>
                      <SelectItem value="completed">مكتملة</SelectItem>
                      <SelectItem value="failed">فاشلة</SelectItem>
                      <SelectItem value="rejected">مرفوضة</SelectItem>
                      <SelectItem value="cancelled">ملغية</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button 
                    variant="outline" 
                    onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                    className="flex items-center"
                  >
                    <ArrowUpDown className="h-4 w-4 ml-2" />
                    {sortOrder === 'desc' ? 'الأحدث أولاً' : 'الأقدم أولاً'}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المستخدم</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Wallet className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد معاملات مطابقة لبحثك</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedTransactions.map(transaction => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.user_name}</p>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.amount}</TableCell>
                        <TableCell>
                          {transaction.status === 'pending' ? (
                            <Badge className="bg-yellow-50 text-yellow-600 border-yellow-200">قيد الانتظار</Badge>
                          ) : transaction.status === 'completed' ? (
                            <Badge className="bg-green-50 text-green-600 border-green-200">مكتملة</Badge>
                          ) : (
                            <Badge className="bg-red-50 text-red-600 border-red-200">{transaction.status}</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(transaction.created_at)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {transaction.status === 'pending' ? (
                            <>
                              <Button size={isMobile ? "sm" : "default"} variant="outline" onClick={() => updateStatus(transaction.id, 'completed')}>
                                <CheckCircle className="h-4 w-4 ml-2" />
                                قبول
                              </Button>
                              <Button size={isMobile ? "sm" : "default"} variant="destructive" onClick={() => updateStatus(transaction.id, 'rejected' as TransactionStatus)}>
                                <XCircle className="h-4 w-4 ml-2" />
                                رفض
                              </Button>
                            </>
                          ) : (
                            <span className="text-sm text-muted-foreground">
                              {transaction.status}
                            </span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between pt-6">
              <div className="text-sm text-muted-foreground">
                إجمالي المعاملات: {sortedTransactions.length}
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <Card className="mt-0">
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المستخدم</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Clock className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد معاملات قيد الانتظار</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedTransactions.map(transaction => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.user_name}</p>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.amount}</TableCell>
                        <TableCell>
                          <Badge className="bg-yellow-50 text-yellow-600 border-yellow-200">قيد الانتظار</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(transaction.created_at)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button size={isMobile ? "sm" : "default"} variant="outline" onClick={() => updateStatus(transaction.id, 'completed')}>
                            <CheckCircle className="h-4 w-4 ml-2" />
                            قبول
                          </Button>
                          <Button size={isMobile ? "sm" : "default"} variant="destructive" onClick={() => updateStatus(transaction.id, 'rejected' as TransactionStatus)}>
                            <XCircle className="h-4 w-4 ml-2" />
                            رفض
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card className="mt-0">
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم المستخدم</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الطلب</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <CheckCircle className="h-12 w-12 mb-2 opacity-20" />
                          <p>لا توجد معاملات مكتملة</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedTransactions.map(transaction => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{transaction.user_name}</p>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.amount}</TableCell>
                        <TableCell>
                          <Badge className="bg-green-50 text-green-600 border-green-200">مكتملة</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(transaction.created_at)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="text-sm text-muted-foreground">مكتملة</span>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  );
}
