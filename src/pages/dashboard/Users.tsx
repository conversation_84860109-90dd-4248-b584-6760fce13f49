
import React, { useEffect, useState } from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { User, UserRole } from '@/types';
import { Loading } from '@/components/ui/loading';
import { Pencil, Trash2, UserPlus } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

export default function UsersPage() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // In a real app, fetch users from an API
    setTimeout(() => {
      setUsers([
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['manage_users', 'approve_payments'],
          avatar: 'https://ui-avatars.com/api/?name=Ahmed+Mohamed&background=random',
          phone_number: '+966501234567',
          balance: 0,
          created_at: '2024-01-01T12:00:00Z',
          address: 'الرياض، السعودية',
          active: true // Add active status
        },
        {
          id: '2',
          name: 'متجر الأمير',
          email: '<EMAIL>',
          role: 'store',
          permissions: [],
          avatar: 'https://ui-avatars.com/api/?name=PrinceStore&background=random',
          phone_number: '+966512345678',
          balance: 5000,
          created_at: '2024-01-15T12:00:00Z',
          address: 'جدة، السعودية',
          active: true // Add active status
        },
        {
          id: '3',
          name: 'متجر الكترونيات الفيصل',
          email: '<EMAIL>',
          role: 'store',
          permissions: [],
          avatar: 'https://ui-avatars.com/api/?name=FaisalElectronics&background=random',
          phone_number: '+966523456789',
          balance: 7500,
          created_at: '2024-02-01T12:00:00Z',
          address: 'الدمام، السعودية',
          active: true // Add active status
        },
        {
          id: '4',
          name: 'متجر الكتروني للعطور',
          email: '<EMAIL>',
          role: 'ecommerce',
          permissions: [],
          avatar: 'https://ui-avatars.com/api/?name=Perfume+Ecommerce&background=random',
          phone_number: '+966534567890',
          balance: 3000,
          created_at: '2024-02-15T12:00:00Z',
          address: 'الرياض، السعودية',
          active: true // Add active status
        },
        {
          id: '5',
          name: 'متجر الكتروني للإلكترونيات',
          email: '<EMAIL>',
          role: 'ecommerce',
          permissions: [],
          avatar: 'https://ui-avatars.com/api/?name=Electronics+Ecommerce&background=random',
          phone_number: '+966545678901',
          balance: 4500,
          created_at: '2024-03-01T12:00:00Z',
          address: 'جدة، السعودية',
          active: true // Add active status
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setDetailsOpen(true);
  };

  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return <Badge variant="default">مدير</Badge>;
      case 'store':
        return <Badge className="bg-blue-500">متجر</Badge>;
      case 'ecommerce':
        return <Badge className="bg-purple-500">متجر إلكتروني</Badge>;
      case 'sub-admin':
        return <Badge className="bg-orange-500">مشرف</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  const filteredUsers = users.filter(user => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      user.name.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      user.role.toLowerCase().includes(query) ||
      (user.phone_number && user.phone_number.includes(query))
    );
  });

  return (
    <DashboardLayout>
      <PageHeader
        title="المستخدمين"
        description="إدارة المستخدمين والمتاجر والمتاجر الإلكترونية"
      />
      
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="البحث عن مستخدم..."
            className="max-w-xs"
          />
          <Button onClick={() => navigate('/dashboard/user-management')}>
            <UserPlus className="h-4 w-4 mr-2" />
            إضافة مستخدم
          </Button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loading />
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">
                    <Checkbox />
                  </TableHead>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>الدور</TableHead>
                  <TableHead>رقم الجوال</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead className="text-left">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Checkbox />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full overflow-hidden">
                          <img
                            src={user.avatar}
                            alt={user.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-xs text-muted-foreground">{user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getRoleBadge(user.role)}</TableCell>
                    <TableCell>{user.phone_number || '-'}</TableCell>
                    <TableCell>{formatDate(user.created_at || '')}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" onClick={() => handleViewUser(user)}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">تعديل</span>
                        </Button>
                        <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">حذف</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
      
      {selectedUser && (
        <Sheet open={detailsOpen} onOpenChange={setDetailsOpen}>
          <SheetContent className="sm:max-w-xl overflow-y-auto">
            <SheetHeader>
              <SheetTitle>تفاصيل المستخدم</SheetTitle>
              <SheetDescription>
                عرض وتعديل معلومات المستخدم
              </SheetDescription>
            </SheetHeader>
            
            <div className="py-6">
              <div className="flex items-center gap-4 mb-6">
                <div className="h-16 w-16 rounded-full overflow-hidden">
                  <img
                    src={selectedUser.avatar}
                    alt={selectedUser.name}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium">{selectedUser.name}</h3>
                  <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
                  <div className="mt-1">{getRoleBadge(selectedUser.role)}</div>
                </div>
              </div>
              
              <Tabs defaultValue="details">
                <TabsList className="mb-4">
                  <TabsTrigger value="details">المعلومات الأساسية</TabsTrigger>
                  <TabsTrigger value="financial">المعلومات المالية</TabsTrigger>
                  <TabsTrigger value="permissions">الصلاحيات</TabsTrigger>
                </TabsList>
                
                <TabsContent value="details">
                  <Card>
                    <CardContent className="pt-6 space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium mb-1">المعرف</h4>
                          <p className="text-sm text-muted-foreground">{selectedUser.id}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium mb-1">تاريخ الإنشاء</h4>
                          <p className="text-sm text-muted-foreground">{formatDate(selectedUser.created_at || '')}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium mb-1">رقم الجوال</h4>
                        <p className="text-sm">{selectedUser.phone_number || 'غير محدد'}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium mb-1">العنوان</h4>
                        <p className="text-sm">{selectedUser.address || 'غير محدد'}</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="financial">
                  <Card>
                    <CardContent className="pt-6 space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-1">الرصيد الحالي</h4>
                        <p className="text-lg font-semibold">{selectedUser.balance} ريال</p>
                      </div>
                      
                      <Button variant="outline" className="w-full">عرض المعاملات المالية</Button>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="permissions">
                  <Card>
                    <CardContent className="pt-6">
                      <h4 className="text-sm font-medium mb-3">الصلاحيات</h4>
                      {selectedUser.permissions && selectedUser.permissions.length > 0 ? (
                        <div className="space-y-2">
                          {selectedUser.permissions.map((permission) => (
                            <div key={permission} className="flex items-center gap-2">
                              <Checkbox checked disabled />
                              <span>{permission}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">لا توجد صلاحيات خاصة</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
              
              <div className="mt-6 flex justify-end gap-4">
                <Button variant="outline" onClick={() => setDetailsOpen(false)}>
                  إغلاق
                </Button>
                <Button>حفظ التغييرات</Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      )}
    </DashboardLayout>
  );
}
