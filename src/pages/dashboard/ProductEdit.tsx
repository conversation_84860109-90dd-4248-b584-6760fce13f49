
import React from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card } from '@/components/ui/card';
import { useParams } from 'react-router-dom';

export default function ProductEdit() {
  const { id } = useParams();
  
  return (
    <DashboardLayout>
      <PageHeader 
        title="تعديل المنتج" 
        description={`تعديل بيانات المنتج (${id})`}
      />
      <Card className="p-6">
        <p className="text-center text-gray-500">نموذج تعديل المنتج سيظهر هنا</p>
      </Card>
    </DashboardLayout>
  );
}
