import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { UserPlus, Pencil, Trash2 } from "lucide-react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { EmployeeWizard } from "@/components/employees/EmployeeWizard";
import { StoreEmployee } from "@/types";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useEmployees } from "@/hooks/query/useEmployees";

interface StoreEmployeeData {
  id: string;
  name: string;
  email: string;
  phone_number: string;
  position: string;
  store_id: string;
  branch_id: string | null;
  active: boolean;
  permissions: string[];
  created_at: string;
  updated_at: string;
  user_id: string;
}

const Employees = () => {
  const { user } = useAuth();
  const {
    employees,
    isLoading,
    deleteEmployee: handleDelete,
    error,
    refetch,
    addEmployee,
    updateEmployee,
  } = useEmployees();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] =
    useState<StoreEmployee | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (user?.id) {
      refetch();
    }
  }, [user, refetch]);

  const handleCreate = () => {
    setSelectedEmployee(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (employee: StoreEmployee) => {
    setSelectedEmployee(employee);
    setIsDialogOpen(true);
  };

  const handleSubmit = async (employeeData: StoreEmployee) => {
    setIsSubmitting(true);
    console.log(employeeData);

    try {
      if (selectedEmployee) {
        updateEmployee({
          ...employeeData,
          id: selectedEmployee.id,
        } as StoreEmployeeData);
      } else {
        addEmployee(employeeData);
      }
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error submitting employee data:", error);
      toast.error("حدث خطأ أثناء معالجة الطلب. يرجى المحاولة لاحقًا.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">الموظفين</h1>
          <Button
            onClick={() => {
              setSelectedEmployee(null);
              setIsDialogOpen(true);
            }}
            className="flex items-center gap-2"
          >
            <UserPlus className="h-4 w-4" />
            إضافة موظف
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>قائمة الموظفين</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                جاري تحميل بيانات الموظفين...
              </div>
            ) : employees?.length === 0 ? (
              <div className="text-center py-8">
                لا يوجد موظفين. أضف موظفك الأول للبدء.
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الاسم</TableHead>
                    <TableHead>البريد الإلكتروني</TableHead>
                    <TableHead>المنصب</TableHead>
                    <TableHead>الهاتف</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead className="text-left">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employees?.map((employee: StoreEmployee) => (
                    <TableRow key={employee.id}>
                      <TableCell className="font-medium">
                        {employee.name}
                      </TableCell>
                      <TableCell>{employee.email}</TableCell>
                      <TableCell>{employee.position}</TableCell>
                      <TableCell>{employee.phone_number}</TableCell>
                      <TableCell>
                        <div
                          className={`px-2 py-1 rounded-full text-xs inline-flex items-center ${
                            employee.active
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {employee.active ? "نشط" : "غير نشط"}
                        </div>
                      </TableCell>
                      <TableCell className="text-left">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(employee)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(employee.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {isDialogOpen && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {selectedEmployee ? "تعديل موظف" : "إضافة موظف جديد"}
                </DialogTitle>
              </DialogHeader>
              <EmployeeWizard
                initialData={selectedEmployee || undefined}
                onSubmit={handleSubmit}
                onCancel={() => setIsDialogOpen(false)}
                isSubmitting={isSubmitting}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Employees;
