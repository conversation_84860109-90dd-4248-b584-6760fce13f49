import EmptyState from "@/components/EmptyState";
import { StoreCard } from "@/components/stores/StoreCard";
import { StoreDetails } from "@/components/stores/StoreDetails";
import { StoreFilters } from "@/components/stores/StoreFilters";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loading } from "@/components/ui/loading";
import { PageHeader } from "@/components/ui/page-header";
import { useAuth } from "@/contexts/auth";
import { useStores } from "@/hooks/store/useStores";
import { useToast } from "@/hooks/toast";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import type { Store as StoreType } from "@/types/store";
import { Store } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

// const STORES_DATA: StoreType[] = [
//   {
//     id: "store-1",
//     name: "متجر الإلكترونيات",
//     description: "متجر متخصص في بيع الإلكترونيات والأجهزة",
//     address: "شارع العليا، الرياض",
//     city: "الرياض",
//     legalActivity: "47191-بيع بالتجزئة",
//     shelfSpace: "كبير",
//     phone: "0112345678",
//     email: "<EMAIL>",
//     ownerId: "user-1",
//     image: "https://source.unsplash.com/random/800x600?electronics",
//     gallery: ["https://source.unsplash.com/random/800x600?electronics"],
//     hours: "9:00 - 21:00",
//     rating: 4.5,
//     capacity: 50,
//     featured: true
//   },
//   {
//     id: "store-2",
//     name: "مقهى النخبة",
//     description: "مقهى راقي يقدم أفضل أنواع القهوة",
//     address: "شارع التحلية، جدة",
//     city: "جدة",
//     legalActivity: "56101-أنشطة المطاعم وخدمات الأطعمة",
//     shelfSpace: "متوسط",
//     phone: "0123456789",
//     email: "<EMAIL>",
//     ownerId: "user-2",
//     image: "https://source.unsplash.com/random/800x600?cafe",
//     gallery: ["https://source.unsplash.com/random/800x600?coffee"],
//     hours: "8:00 - 23:00",
//     rating: 4.8,
//     capacity: 30,
//     featured: true
//   }
// ];

export default function Stores() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCity, setSelectedCity] = useState<string>("");
  const [selectedActivity, setSelectedActivity] = useState<string>("");
  const [shelfSize, setShelfSize] = useState<string>("");
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreType | null>(null);
  const { data: stores, isLoading } = useStores();
  const { user } = useAuth();
  if (isLoading) {
    return (
      <DashboardLayout>
        <Loading />
      </DashboardLayout>
    );
  }
  if (!stores) {
    return (
      <DashboardLayout>
        <EmptyState
          title="لا توجد محلات تجارية"
          description="لا توجد محلات تجارية تطابق معايير البحث"
          icon={Store}
        />
      </DashboardLayout>
    );
  }
  const cities = [...new Set(stores.map((store) => store.city))];
  const activities = [...new Set(stores.map((store) => store.legalActivity))];
  const shelfSizes = [...new Set(stores.map((store) => store.shelfSpace))];

  const filteredStores = stores.filter((store) => {
    const matchesSearch =
      store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      store.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (store.description?.toLowerCase() || "").includes(
        searchQuery.toLowerCase()
      );

    const matchesCity = selectedCity ? store.city === selectedCity : true;
    const matchesActivity = selectedActivity
      ? store.legalActivity === selectedActivity
      : true;
    const matchesShelfSize = shelfSize ? store.shelfSpace === shelfSize : true;

    return matchesSearch && matchesCity && matchesActivity && matchesShelfSize;
  });

  const handleSendRequest = (
    storeId: string,
    storeName: string,
    legalActivity: string
  ) => {
    navigate("/dashboard/hosting-requests");

    toast({
      title: "تم إرسال الطلب",
      description: `تم إرسال طلب استضافة إلى ${storeName} بنجاح`,
    });
  };

  const openStoreDetails = (store: StoreType) => {
    setSelectedStore(store);
  };

  const closeStoreDetails = () => {
    setSelectedStore(null);
  };

  const resetFilters = () => {
    setSearchQuery("");
    setSelectedCity("");
    setSelectedActivity("");
    setShelfSize("");
    setIsFilterSheetOpen(false);
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="المحلات التجارية"
        description="استعراض المحلات التجارية المتاحة للتعاون"
        icon={Store}
      />

      <Alert variant="default" className="mb-4 bg-blue-50">
        <AlertDescription>
          يجب أن يتطابق النشاط التجاري للمتجر مع نشاطك التجاري المسجل في السجل
          التجاري أو وثيقة العمل الحر.
        </AlertDescription>
      </Alert>

      <StoreFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedCity={selectedCity}
        setSelectedCity={setSelectedCity}
        selectedActivity={selectedActivity}
        setSelectedActivity={setSelectedActivity}
        shelfSize={shelfSize}
        setShelfSize={setShelfSize}
        isFilterSheetOpen={isFilterSheetOpen}
        setIsFilterSheetOpen={setIsFilterSheetOpen}
        resetFilters={resetFilters}
        cities={cities as string[]}
        activities={activities as string[]}
        shelfSizes={shelfSizes as string[]}
      />

      {filteredStores.length === 0 ? (
        <EmptyState
          title="لا توجد محلات تجارية"
          description="لا توجد محلات تجارية تطابق معايير البحث"
          icon={Store}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
          {filteredStores.map((store) => (
            <StoreCard
              key={store.id}
              store={store}
              onViewDetails={() => openStoreDetails(store)}
            />
          ))}
        </div>
      )}

      <StoreDetails
        selectedStore={selectedStore}
        onClose={closeStoreDetails}
        onSendRequest={handleSendRequest}
      />
    </DashboardLayout>
  );
}
