import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth';
import { UserRole, AdminPermission, User } from '@/types';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Edit, Trash2, Plus, CheckCheck, X } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from "@/components/ui/dialog";
import { updateUserMetadata } from '@/services/user/updateUserMetadata';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from '@/lib/utils';

import { supabase } from '@/integrations/supabase/client';

interface ProfileRecord extends User {
  active: boolean;
  permissions: AdminPermission[];
}

const AdminManagement: React.FC = () => {
  const [profiles, setProfiles] = useState<ProfileRecord[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const { user: currentUser } = useAuth();
  const [selectedProfile, setSelectedProfile] = useState<ProfileRecord | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [newProfile, setNewProfile] = useState<Partial<ProfileRecord>>({
    name: '',
    email: '',
    role: 'store' as UserRole,
    phone_number: '',
    address: '',
    active: true,
    balance: 0,
    permissions: []
  });
  const [selectedPermissions, setSelectedPermissions] = useState<AdminPermission[]>([]);

  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        const { data, error } = await supabase.from('profiles').select('*');
        if (error) throw error;
        
        const profiles: ProfileRecord[] = data.map(profile => ({
          id: profile.id,
          name: profile.name,
          email: profile.email,
          role: profile.role as UserRole,
          phone_number: profile.phone_number || '',
          address: profile.address || '',
          avatar: profile.avatar || '',
          balance: profile.balance || 0,
          bank_account: profile.bank_account || '',
          legal_activity: profile.legal_activity || '',
          created_at: profile.created_at,
          active: profile.active !== undefined ? profile.active : true,
          permissions: profile.permissions as AdminPermission[] || [],
          updated_at: profile.updated_at || '',
        }));
        
        setProfiles(profiles);
      } catch (error) {
        console.error("Error fetching profiles:", error);
        toast.error("Failed to load user profiles");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchProfiles();
  }, []);

  const handleCreateUser = async (data: Partial<ProfileRecord>) => {
    try {
      setIsLoading(true);
      
      const newUserId = crypto.randomUUID();
      
      const userData = {
        id: newUserId,
        name: data.name || '',
        email: data.email || '',
        role: data.role as UserRole,
        phone_number: data.phone_number,
        address: data.address,
        active: data.active !== undefined ? data.active : true,
        balance: data.balance || 0,
        permissions: data.permissions || []
      };
      
      const { error } = await supabase.from('profiles').insert(userData);
      
      if (error) {
        throw error;
      }
      
      setProfiles([...profiles, userData as ProfileRecord]);
      toast.success('User created successfully');
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error('Failed to create user');
    } finally {
      setIsLoading(false);
      closeAllDialogs();
    }
  };

  const handleUpdateUser = async (profileId: string, updatedData: Partial<ProfileRecord>) => {
    try {
      setIsUpdating(true);
      
      const updateData: any = {
        ...updatedData
      };
      
      if (updatedData.active !== undefined) {
        updateData.active = updatedData.active;
      }
      
      if (updatedData.permissions !== undefined) {
        updateData.permissions = updatedData.permissions;
      }
      
      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', profileId);
      
      if (error) throw error;
      
      setProfiles(profiles.map(profile => 
        profile.id === profileId
          ? { ...profile, ...updatedData }
          : profile
      ));
      
      toast.success('User updated successfully');
      
      updateUserMetadata(profileId, updatedData);
      
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user");
    } finally {
      setIsUpdating(false);
      setSelectedProfile(null);
      closeAllDialogs();
    }
  };

  const handleDeleteUser = async (profileId: string) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase.from('profiles').delete().eq('id', profileId);
      
      if (error) {
        throw error;
      }
      
      setProfiles(profiles.filter(profile => profile.id !== profileId));
      toast.success('User deleted successfully');
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    } finally {
      setIsLoading(false);
      closeAllDialogs();
    }
  };

  const handleTogglePermission = (permission: AdminPermission) => {
    setSelectedPermissions(prevPermissions => {
      if (prevPermissions.includes(permission)) {
        return prevPermissions.filter(p => p !== permission);
      } else {
        return [...prevPermissions, permission];
      }
    });
  };

  const handleSavePermissions = async () => {
    if (!selectedProfile) return;
    
    await handleUpdateUser(selectedProfile.id, { permissions: selectedPermissions });
    closeAllDialogs();
  };

  const closeAllDialogs = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setIsDeleteDialogOpen(false);
    setIsPermissionsDialogOpen(false);
    setSelectedProfile(null);
    setNewProfile({
      name: '',
      email: '',
      role: 'store' as UserRole,
      phone_number: '',
      address: '',
      active: true,
      balance: 0,
      permissions: []
    });
    setSelectedPermissions([]);
  };

  return (
    <DashboardLayout>
      <PageHeader title="إدارة المديرين" description="إدارة صلاحيات المديرين والمشرفين" />

      <div className="container mx-auto py-6">
        <div className="my-4">
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            إضافة مدير جديد
          </Button>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>الدور</TableHead>
                <TableHead>رقم الهاتف</TableHead>
                <TableHead>العنوان</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {profiles.map((profile) => (
                <TableRow key={profile.id}>
                  <TableCell>{profile.name}</TableCell>
                  <TableCell>{profile.email}</TableCell>
                  <TableCell>{profile.role}</TableCell>
                  <TableCell>{profile.phone_number}</TableCell>
                  <TableCell>{profile.address}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => {
                      setSelectedProfile(profile);
                      setIsEditDialogOpen(true);
                    }}>
                      <Edit className="h-4 w-4 mr-2" />
                      تعديل
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => {
                      setSelectedProfile(profile);
                      setIsDeleteDialogOpen(true);
                    }}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      حذف
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => {
                      setSelectedProfile(profile);
                      setSelectedPermissions(profile.permissions || []);
                      setIsPermissionsDialogOpen(true);
                    }}>
                      <CheckCheck className="h-4 w-4 mr-2" />
                      الصلاحيات
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>إنشاء مدير جديد</DialogTitle>
              <DialogDescription>
                أدخل معلومات المدير الجديد لإنشاء حسابه.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  الاسم
                </Label>
                <Input id="name" value={newProfile.name || ''} onChange={(e) => setNewProfile({ ...newProfile, name: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  البريد الإلكتروني
                </Label>
                <Input id="email" type="email" value={newProfile.email || ''} onChange={(e) => setNewProfile({ ...newProfile, email: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  الدور
                </Label>
                <Select onValueChange={(value: UserRole) => setNewProfile({ ...newProfile, role: value })}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="اختر دور المستخدم" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">مدير</SelectItem>
                    <SelectItem value="sub-admin">مشرف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone_number" className="text-right">
                  رقم الهاتف
                </Label>
                <Input id="phone_number" value={newProfile.phone_number || ''} onChange={(e) => setNewProfile({ ...newProfile, phone_number: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="address" className="text-right">
                  العنوان
                </Label>
                <Input id="address" value={newProfile.address || ''} onChange={(e) => setNewProfile({ ...newProfile, address: e.target.value })} className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="secondary" onClick={closeAllDialogs}>
                إلغاء
              </Button>
              <Button type="submit" onClick={() => handleCreateUser(newProfile)} disabled={isLoading}>
                {isLoading ? 'جاري الإنشاء...' : 'إنشاء'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={isEditDialogOpen && selectedProfile !== null} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>تعديل معلومات المدير</DialogTitle>
              <DialogDescription>
                تعديل معلومات المدير الحالية.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  الاسم
                </Label>
                <Input id="name" defaultValue={selectedProfile?.name} onChange={(e) => setSelectedProfile({ ...selectedProfile!, name: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  البريد الإلكتروني
                </Label>
                <Input id="email" type="email" defaultValue={selectedProfile?.email} onChange={(e) => setSelectedProfile({ ...selectedProfile!, email: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  الدور
                </Label>
                <Select defaultValue={selectedProfile?.role} onValueChange={(value: UserRole) => setSelectedProfile({ ...selectedProfile!, role: value })}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="اختر دور المستخدم" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">مدير</SelectItem>
                    <SelectItem value="sub-admin">مشرف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone_number" className="text-right">
                  رقم الهاتف
                </Label>
                <Input id="phone_number" defaultValue={selectedProfile?.phone_number} onChange={(e) => setSelectedProfile({ ...selectedProfile!, phone_number: e.target.value })} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="address" className="text-right">
                  العنوان
                </Label>
                <Input id="address" defaultValue={selectedProfile?.address} onChange={(e) => setSelectedProfile({ ...selectedProfile!, address: e.target.value })} className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="secondary" onClick={closeAllDialogs}>
                إلغاء
              </Button>
              <Button type="submit" onClick={() => handleUpdateUser(selectedProfile!.id, selectedProfile!)} disabled={isUpdating}>
                {isUpdating ? 'جاري التحديث...' : 'تحديث'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={isDeleteDialogOpen && selectedProfile !== null} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>حذف المدير</DialogTitle>
              <DialogDescription>
                هل أنت متأكد أنك تريد حذف هذا المدير؟ سيتم حذف جميع البيانات المرتبطة به.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="button" variant="secondary" onClick={closeAllDialogs}>
                إلغاء
              </Button>
              <Button type="submit" variant="destructive" onClick={() => handleDeleteUser(selectedProfile!.id)} disabled={isLoading}>
                {isLoading ? 'جاري الحذف...' : 'حذف'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={isPermissionsDialogOpen && selectedProfile !== null} onOpenChange={setIsPermissionsDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>تعديل صلاحيات المدير</DialogTitle>
              <DialogDescription>
                تحديد الصلاحيات التي يتمتع بها المدير.
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="h-[300px] w-full rounded-md border p-4">
              <div className="grid gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="manage_users"
                    checked={selectedPermissions.includes('manage_users')}
                    onCheckedChange={() => handleTogglePermission('manage_users')}
                  />
                  <Label htmlFor="manage_users">إدارة المستخدمين</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="approve_payments"
                    checked={selectedPermissions.includes('approve_payments')}
                    onCheckedChange={() => handleTogglePermission('approve_payments')}
                  />
                  <Label htmlFor="approve_payments">الموافقة على المدفوعات</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="manage_transactions"
                    checked={selectedPermissions.includes('manage_transactions')}
                    onCheckedChange={() => handleTogglePermission('manage_transactions')}
                  />
                  <Label htmlFor="manage_transactions">إدارة المعاملات</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="manage_admins"
                    checked={selectedPermissions.includes('manage_admins')}
                    onCheckedChange={() => handleTogglePermission('manage_admins')}
                  />
                  <Label htmlFor="manage_admins">إدارة المديرين</Label>
                </div>
              </div>
            </ScrollArea>
            <DialogFooter>
              <Button type="button" variant="secondary" onClick={closeAllDialogs}>
                إلغاء
              </Button>
              <Button type="submit" onClick={handleSavePermissions} disabled={isUpdating}>
                {isUpdating ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
};

export default AdminManagement;
