
import React from 'react';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/auth';

export default function Contracts() {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  return (
    <DashboardLayout>
      <PageHeader 
        title="العقود" 
        description="إدارة العقود الخاصة بمتجرك"
        icon={FileText}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="text-lg">عقود الاستضافة</CardTitle>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-muted-foreground">
              عرض وإدارة عقود الاستضافة مع المتاجر التقليدية
            </p>
          </CardContent>
          <div className="p-6 pt-0 mt-auto">
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => navigate('/dashboard/invoices')}
            >
              عرض الفواتير
              <ArrowRight className="mr-2 h-4 w-4" />
            </Button>
          </div>
        </Card>
        
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="text-lg">عقود العمل</CardTitle>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-muted-foreground">
              {user?.role === 'ecommerce' 
                ? 'إدارة عقود العمل مع الموظفين والمتعاونين' 
                : 'إدارة عقود العمل مع المتاجر الإلكترونية'}
            </p>
          </CardContent>
          <div className="p-6 pt-0 mt-auto">
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => user?.role === 'ecommerce' 
                ? navigate('/dashboard/employees') 
                : navigate('/dashboard/hosting-requests')}
            >
              {user?.role === 'ecommerce' ? 'إدارة الموظفين' : 'عرض طلبات الاستضافة'}
              <ArrowRight className="mr-2 h-4 w-4" />
            </Button>
          </div>
        </Card>
        
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="text-lg">العقود الجديدة</CardTitle>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-muted-foreground">
              إنشاء عقود جديدة مع المتاجر أو الموظفين
            </p>
          </CardContent>
          <div className="p-6 pt-0 mt-auto">
            <Button 
              variant="default" 
              className="w-full"
              onClick={() => navigate('/dashboard/hosting-requests/new')}
            >
              إنشاء عقد جديد
              <ArrowRight className="mr-2 h-4 w-4" />
            </Button>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
}
