
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/layouts/DashboardLayout';
import { PageHeader } from '@/components/ui/page-header';
import { Card, CardHeader, CardT<PERSON>le, CardContent, CardFooter, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useHosting } from '@/contexts/HostingContext';
import { useAuth } from '@/contexts/auth';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function NewHostingRequest() {
  const { addHostingRequest } = useHosting();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    storeName: '',
    description: '',
  });
  const [errors, setErrors] = useState<{
    storeName?: string;
    general?: string;
  }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user types
    if (name === 'storeName' && errors.storeName) {
      setErrors(prev => ({ ...prev, storeName: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: {
      storeName?: string;
      general?: string;
    } = {};
    
    if (!formData.storeName.trim()) {
      newErrors.storeName = 'يرجى إدخال اسم المحل التجاري';
    }
    
    if (!user) {
      newErrors.general = 'يجب تسجيل الدخول لإرسال طلب';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      addHostingRequest({
        ecommerce_id: user?.id || '',
        ecommerce_name: user?.name || '',
        store_name: formData.storeName,
        notes: formData.description,
        status: 'pending',
      });
      
      toast.success('تم إرسال طلب عرض المنتجات بنجاح');
      navigate('/dashboard/hosting-requests');
    } catch (error) {
      console.error('Error submitting request:', error);
      toast.error('حدث خطأ أثناء إرسال الطلب');
      setErrors({ general: 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <PageHeader 
        title="طلب عرض منتجات جديد" 
        description="قم بإنشاء طلب عرض منتجاتك في محل تجاري" 
        backLink="/dashboard/hosting-requests"
      />
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>تفاصيل الطلب</CardTitle>
          <CardDescription>أدخل معلومات طلب عرض المنتجات الخاص بك</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {errors.general && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.general}</AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="storeName">اسم المحل التجاري <span className="text-red-500">*</span></Label>
              <Input
                id="storeName"
                name="storeName"
                value={formData.storeName}
                onChange={handleChange}
                placeholder="أدخل اسم المحل التجاري الذي ترغب بعرض منتجاتك فيه"
                className={errors.storeName ? "border-red-500" : ""}
                required
              />
              {errors.storeName && (
                <p className="text-sm text-red-500">{errors.storeName}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">وصف الطلب</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="اكتب تفاصيل طلبك هنا..."
                className="h-32"
              />
              <p className="text-xs text-muted-foreground">
                يمكنك كتابة وصف للمنتجات التي ترغب في عرضها وأي متطلبات خاصة.
              </p>
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/dashboard/hosting-requests')}
            >
              إلغاء
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="relative"
            >
              {isSubmitting ? 'جاري الإرسال...' : 'إرسال الطلب'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </DashboardLayout>
  );
}
