import React, { useState, useEffect } from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Plus } from "lucide-react";
import { Transaction, TransactionType } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import { Loading } from "@/components/ui/loading";
import { useNavigate } from "react-router-dom";
import EmptyState from "@/components/EmptyState";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  useTransactionOperations,
  WithdrawalParams,
} from "@/hooks/useTransactionOperations";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  formatCurrency,
  getTransactionStatusColor,
  getTransactionStatusText,
} from "@/lib/transactionUtils";

function Withdrawals() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [withdrawals, setWithdrawals] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userBalance, setUserBalance] = useState(0);
  const [withdrawalAmount, setWithdrawalAmount] = useState("");
  const [withdrawalNotes, setWithdrawalNotes] = useState("");
  const [withdrawalReferenceNumber, setWithdrawalReferenceNumber] =
    useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [withdrawalDialogOpen, setWithdrawalDialogOpen] = useState(false);
  const { withdrawMoney } = useTransactionOperations(user?.id, user?.name);

  // Fetch withdrawals and user balance
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!user?.id) return;

        setIsLoading(true);

        // Fetch balance
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("balance")
          .eq("id", user.id)
          .single();

        if (profileError) throw profileError;

        setUserBalance(profileData?.balance || 0);

        // Fetch withdrawals
        const { data, error } = await supabase
          .from("transactions")
          .select("*")
          .eq("user_id", user.id)
          .eq("type", "withdraw")
          .order("created_at", { ascending: false });

        if (error) throw error;

        setWithdrawals(data as Transaction[]);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("حدث خطأ أثناء جلب البيانات");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user?.id]);

  const handleWithdrawal = async () => {
    if (!validateAmount(withdrawalAmount)) return;

    setIsSubmitting(true);

    try {
      const withdrawalParams: WithdrawalParams = {
        amount: Number(withdrawalAmount),
        description: withdrawalNotes,
      };

      const success = await withdrawMoney(withdrawalParams);

      if (success) {
        toast.success("تم إرسال طلب السحب بنجاح");
        // Add temporary transaction to the list until page refresh
        const newWithdrawal: Transaction = {
          id: `temp-${Date.now()}`,
          user_id: user?.id || "",
          user_name: user?.name || "",
          type: "withdraw" as TransactionType,
          status: "pending",
          amount: Number(withdrawalAmount),
          description: withdrawalNotes || "طلب سحب",
          reference: withdrawalReferenceNumber || undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        setWithdrawals([newWithdrawal, ...withdrawals]);

        // Update local balance
        setUserBalance((prev) => prev - Number(withdrawalAmount));

        setWithdrawalDialogOpen(false);
        setWithdrawalAmount("");
        setWithdrawalNotes("");
        setWithdrawalReferenceNumber("");
      }
    } catch (error) {
      console.error("Error processing withdrawal:", error);
      toast.error("فشل في إرسال طلب السحب");
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateAmount = (amount: string): boolean => {
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error("الرجاء إدخال مبلغ صحيح");
      return false;
    }

    if (Number(amount) > userBalance) {
      toast.error("المبلغ المطلوب للسحب أكبر من رصيدك الحالي");
      return false;
    }

    return true;
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("ar-SA", {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <Loading />
        </div>
      </DashboardLayout>
    );
  }

  const hasBankInfo = !!user?.bank_account;

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-6">
        <PageHeader
          title="عمليات السحب"
          description="إدارة ومتابعة عمليات السحب المالية"
        />

        <Dialog
          open={withdrawalDialogOpen}
          onOpenChange={setWithdrawalDialogOpen}
        >
          <DialogTrigger asChild>
            <Button disabled={!hasBankInfo}>
              <Plus className="h-4 w-4 ml-2" />
              سحب جديد
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>سحب مبلغ</DialogTitle>
              <DialogDescription>
                أدخل المبلغ الذي ترغب بسحبه من رصيدك
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="text-sm">
                <span className="font-medium">الرصيد المتاح: </span>
                <span>{formatCurrency(userBalance)}</span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="withdrawal-amount">المبلغ (ريال سعودي)</Label>
                <Input
                  id="withdrawal-amount"
                  placeholder="أدخل المبلغ"
                  type="number"
                  min={1}
                  max={userBalance}
                  value={withdrawalAmount}
                  onChange={(e) => setWithdrawalAmount(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="withdrawal-reference">
                  رقم المرجع (اختياري)
                </Label>
                <Input
                  id="withdrawal-reference"
                  placeholder="أدخل رقم مرجعي للعملية"
                  type="text"
                  value={withdrawalReferenceNumber}
                  onChange={(e) => setWithdrawalReferenceNumber(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="withdrawal-notes">
                  ملاحظات إضافية (اختياري)
                </Label>
                <Textarea
                  id="withdrawal-notes"
                  placeholder="أي معلومات إضافية عن عملية السحب"
                  value={withdrawalNotes}
                  onChange={(e) => setWithdrawalNotes(e.target.value)}
                />
              </div>

              {hasBankInfo ? (
                <div className="p-4 bg-muted rounded-lg text-sm">
                  <div className="mb-2 font-medium">
                    سيتم التحويل إلى الحساب البنكي التالي:
                  </div>
                  <div>
                    {user?.bankName || "غير محدد"} - {user?.bank_account}
                  </div>
                </div>
              ) : (
                <Alert>
                  <AlertDescription>
                    لم تقم بإضافة معلومات الحساب البنكي.{" "}
                    <Button
                      variant="link"
                      className="h-auto p-0"
                      onClick={() => {
                        setWithdrawalDialogOpen(false);
                        navigate("/dashboard/profile");
                      }}
                    >
                      أضف معلومات الحساب البنكي
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setWithdrawalDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleWithdrawal}
                disabled={isSubmitting || !hasBankInfo}
              >
                {isSubmitting ? "جاري المعالجة..." : "تأكيد السحب"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {!hasBankInfo && (
        <Alert className="mb-4 bg-yellow-50">
          <AlertDescription>
            لم تقم بإضافة معلومات الحساب البنكي بعد. يجب إضافة معلومات الحساب
            البنكي لتتمكن من سحب الأموال.{" "}
            <Button
              variant="link"
              className="h-auto p-0"
              onClick={() => navigate("/dashboard/profile")}
            >
              أضف معلومات الحساب البنكي
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate("/dashboard/wallet")}>
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة إلى المحفظة
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>سجل عمليات السحب</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
              <TabsTrigger value="completed">مكتملة</TabsTrigger>
              <TabsTrigger value="rejected">مرفوضة</TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              {withdrawals.length === 0 ? (
                <EmptyState
                  title="لا توجد عمليات سحب"
                  description="لم تقم بأي عمليات سحب بعد"
                  action={
                    hasBankInfo
                      ? {
                          label: "إضافة سحب جديد",
                          onClick: () => setWithdrawalDialogOpen(true),
                        }
                      : undefined
                  }
                />
              ) : (
                <div className="space-y-4">
                  {withdrawals.map((withdrawal) => (
                    <Card key={withdrawal.id} className="overflow-hidden">
                      <div className="flex items-start p-6 justify-between">
                        <div>
                          <h3 className="font-medium">
                            سحب {formatCurrency(withdrawal.amount)}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {withdrawal.description || "سحب"}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDate(withdrawal.created_at)}
                          </p>
                        </div>
                        <div className="flex flex-col items-end">
                          <span
                            className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                            style={{
                              backgroundColor: getTransactionStatusColor(
                                withdrawal.status
                              ),
                            }}
                          >
                            {getTransactionStatusText(withdrawal.status)}
                          </span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="pending">
              {withdrawals.filter((w) => w.status === "pending").length ===
              0 ? (
                <EmptyState
                  title="لا توجد عمليات سحب قيد الانتظار"
                  description="ليس لديك أي عمليات سحب قيد الانتظار حاليًا"
                />
              ) : (
                <div className="space-y-4">
                  {withdrawals
                    .filter((w) => w.status === "pending")
                    .map((withdrawal) => (
                      <Card key={withdrawal.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              سحب {formatCurrency(withdrawal.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {withdrawal.description || "سحب"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(withdrawal.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  withdrawal.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(withdrawal.status)}
                            </span>
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="completed">
              {withdrawals.filter((w) => w.status === "completed").length ===
              0 ? (
                <EmptyState
                  title="لا توجد عمليات سحب مكتملة"
                  description="لم يتم اكتمال أي عمليات سحب بعد"
                />
              ) : (
                <div className="space-y-4">
                  {withdrawals
                    .filter((w) => w.status === "completed")
                    .map((withdrawal) => (
                      <Card key={withdrawal.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              سحب {formatCurrency(withdrawal.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {withdrawal.description || "سحب"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(withdrawal.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  withdrawal.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(withdrawal.status)}
                            </span>
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="rejected">
              {withdrawals.filter((w) => w.status === "rejected").length ===
              0 ? (
                <EmptyState
                  title="لا توجد عمليات سحب مرفوضة"
                  description="لم يتم رفض أي عمليات سحب"
                />
              ) : (
                <div className="space-y-4">
                  {withdrawals
                    .filter((w) => w.status === "rejected")
                    .map((withdrawal) => (
                      <Card key={withdrawal.id} className="overflow-hidden">
                        <div className="flex items-start p-6 justify-between">
                          <div>
                            <h3 className="font-medium">
                              سحب {formatCurrency(withdrawal.amount)}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {withdrawal.description || "سحب"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDate(withdrawal.created_at)}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <span
                              className="px-2 py-1 text-xs rounded-full font-medium whitespace-nowrap"
                              style={{
                                backgroundColor: getTransactionStatusColor(
                                  withdrawal.status
                                ),
                              }}
                            >
                              {getTransactionStatusText(withdrawal.status)}
                            </span>
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}

export default Withdrawals;
