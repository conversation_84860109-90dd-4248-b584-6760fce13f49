import DashboardLayout from "@/layouts/DashboardLayout";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const formSchema = z.object({
  city: z.string(),
  district: z.string(),
  street: z.string(),
  spaceType: z.enum(["shelves", "refrigerator", "stand"]),
  sizeCategory: z.string(),
  customLength: z.string(),
  customWidth: z.string(),
  productTypes: z.string(),
});

function MySpaces() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      spaceType: "shelves",
      sizeCategory: undefined,
      customLength: undefined,
      customWidth: undefined,
      productTypes: "",
    },
  });

  const sizeCategories = [
    {
      id: "1",
      category: "صغير",
      approximateSize: "10 × 10 سم = 100 سم²",
    },
    {
      id: "2",
      category: "متوسط",
      approximateSize: "15 × 15 سم = 225 سم²",
    },
    {
      id: "3",
      category: "كبير",
      approximateSize: "20 × 20 سم = 400 سم²",
    },
    {
      id: "4",
      category: "اخرى",
      approximateSize: "مساحة مختلفة (ادخال يدوي)",
    },
  ];

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log("Form values:", values);
    setIsDialogOpen(false);
    form.reset();
    // TODO: API call to save the new space
  }

  return (
    <DashboardLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">المساحات الخاصة بي</h1>
        <Button onClick={() => setIsDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          إنشاء مساحة جديدة
        </Button>
      </div>

      {/* List of existing spaces */}
      <div className="grid gap-4">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              لا توجد مساحات حالياً
            </p>
          </CardContent>
        </Card>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>إنشاء مساحة جديدة</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>المدينة</FormLabel>
                      <FormControl>
                        <Input placeholder="المدينة" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="district"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الحي</FormLabel>
                      <FormControl>
                        <Input placeholder="الحي" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="street"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>الشارع</FormLabel>
                      <FormControl>
                        <Input placeholder="الشارع" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">نوع المساحة</h3>
                <FormField
                  control={form.control}
                  name="spaceType"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نوع المساحة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="shelves">رفوف</SelectItem>
                          <SelectItem value="refrigerator">ثلاجة</SelectItem>
                          <SelectItem value="stand">ستاند</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">سعة التخزين التقريبية</h3>
                <FormField
                  control={form.control}
                  name="sizeCategory"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر حجم المنتجات" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {sizeCategories.map((size) => (
                            <SelectItem key={size.id} value={size.id}>
                              {size.category} - {size.approximateSize}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                {form.watch("sizeCategory") === "4" && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormField
                      control={form.control}
                      name="customLength"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>الطول (سم)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="أدخل الطول"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="customWidth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>العرض (سم)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="أدخل العرض"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  نوع المنتجات التي تناسب مساحتي
                </h3>
                <FormField
                  control={form.control}
                  name="productTypes"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={(value) => {
                          const currentValues = field.value || [];
                          if (!currentValues.includes(value)) {
                            field.onChange([...currentValues, value]);
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر أنواع المنتجات المناسبة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="clothing">ملابس</SelectItem>
                          <SelectItem value="electronics">
                            إلكترونيات
                          </SelectItem>
                          <SelectItem value="food">مواد غذائية</SelectItem>
                          <SelectItem value="cosmetics">
                            مستحضرات تجميل
                          </SelectItem>
                          <SelectItem value="accessories">إكسسوارات</SelectItem>
                          <SelectItem value="other">أخرى</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsDialogOpen(false)}
                >
                  إلغاء
                </Button>
                <Button type="submit">إنشاء</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}

export default MySpaces;
