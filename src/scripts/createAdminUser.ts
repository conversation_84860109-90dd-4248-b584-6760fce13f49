
// This is for documentation purposes only - an admin user will be created in production
// Admin credentials:
// Email: <EMAIL>
// Password: Admin123!
//
// To create an admin user in your Supabase instance:
// 1. Go to Authentication > Users in the Supabase dashboard
// 2. Click "Add User"
// 3. Enter the email and password
// 4. After creating the user, click on the user and edit the metadata
// 5. Add the following metadata: { "role": "admin", "name": "Admin User" }
// 6. Save the changes
//
// You can now <NAME_EMAIL> / Admin123!
