
import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export function useVerificationStatus() {
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    // Check for verification tokens in URL
    const verifyEmail = async () => {
      const url = new URL(window.location.href);
      const token = url.searchParams.get('token');
      const type = url.searchParams.get('type');
      const email = url.searchParams.get('email') || '';
      
      // If no token or no type, try to handle as possible OTP verification
      if (!token) {
        // This could be an OTP verification page, so don't treat it as an error
        setIsVerifying(false);
        return;
      }

      try {
        setIsVerifying(true);

        // Different verification types
        switch (type) {
          case 'email_verification':
          case 'email':
            // Use Supabase to verify the email token
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'email',
            });
    
            if (error) {
              console.error('Email verification error:', error);
              setVerificationError(error.message);
              toast({
                title: 'خطأ في التحقق',
                description: error.message || 'حدث خطأ أثناء التحقق من البريد الإلكتروني.',
                variant: 'destructive',
              });
              navigate('/auth/verification-error', { 
                state: { error: error.message } 
              });
              return;
            }
    
            // Verification successful
            toast({
              title: 'تم التحقق بنجاح',
              description: 'تم التحقق من بريدك الإلكتروني بنجاح.',
            });
            navigate('/auth/verification-success');
            break;

          case 'recovery':
          case 'reset':
            // This is a password reset token
            // Just validate the token exists and redirect to reset password page
            if (!token) {
              setVerificationError('توكن إعادة تعيين كلمة المرور غير صالح أو مفقود');
              navigate('/auth/verification-error', { 
                state: { error: 'توكن إعادة تعيين كلمة المرور غير صالح أو مفقود' } 
              });
              return;
            }
            // Pass the token to the reset password page
            navigate(`/auth/reset-password?token=${token}&email=${encodeURIComponent(email)}`);
            break;

          default:
            // Unknown verification type
            setVerificationError('نوع التحقق غير معروف');
            navigate('/auth/verification-error', { 
              state: { error: 'نوع التحقق غير معروف' } 
            });
        }
      } catch (err: any) {
        console.error('Verification processing error:', err);
        setVerificationError(err.message || 'حدث خطأ غير متوقع أثناء معالجة التحقق.');
        navigate('/auth/verification-error', { 
          state: { error: err.message } 
        });
      } finally {
        setIsVerifying(false);
      }
    };

    verifyEmail();
  }, [navigate]);

  return { isVerifying, verificationError };
}
