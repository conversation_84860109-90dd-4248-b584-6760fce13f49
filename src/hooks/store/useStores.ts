import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Store } from "@/types/store";

// Keep the original function to use as the query function
async function fetchStores(filter: Partial<Store>) {
  let query: any = supabase.from("stores").select("*");
  Object.entries(filter).forEach(([key, value]) => {
    if (!value) {
      return;
    }
    query = query.eq(key, value);
  });

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching stores:", error);
    throw error; // Better to throw for React Query to handle
  }

  return data;
}

// Create the hook that uses React Query
export function useStores(filter: Partial<Store> = {}) {
  return useQuery({
    queryKey: ["stores", filter],
    queryFn: () => fetchStores(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
}
