
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { toast } from 'sonner';

export interface SubscriptionOption {
  id: string;
  name: string;
  value: 'percentage' | 'fixed';
  rate: number;
  isEnabled: boolean;
}

export function useStoreSubscriptionOptions() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Fetch subscription options
  const {
    data: subscriptionOptions,
    isLoading,
    error
  } = useQuery({
    queryKey: ['subscriptionOptions'],
    queryFn: async () => {
      const defaultOptions = [
        { id: 'percentage', name: 'عمولة على المبيعات', value: 'percentage' as const, rate: 10, isEnabled: true },
        { id: 'fixed', name: 'اشتراك شهري ثابت', value: 'fixed' as const, rate: 100, isEnabled: true }
      ];
      
      // In a real implementation, fetch these from the database
      // For now, using default values
      return defaultOptions;
    },
    staleTime: 1000 * 60 * 60, // 1 hour
  });

  // Mutation to update a subscription option
  const updateOptionMutation = useMutation({
    mutationFn: async (updates: { optionId: string, changes: Partial<SubscriptionOption> }) => {
      const { optionId, changes } = updates;
      
      // In a real implementation, save to database
      // For now, just return success
      return { success: true };
    },
    onSuccess: () => {
      toast.success('تم تحديث خيار الاشتراك بنجاح');
      queryClient.invalidateQueries({ queryKey: ['subscriptionOptions'] });
    },
    onError: (error) => {
      toast.error('فشل في تحديث خيار الاشتراك');
      console.error('Error updating subscription option:', error);
    }
  });

  // Check for expiring subscriptions
  const checkExpiringSubscriptions = async () => {
    if (!user?.id) return;
    
    try {
      // Get hosting requests with expiring subscriptions
      const { data, error } = await supabase
        .from('hosting_requests')
        .select('id, store_id, store_name, ecommerce_id, ecommerce_name, expires_at, subscription_type')
        .eq(user.role === 'store' ? 'store_id' : 'ecommerce_id', user.id)
        .lte('expires_at', new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()) // 7 days from now
        .gt('expires_at', new Date().toISOString()) // Not expired yet
        .eq('status', 'accepted');
      
      if (error) {
        console.error('Error checking expiring subscriptions:', error);
        return;
      }
      
      if (data && data.length > 0) {
        data.forEach(subscription => {
          const daysToExpiry = Math.ceil((new Date(subscription.expires_at).getTime() - Date.now()) / (24 * 60 * 60 * 1000));
          
          if (daysToExpiry <= 7) {
            toast.info(`اشتراك "${subscription.store_name}" ينتهي خلال ${daysToExpiry} ${daysToExpiry === 1 ? 'يوم' : 'أيام'}`, {
              action: {
                label: 'تجديد',
                onClick: () => {
                  // Navigate to renewal page or open renewal dialog
                  console.log('Renew subscription for:', subscription.id);
                }
              }
            });
          }
        });
      }
    } catch (err) {
      console.error('Error in subscription expiry check:', err);
    }
  };

  // Check expiring subscriptions on component mount
  useEffect(() => {
    if (user) {
      checkExpiringSubscriptions();
    }
  }, [user]);

  return {
    subscriptionOptions,
    isLoading,
    error: error ? (error as Error).message : null,
    updateSubscriptionOption: (optionId: string, changes: Partial<SubscriptionOption>) => 
      updateOptionMutation.mutateAsync({ optionId, changes }),
    checkExpiringSubscriptions
  };
}
