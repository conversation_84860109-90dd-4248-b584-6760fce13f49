
import {useStoreDataQuery} from '../query/useStoreDataQuery';

export const useStoreData = () => {
  const {
    storeData,
    isLoading,
    error,
    submitData: querySubmitData,
    refetch: getStoreData
  } = useStoreDataQuery();

  const submitData = async (data: any, imageFile: File, galleryFiles: File[] = []) => {
    await querySubmitData({
      data,
      imageFile,
      galleryFiles
    });
  };

  return {
    getStoreData,
    storeData,
    submitData,
    isLoading,
  };
};
