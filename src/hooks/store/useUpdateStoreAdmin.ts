import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export function useUpdateStoreAdmin() {
  const { user } = useAuth();
  const updateStore = useMutation({
    mutationFn: async ({
      storeId,
      featured,
      rating,
      subscription,
    }: {
      storeId: string;
      featured: boolean;
      rating: number;
      subscription: string;
    }) => {
      if (!user) throw new Error("User not authenticated");
      const updateData: {
        featured?: boolean;
        rating?: number;
        subscription?: string;
      } = {};
      if (featured) {
        updateData.featured = featured;
      }
      if (rating) {
        updateData.rating = rating;
      }
      if (subscription) {
        updateData.subscription = subscription;
      }

      const { data, error } = await supabase
        .from("stores")
        .update({
          ...updateData,
        })
        .eq("id", storeId)
        .single();

      if (error) {
        toast.error("خطأ في تحديث المتجر");
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      toast.success("تم تحديث المتجر بنجاح", {});
    },
    onError: () => {
      toast.error("خطأ في تحديث المتجر", {});
    },
  });
  return {
    ...updateStore,
  };
}
