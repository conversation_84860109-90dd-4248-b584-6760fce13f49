
import { useState, useEffect } from 'react';
import { Product } from '@/types';
import { useProductFilters } from './product/useProductFilters';
import { useProductFetch } from './product/useProductFetch';
import { useProductActions } from './product/useProductActions';

interface UseProductManagementOptions {
  initialPage?: number;
  pageSize?: number;
  initialSearch?: string;
  initialStoreFilter?: string | 'all';
  autoFetch?: boolean;
}

export function useProductManagement(options: UseProductManagementOptions = {}) {
  const {
    initialPage = 1,
    pageSize = 10,
    initialSearch = '',
    initialStoreFilter = 'all',
    autoFetch = true,
  } = options;

  const [page, setPage] = useState<number>(initialPage);
  const [search, setSearch] = useState<string>(initialSearch);
  const [storeFilter, setStoreFilter] = useState<string | 'all'>(initialStoreFilter);

  // استخدام الهوكس المعاد هيكلتها
  const { stores, fetchStores } = useProductFilters();
  const { products, totalCount, isLoading, error, setProducts, fetchProducts } = useProductFetch();
  const productActions = useProductActions(setProducts);

  // جلب المتاجر عند تحميل الصفحة
  useEffect(() => {
    fetchStores();
  }, []);

  // جلب المنتجات عند تغيير الصفحة أو البحث أو المتجر
  useEffect(() => {
    if (autoFetch) {
      fetchProducts({
        page,
        pageSize,
        search,
        storeFilter
      });
    }
  }, [page, pageSize, search, storeFilter, autoFetch, fetchProducts]);

  return {
    products,
    stores,
    isLoading: isLoading || productActions.isLoading,
    error,
    page,
    totalCount,
    search,
    storeFilter,
    totalPages: Math.ceil(totalCount / pageSize),
    setPage,
    setSearch,
    setStoreFilter,
    refreshProducts: () => fetchProducts({ page, pageSize, search, storeFilter }),
    ...productActions
  };
}
