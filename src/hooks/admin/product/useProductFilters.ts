
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface StoreType {
  id: string;
  name: string;
}

export function useProductFilters() {
  const [stores, setStores] = useState<StoreType[]>([]);

  // جلب قائمة المتاجر
  const fetchStores = async () => {
    try {
      const { data: storesData, error: storesError } = await supabase
        .from('profiles')
        .select('id, name')
        .in('role', ['store', 'ecommerce'])
        .order('name', { ascending: true });

      if (storesError) {
        console.error('Error fetching stores:', storesError);
        return;
      }

      if (storesData) {
        setStores(storesData);
      }
    } catch (err) {
      console.error('Exception fetching stores:', err);
    }
  };

  return {
    stores,
    fetchStores
  };
}
