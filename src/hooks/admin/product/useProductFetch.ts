
import { useState, useCallback } from 'react';
import { Product } from '@/types';
import { supabase } from '@/integrations/supabase/client';

interface UseProductFetchOptions {
  page: number;
  pageSize: number;
  search: string;
  storeFilter: string | 'all';
}

export function useProductFetch() {
  const [products, setProducts] = useState<Product[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = useCallback(async ({ 
    page,
    pageSize,
    search,
    storeFilter 
  }: UseProductFetchOptions) => {
    setIsLoading(true);
    setError(null);
    try {
      // إنشاء الاستعلام الأساسي
      let query = supabase
        .from('products')
        .select('*, profiles(name)', { count: 'exact' });

      // إضافة البحث إذا تم تحديده
      if (search && search.trim() !== '') {
        query = query.ilike('name', `%${search}%`);
      }

      // إضافة تصفية المتجر إذا تم تحديد متجر معين
      if (storeFilter && storeFilter !== 'all') {
        query = query.eq('user_id', storeFilter);
      }

      // إضافة الترتيب والصفحات
      query = query
        .order('created_at', { ascending: false })
        .range((page - 1) * pageSize, page * pageSize - 1);

      const { data, error: queryError, count } = await query;

      if (queryError) {
        throw new Error(queryError.message);
      }

      // معالجة البيانات وتحويلها إلى تنسيق المنتجات
      const processedProducts = data?.map(item => {
        let sellerName = 'غير معروف';
        
        // تحقق من وجود بيانات profiles
        if (item.profiles && typeof item.profiles === 'object' && 'name' in item.profiles) {
          sellerName = item.profiles.name || 'غير معروف';
        }
        
        return {
          ...item,
          store_name: sellerName,
          seller_name: sellerName
        };
      }) || [];

      setProducts(processedProducts as Product[]);
      if (count !== null) {
        setTotalCount(count);
      }
    } catch (err: any) {
      setError(err.message || 'فشل تحميل المنتجات');
      console.error('Error loading products:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    products,
    totalCount,
    isLoading,
    error,
    setProducts,
    fetchProducts
  };
}
