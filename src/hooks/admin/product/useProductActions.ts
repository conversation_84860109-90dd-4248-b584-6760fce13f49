
import { useState } from 'react';
import { Product, ProductStatus } from '@/types';
import { supabase } from '@/integrations/supabase/client';

export function useProductActions(onProductsChange: (updatedProducts: (prevProducts: Product[]) => Product[]) => void) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const updateProduct = async (id: string, productData: Partial<Product>): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('products')
        .update(productData)
        .eq('id', id);

      if (error) {
        console.error('Error updating product:', error);
        return false;
      }

      // تحديث المنتج في الحالة المحلية
      onProductsChange(prevProducts => 
        prevProducts.map(product => 
          product.id === id ? { ...product, ...productData } : product
        )
      );

      return true;
    } catch (error) {
      console.error('Exception updating product:', error);
      return false;
    }
  };

  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting product:', error);
        return false;
      }

      // إزالة المنتج من الحالة المحلية
      onProductsChange(prevProducts => prevProducts.filter(product => product.id !== id));

      return true;
    } catch (error) {
      console.error('Exception deleting product:', error);
      return false;
    }
  };

  const approveProduct = async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ 
          status: 'approved' as ProductStatus,
          active: true,
          approved_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error approving product:', error);
        return false;
      }

      // تحديث حالة المنتج في الحالة المحلية
      onProductsChange(prevProducts => 
        prevProducts.map(product => 
          product.id === id ? { 
            ...product, 
            status: 'approved', 
            active: true 
          } : product
        )
      );

      return true;
    } catch (error) {
      console.error('Exception approving product:', error);
      return false;
    }
  };

  const rejectProduct = async (id: string, reason?: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ 
          status: 'rejected' as ProductStatus, 
          active: false,
          rejection_reason: reason || null,
          rejected_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        console.error('Error rejecting product:', error);
        return false;
      }

      // تحديث حالة المنتج في الحالة المحلية
      onProductsChange(prevProducts => 
        prevProducts.map(product => 
          product.id === id ? { 
            ...product, 
            status: 'rejected', 
            active: false,
            rejection_reason: reason || null
          } : product
        )
      );

      return true;
    } catch (error) {
      console.error('Exception rejecting product:', error);
      return false;
    }
  };

  return {
    isLoading,
    setIsLoading,
    updateProduct,
    deleteProduct,
    approveProduct,
    rejectProduct
  };
}
