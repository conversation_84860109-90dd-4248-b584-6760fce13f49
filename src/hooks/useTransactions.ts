
import { useState } from 'react';
import { Transaction } from '@/types';
import { useTransactionsQuery } from './query/useTransactionsQuery';

export const useTransactions = (userId?: string) => {
  const [localTransactions, setLocalTransactions] = useState<Transaction[]>([]);
  
  const {
    transactions,
    balance,
    isLoading,
    getTransactionById,
    refetch: fetchTransactions
  } = useTransactionsQuery();

  const addTransaction = (transaction: Transaction) => {
    setLocalTransactions(prev => [transaction, ...prev]);
  };
  
  return {
    transactions: [...localTransactions, ...transactions],
    balance,
    isLoading,
    fetchTransactions,
    getTransactionById,
    addTransaction
  };
};
