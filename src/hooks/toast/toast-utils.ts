
import { toast as sonnerToast } from "sonner";
import { dispatch, actionTypes, genId, ToasterToast } from "./toast-store";
import { ToastActionElement } from "@/components/ui/toast";
import { ReactNode } from "react";

// Interface for creating toast notifications
export interface ToastInput {
  title?: ReactNode;
  description?: ReactNode;
  action?: ToastActionElement;
  variant?: "default" | "destructive";
  duration?: number;
  className?: string;
}

// Main toast function
export function toast(props: ToastInput) {
  const id = genId();

  const update = (props: Partial<ToasterToast>) =>
    dispatch({
      type: actionTypes.UPDATE_TOAST,
      toast: { ...props, id },
    });

  const dismiss = () => {
    dispatch({ type: actionTypes.DISMISS_TOAST, toastId: id });
    // Also dismiss from sonner
    sonnerToast.dismiss(id);
  };

  // Create a toast object for dispatch
  const toastToDispatch: ToasterToast = {
    id,
    open: true,
    onOpenChange: (open) => {
      if (!open) dismiss();
    },
    ...props,
  };

  dispatch({
    type: actionTypes.ADD_TOAST,
    toast: toastToDispatch,
  });

  // For sonner toast integration
  let sonnerInstance;
  
  if (props.variant === "destructive") {
    sonnerInstance = sonnerToast.error(props.title ? String(props.title) : "", {
      id,
      description: props.description ? String(props.description) : undefined,
      duration: props.duration,
    });
  } else {
    sonnerInstance = sonnerToast(props.title ? String(props.title) : "", {
      id,
      description: props.description ? String(props.description) : undefined,
      duration: props.duration,
    });
  }

  return {
    id,
    dismiss,
    update,
  };
}

// Convenience methods
toast.success = (title: string, description?: string) => {
  return toast({
    title,
    description,
    variant: "default",
  });
};

toast.error = (title: string, description?: string) => {
  return toast({
    title,
    description,
    variant: "destructive",
  });
};

toast.loading = (title: string, description?: string) => {
  const toastInstance = toast({
    title,
    description,
    variant: "default",
    duration: Infinity, // Loading toasts should stay until dismissed
  });
  
  sonnerToast.loading(title, {
    id: toastInstance.id,
    description: description,
  });
  
  return toastInstance;
};
