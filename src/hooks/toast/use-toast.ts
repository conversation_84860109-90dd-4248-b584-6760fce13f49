
import { useToastStore, dispatch, actionTypes } from "./toast-store";
import { toast } from "./toast-utils";
import type { ToastActionElement, ToastProps } from "@/components/ui/toast";

export function useToast() {
  const state = useToastStore();
  
  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: actionTypes.DISMISS_TOAST, toastId }),
  };
}

export { toast };
export type { ToastProps, ToastActionElement };
export type { ToastInput } from "./toast-utils";
