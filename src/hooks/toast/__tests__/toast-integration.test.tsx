
import React from 'react';
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '../use-toast';
import { toast } from '../toast-utils';

// Dummy custom action component for testing
const CustomAction = () => <button>Action</button>;

// Test component that uses the toast hook
const TestComponent = ({ 
  showToast = false, 
  variant = 'default' as const,
  title = 'Test Toast',
  description = 'Toast Description'
}) => {
  const { toast } = useToast();
  
  React.useEffect(() => {
    if (showToast) {
      toast({
        title,
        description,
        variant,
      });
    }
  }, [showToast, toast, title, description, variant]);
  
  return <div>Test Component</div>;
};

// Wrapper for isolating toast tests
const ToasterWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      {children}
      <Toaster />
    </>
  );
};

describe('Toast Integration', () => {
  it('should render the Toaster component', () => {
    render(<Toaster />);
    // Use query selector to check if element exists
    expect(document.querySelector('[role="region"]')).toBeTruthy();
  });

  it('should expose toast utilities for direct import', () => {
    expect(typeof toast).toBe('function');
    expect(typeof toast.success).toBe('function');
    expect(typeof toast.error).toBe('function');
    expect(typeof toast.loading).toBe('function');
  });

  it('should allow creating toasts through the hook', () => {
    const { result } = renderHook(() => useToast());
    expect(result.current.toast).toBeDefined();
    expect(typeof result.current.toast).toBe('function');
  });

  it('should allow toasts to be created with custom properties', () => {
    const customToast = toast({
      title: 'Custom',
      description: 'This is custom',
      variant: 'destructive',
      action: <CustomAction />,
      duration: 5000,
    });
    
    expect(customToast.id).toBeTruthy();
    expect(typeof customToast.dismiss).toBe('function');
  });

  it('should render the test component with toast', () => {
    render(<TestComponent showToast={true} />, { wrapper: ToasterWrapper });
    
    // In a real browser, we'd expect to see the toast
    // In the test environment, we can check that the component renders
    expect(screen.getByText('Test Component')).toBeDefined();
  });
});

// Import renderHook from testing library for the hook test
import { renderHook } from '@testing-library/react';
