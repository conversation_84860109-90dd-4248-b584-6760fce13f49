
import { describe, it, expect, afterEach } from 'vitest';
import { 
  useToastStore, 
  dispatch, 
  actionTypes, 
  memoryState, 
  toastTimeouts,
  TOAST_LIMIT,
  reducer
} from '../toast-store';

// Helper to create mock toast
const createMockToast = (id: string) => ({
  id,
  title: `Test Toast ${id}`,
  description: `Test Description ${id}`,
  open: true,
  variant: 'default' as const,
});

describe('Toast Store', () => {
  afterEach(() => {
    // Clean up toasts between tests
    dispatch({ type: actionTypes.REMOVE_TOAST });
    toastTimeouts.forEach((timeout) => clearTimeout(timeout));
    toastTimeouts.clear();
  });

  it('should add a toast', () => {
    const initialCount = memoryState.toasts.length;
    const toast = createMockToast('test-1');
    
    dispatch({ 
      type: actionTypes.ADD_TOAST, 
      toast 
    });
    
    expect(memoryState.toasts.length).toBe(initialCount + 1);
    expect(memoryState.toasts[0]).toEqual(toast);
  });

  it('should respect the toast limit', () => {
    // Clear existing toasts first
    dispatch({ type: actionTypes.REMOVE_TOAST });
    
    // Add more toasts than the limit
    for (let i = 0; i < TOAST_LIMIT + 5; i++) {
      dispatch({ 
        type: actionTypes.ADD_TOAST, 
        toast: createMockToast(`test-${i}`) 
      });
    }
    
    expect(memoryState.toasts.length).toBe(TOAST_LIMIT);
  });

  it('should update an existing toast', () => {
    const toast = createMockToast('test-update');
    dispatch({ type: actionTypes.ADD_TOAST, toast });
    
    const updatedTitle = 'Updated Toast Title';
    dispatch({ 
      type: actionTypes.UPDATE_TOAST, 
      toast: { 
        id: 'test-update',
        title: updatedTitle 
      } 
    });
    
    const updatedToast = memoryState.toasts.find(t => t.id === 'test-update');
    expect(updatedToast?.title).toBe(updatedTitle);
  });

  it('should dismiss a toast by setting open to false', () => {
    const toast = createMockToast('test-dismiss');
    dispatch({ type: actionTypes.ADD_TOAST, toast });
    
    dispatch({ type: actionTypes.DISMISS_TOAST, toastId: 'test-dismiss' });
    
    const dismissedToast = memoryState.toasts.find(t => t.id === 'test-dismiss');
    expect(dismissedToast?.open).toBe(false);
  });

  it('should remove a specific toast', () => {
    // Add multiple toasts
    const toast1 = createMockToast('test-remove-1');
    const toast2 = createMockToast('test-remove-2');
    
    dispatch({ type: actionTypes.ADD_TOAST, toast: toast1 });
    dispatch({ type: actionTypes.ADD_TOAST, toast: toast2 });
    
    // Remove just one toast
    dispatch({ type: actionTypes.REMOVE_TOAST, toastId: 'test-remove-1' });
    
    // Assert test-remove-1 is gone but test-remove-2 remains
    expect(memoryState.toasts.find(t => t.id === 'test-remove-1')).toBeUndefined();
    expect(memoryState.toasts.find(t => t.id === 'test-remove-2')).toBeDefined();
  });

  it('should remove all toasts when no toast ID is provided', () => {
    // Add multiple toasts
    dispatch({ type: actionTypes.ADD_TOAST, toast: createMockToast('test-1') });
    dispatch({ type: actionTypes.ADD_TOAST, toast: createMockToast('test-2') });
    
    // Remove all toasts
    dispatch({ type: actionTypes.REMOVE_TOAST });
    
    expect(memoryState.toasts.length).toBe(0);
  });

  it('should correctly apply reducer without modifying state directly', () => {
    const initialState = { toasts: [] };
    const toast = createMockToast('test-reducer');
    
    // Test ADD_TOAST
    const stateAfterAdd = reducer(initialState, { 
      type: actionTypes.ADD_TOAST, 
      toast 
    });
    
    expect(stateAfterAdd.toasts.length).toBe(1);
    expect(initialState.toasts.length).toBe(0); // Original state unchanged
    
    // Test DISMISS_TOAST
    const stateAfterDismiss = reducer(stateAfterAdd, { 
      type: actionTypes.DISMISS_TOAST, 
      toastId: 'test-reducer' 
    });
    
    expect(stateAfterDismiss.toasts[0].open).toBe(false);
    expect(stateAfterAdd.toasts[0].open).toBe(true); // Previous state unchanged
  });
});
