
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { toast } from '../toast-utils';
import * as storeModule from '../toast-store';
import * as sonnerModule from 'sonner';

// Define proper types for the mocked toast function
interface MockToastFn {
  (title: string, options?: any): string | number;
  error: (title: string, options?: any) => string | number;
  success: (title: string, options?: any) => string | number;
  loading: (title: string, options?: any) => string | number;
  promise: (promise: Promise<any>, options?: any) => string | number;
}

// Mock dependencies
vi.mock('../toast-store', () => ({
  dispatch: vi.fn(),
  actionTypes: {
    ADD_TOAST: 'add_toast',
    UPDATE_TOAST: 'update_toast',
    DISMISS_TOAST: 'dismiss_toast',
  },
  genId: vi.fn(() => 'test-id'),
}));

// Create a properly typed mock for sonner toast
const mockToast = vi.fn() as unknown as MockToastFn;
mockToast.error = vi.fn();
mockToast.success = vi.fn();
mockToast.loading = vi.fn();
mockToast.promise = vi.fn();

vi.mock('sonner', () => ({
  toast: mockToast
}));

describe('Toast Utils', () => {
  const mockDispatch = storeModule.dispatch as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a toast', () => {
    const props = { title: 'Test Toast', description: 'Test Description' };
    const result = toast(props);

    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.ADD_TOAST,
      toast: expect.objectContaining({
        id: 'test-id',
        open: true,
        ...props,
      }),
    });

    expect(result).toEqual({
      id: 'test-id',
      dismiss: expect.any(Function),
      update: expect.any(Function),
    });
  });

  it('should update a toast', () => {
    const props = { title: 'Test Toast' };
    const result = toast(props);
    const updateProps = { title: 'Updated Toast' };

    result.update(updateProps);

    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.UPDATE_TOAST,
      toast: expect.objectContaining({
        id: 'test-id',
        ...updateProps,
      }),
    });
  });

  it('should dismiss a toast', () => {
    const props = { title: 'Test Toast' };
    const result = toast(props);

    result.dismiss();

    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.DISMISS_TOAST,
      toastId: 'test-id',
    });
  });

  it('should call sonner toast with correct parameters for default toast', () => {
    const props = { title: 'Test Toast', description: 'Test Description' };
    toast(props);

    expect(mockToast).toHaveBeenCalledWith(props.title, {
      description: props.description,
    });
  });

  it('should call sonner toast.error for destructive variant', () => {
    const props = { 
      title: 'Test Toast', 
      description: 'Test Description',
      variant: 'destructive' as const
    };

    toast(props);

    expect(mockToast.error).toHaveBeenCalledWith(props.title, {
      description: props.description,
    });
  });

  it('should have convenience methods', () => {
    toast.success('Success', 'It worked!');
    toast.error('Error', 'Something went wrong!');
    
    expect(mockDispatch).toHaveBeenCalledTimes(2);
  });
  
  it('should have a loading method that shows a persistent toast', () => {
    const result = toast.loading('Loading', 'Please wait...');
    
    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.ADD_TOAST,
      toast: expect.objectContaining({
        id: 'test-id',
        open: true,
        title: 'Loading',
        description: 'Please wait...',
        duration: Infinity,
      }),
    });
    
    expect(mockToast.loading).toHaveBeenCalledWith('Loading', {
      description: 'Please wait...',
    });
    
    expect(result).toEqual({
      id: 'test-id',
      dismiss: expect.any(Function),
      update: expect.any(Function),
    });
  });
});
