
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useToast } from '../use-toast';
import * as storeModule from '../toast-store';
import { toast as toastUtil } from '../toast-utils';

// Mock the store module
vi.mock('../toast-store', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual as object,
    useToastStore: vi.fn(),
    dispatch: vi.fn()
  };
});

// Mock toast-utils
vi.mock('../toast-utils', () => ({
  toast: vi.fn().mockImplementation(() => ({
    id: 'mock-toast-id',
    dismiss: vi.fn(),
    update: vi.fn(),
  }))
}));

describe('useToast Hook', () => {
  const mockToastStore = storeModule.useToastStore as ReturnType<typeof vi.fn>;
  const mockDispatch = storeModule.dispatch as ReturnType<typeof vi.fn>;
  const mockState = { toasts: [{ id: 'test-toast', title: 'Test Toast' }] };

  beforeEach(() => {
    vi.clearAllMocks();
    mockToastStore.mockReturnValue(mockState);
  });

  it('should return toast store state', () => {
    const { result } = renderHook(() => useToast());
    
    expect(result.current.toasts).toEqual(mockState.toasts);
  });

  it('should expose toast function', () => {
    const { result } = renderHook(() => useToast());
    
    expect(result.current.toast).toBe(toastUtil);
  });

  it('should provide dismiss function that dispatches DISMISS_TOAST', () => {
    const { result } = renderHook(() => useToast());
    
    act(() => {
      result.current.dismiss('test-toast-id');
    });
    
    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.DISMISS_TOAST,
      toastId: 'test-toast-id'
    });
  });

  it('should dismiss without id to dismiss all toasts', () => {
    const { result } = renderHook(() => useToast());
    
    act(() => {
      result.current.dismiss();
    });
    
    expect(mockDispatch).toHaveBeenCalledWith({
      type: storeModule.actionTypes.DISMISS_TOAST,
      toastId: undefined
    });
  });
});
