import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import { uploadImage } from "@/utils/supabaseActions";

interface StoreDataSubmitParams {
  data: any;
  imageFile?: File;
  galleryFiles?: File[];
}

export function useStoreDataQuery() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: storeData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["storeData", user?.id],
    queryFn: async () => {
      if (!user) return null;

      const { data, error } = await supabase
        .from("stores")
        .select("*")
        .eq("owner_id", user.id)
        .single();

      if (error) {
        if (error.code !== "PGRST116") {
          // Not found error
          throw error;
        }
        return null;
      }

      return data;
    },
    enabled: !!user,
  });

  const submitDataMutation = useMutation({
    mutationFn: async ({
      data,
      imageFile,
      galleryFiles = [],
    }: StoreDataSubmitParams) => {
      if (!user) throw new Error("User not authenticated");
      const toastId = toast.loading("جاري حفظ بيانات المتجر...");

      data.gallery = data.gallery?.filter(
        (item: string) => !galleryFiles.find((file) => file.name === item)
      );

      let imageUrl = data.image || "";
      if (imageFile) {
        imageUrl = await uploadImage({
          storageName: "stores",
          file: imageFile,
          path: `store-${user.id}`,
        });
      }

      const galleryUrls: string[] = [];
      if (galleryFiles && galleryFiles.length > 0) {
        for (const file of galleryFiles) {
          const url = await uploadImage({
            file,
            storageName: "stores",
            path: `store-${user.id}/gallery`,
          });
          galleryUrls.push(url);
        }
      }

      // Convert data keys to match database fields
      const storeData = {
        owner_id: user.id,
        name: data.name || "",
        description: data.description || "",
        address: data.address || "",
        city: data.city || "",
        phone: data.phone || "",
        email: data.email || "",
        hours: data.hours || "",
        legal_activity: data.legalActivity || data.legal_activity || "",
        shelf_space: data.shelfSpace || data.shelf_space || "",
        capacity: data.capacity || 0,
        // subscription_type: data.subscription_type || "percentage",
        image: imageUrl,
        gallery: [...data.gallery, ...galleryUrls],
      };

      const { error } = await supabase.from("stores").upsert(storeData, {
        onConflict: "owner_id",
      });

      if (error) {
        console.error("Error saving store data:", error);
        toast.error("فشل في حفظ بيانات المتجر", {
          id: toastId,
        });
        throw error;
      }

      toast.success("تم حفظ بيانات المتجر بنجاح", {
        id: toastId,
      });

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["storeData"] });
    },
    onError: (error) => {
      console.error("Error submitting store data:", error);
      toast.error("فشل في حفظ بيانات المتجر");
    },
  });

  return {
    storeData,
    isLoading,
    error: error ? (error as Error).message : null,
    submitData: submitDataMutation.mutateAsync,
    refetch,
  };
}
