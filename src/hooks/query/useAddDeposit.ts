
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { TransactionType, TransactionStatus } from "@/types";
import { uploadImage } from "@/utils/supabaseActions";
import { TRANSACTIONS_KEY } from "./useWallet";

export function useAddDeposit(userId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const depositMutation = useMutation({
    mutationFn: async ({
      amount,
      receiptUrl,
      notes,
      reference,
    }: {
      amount: number;
      receiptUrl?: string;
      notes?: string;
      reference?: string;
    }) => {
      if (!user) throw new Error("User not authenticated");

      const newTransaction = {
        user_id: user.id,
        user_name: user.name || user.email || "Anonymous",
        amount,
        type: "deposit" as TransactionType,
        status: "pending" as TransactionStatus,
        description: notes || "طلب إيداع",
        receipt_url: receiptUrl || "",
        reference: `DEP-${reference || Math.floor(Math.random() * 1000000)}`,
      };

      const { error } = await supabase.from("transactions").insert(newTransaction);
      if (error) throw error;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TRANSACTIONS_KEY, user?.id] });
      toast.success("تم إنشاء طلب الإيداع بنجاح وسيتم مراجعته");
    },
    onError: () => {
      toast.error("حدث خطأ أثناء إنشاء معاملة الإيداع");
    },
  });

  const addDeposit = async (
    amount: number,
    receipt?: File | null,
    notes?: string,
    reference?: string
  ): Promise<boolean> => {
    try {
      if (!user) {
        toast.error("يجب تسجيل الدخول أولاً");
        return false;
      }
      let receiptUrl: string = "";
      if (receipt) {
        receiptUrl = await uploadImage({
          file: receipt,
          storageName: "receipts",
          path: `receipt-${user.id}`,
        });
      }
      await depositMutation.mutateAsync({
        amount,
        notes,
        reference,
        receiptUrl,
      });
      return true;
    } catch {
      return false;
    }
  };

  return { addDeposit, isAddingDeposit: depositMutation.isPending };
}
