
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Transaction } from '@/types';
import { useAuth } from '@/contexts/auth';

export function useTransactionsQuery() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Query for fetching transactions and balance
  const {
    data: transactionData = { transactions: [], balance: 0 },
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['transactions', user?.id],
    queryFn: async () => {
      if (!user) return { transactions: [], balance: 0 };
      
      console.log("Fetching transactions for user:", user.id);
      
      // Get user balance (with improved error handling)
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('balance')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        console.error("Error fetching user balance:", profileError);
        if (profileError.code !== 'PGRST116') {
          throw profileError;
        }
      }
      
      const balance = profileData?.balance || 0;
      console.log("User balance:", balance);
      
      // Get transactions with improved error handling
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error("Error fetching transactions:", error);
        throw error;
      }
      
      console.log(`Fetched ${data?.length || 0} transactions`);
      
      return {
        transactions: data as Transaction[],
        balance
      };
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
  });
  
  // Function to get transaction by ID
  const getTransactionById = (id: string): Transaction | undefined => {
    return transactionData.transactions.find(tx => tx.id === id);
  };

  // Mutation to update transaction status - for admin purposes
  const updateTransactionStatus = useMutation({
    mutationFn: async (params: { id: string, status: string }) => {
      const { id, status } = params;
      const { error } = await supabase
        .from('transactions')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      return true;
    },
    onSuccess: () => {
      // Invalidate the transactions query to refetch the updated data
      queryClient.invalidateQueries({ queryKey: ['transactions', user?.id] });
    }
  });

  return {
    transactions: transactionData.transactions,
    balance: transactionData.balance,
    isLoading,
    error: error ? (error as Error).message : null,
    getTransactionById,
    updateStatus: updateTransactionStatus.mutateAsync,
    refetch
  };
}
