
import { useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";

export function useUserBalance() {
  const { user } = useAuth();
  const userId = useMemo(() => user?.id, [user?.id]);

  const fetchUserBalance = useCallback(async () => {
    if (!userId) throw new Error("User not authenticated");
    const { data, error } = await supabase
      .from("profiles")
      .select("balance")
      .eq("id", userId)
      .maybeSingle();
    if (error) throw error;
    return Number(data?.balance) || 0;
  }, [userId]);

  const { data: balance = 0, isLoading } = useQuery({
    queryKey: ["userBalance", userId],
    queryFn: fetchUserBalance,
    enabled: !!userId,
    staleTime: 60 * 1000,
    refetchOnWindowFocus: false,
  });

  return { balance, isBalanceLoading: isLoading, userId };
}
