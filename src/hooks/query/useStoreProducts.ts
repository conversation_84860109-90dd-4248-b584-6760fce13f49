
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { toast } from 'sonner';

export function useStoreProducts() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const {
    data: products,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['storeProducts', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      console.log("Fetching store products for user:", user.id);
      
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('seller_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching store products:', error);
        throw error;
      }
      
      console.log(`Fetched ${data?.length || 0} products`);
      return data || [];
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Add product mutation
  const addProductMutation = useMutation({
    mutationFn: async (productData: any) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('products')
        .insert({
          ...productData,
          seller_id: user.id,
          seller_name: user.name || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
      
      if (error) {
        console.error('Error adding product:', error);
        throw error;
      }
      
      return data[0];
    },
    onSuccess: () => {
      toast.success('تمت إضافة المنتج بنجاح');
      queryClient.invalidateQueries({ queryKey: ['storeProducts'] });
    },
    onError: (error: any) => {
      toast.error('فشل في إضافة المنتج');
      console.error('Error adding product:', error);
    }
  });

  // Update product mutation
  const updateProductMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: any }) => {
      if (!user) throw new Error('User not authenticated');
      
      const { data, error } = await supabase
        .from('products')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('seller_id', user.id)
        .select();
      
      if (error) {
        console.error('Error updating product:', error);
        throw error;
      }
      
      return data[0];
    },
    onSuccess: () => {
      toast.success('تم تحديث المنتج بنجاح');
      queryClient.invalidateQueries({ queryKey: ['storeProducts'] });
    },
    onError: (error: any) => {
      toast.error('فشل في تحديث المنتج');
      console.error('Error updating product:', error);
    }
  });

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user) throw new Error('User not authenticated');
      
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id)
        .eq('seller_id', user.id);
      
      if (error) {
        console.error('Error deleting product:', error);
        throw error;
      }
      
      return { success: true };
    },
    onSuccess: () => {
      toast.success('تم حذف المنتج بنجاح');
      queryClient.invalidateQueries({ queryKey: ['storeProducts'] });
    },
    onError: (error: any) => {
      toast.error('فشل في حذف المنتج');
      console.error('Error deleting product:', error);
    }
  });
  
  return {
    products,
    isLoading,
    error: error ? (error as Error).message : null,
    refetch,
    addProduct: addProductMutation.mutateAsync,
    updateProduct: updateProductMutation.mutateAsync,
    deleteProduct: deleteProductMutation.mutateAsync
  };
}
