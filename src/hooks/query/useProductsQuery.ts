import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { uploadImage } from "@/utils/supabaseActions";

export function useProductsQuery() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Query for fetching all products
  const {
    data: products = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["products", user?.id],
    queryFn: async () => {
      let query = supabase.from("products").select("*");

      // For non-admin users, only show their products
      if (user && user.role !== "admin") {
        query = query.eq("user_id", user.id);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as Product[];
    },
    enabled: !!user,
  });

  // Mutation for adding a product
  const addProductMutation = useMutation({
    mutationFn: async (
      productData: Omit<Product, "id" | "created_at" | "updated_at"> & {
        imageFiles?: File[];
      }
    ) => {
      if (!user) throw new Error("User not authenticated");

      // Extract image files from productData
      const imageFiles = productData.imageFiles || [];
      delete productData.imageFiles;

      // Upload images to storage
      const images: string[] = [];
      if (imageFiles.length > 0) {
        for (const file of imageFiles) {
          const path = await uploadImage({
            storageName: "products",
            file: file,
            path: `product-${user.id}`,
          });
          if (path) images.push(path);
        }
      }

      // Create new product with uploaded image URLs
      const newProduct = {
        ...productData,
        images,
        user_id: user.id,
        seller_id: user.id,
        seller_name: user.name || "",
      };

      const { data, error } = await supabase
        .from("products")
        .insert(newProduct)
        .select()
        .single();

      if (error) throw error;
      return data as Product;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product added successfully");
    },
    onError: (error) => {
      console.error("Error adding product:", error);
      toast.error("Failed to add product");
    },
  });

  // Mutation for updating a product
  const updateProductMutation = useMutation({
    mutationFn: async ({
      id,
      data,
      imageFiles = [],
    }: {
      id: string;
      data: Partial<Product>;
      imageFiles?: File[];
    }) => {
      if (!user) throw new Error("User not authenticated");
      data.images = data.images.filter((img) => img.startsWith("http"));
      console.log(data, imageFiles);

      // Handle image uploads if new images are provided
      if (imageFiles.length > 0) {
        const uploadedImageUrls: string[] = [];

        for (const file of imageFiles) {
          const path = await uploadImage({
            storageName: "products",
            file: file,
            path: `product-${user.id}`,
          });
          if (path) uploadedImageUrls.push(path);
        }

        // If we have existing images, combine them with new ones
        const existingImages = data.images || [];
        data.images = [...existingImages, ...uploadedImageUrls];
      }

      const { data: updatedProduct, error } = await supabase
        .from("products")
        .update(data)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return updatedProduct as Product;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product updated successfully");
    },
    onError: (error) => {
      console.error("Error updating product:", error);
      toast.error("Failed to update product");
    },
  });

  // Mutation for deleting a product
  const deleteProductMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from("products").delete().eq("id", id);

      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product deleted successfully");
    },
    onError: (error) => {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    },
  });

  // Function to get a product by ID
  const getProductById = async (id: string): Promise<Product | null> => {
    try {
      const { data, error } = await supabase
        .from("products")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data as Product;
    } catch (error) {
      console.error("Error fetching product:", error);
      return null;
    }
  };

  return {
    products,
    isLoading,
    error: error ? (error as Error).message : null,
    addProduct: addProductMutation.mutateAsync,
    updateProduct: updateProductMutation.mutateAsync,
    deleteProduct: deleteProductMutation.mutateAsync,
    getProductById,
    refetch,
  };
}
