
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { TransactionType, TransactionStatus } from "@/types";
import { TRANSACTIONS_KEY } from "./useWallet";

export function useAddWithdrawal(userBalance: number) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const withdrawalMutation = useMutation({
    mutationFn: async ({
      amount,
      notes,
      reference,
    }: {
      amount: number;
      notes?: string;
      reference?: string;
    }) => {
      if (!user) throw new Error("User not authenticated");
      if (userBalance < amount) throw new Error("Insufficient balance");

      const newTransaction = {
        user_id: user.id,
        user_name: user.name || user.email || "Anonymous",
        amount,
        type: "withdraw" as TransactionType,
        status: "pending" as TransactionStatus,
        description: notes || "طلب سحب",
        reference: `WTH-${reference || Math.floor(Math.random() * 1000000)}`,
      };

      const { error } = await supabase.from("transactions").insert(newTransaction);
      if (error) throw error;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TRANSACTIONS_KEY, user?.id] });
      toast.success("تم إنشاء طلب السحب بنجاح وسيتم مراجعته");
    },
    onError: (error: any) => {
      if (error?.message === "Insufficient balance") {
        toast.error("رصيد غير كافٍ");
      } else {
        toast.error("حدث خطأ أثناء إنشاء معاملة السحب");
      }
    },
  });

  const addWithdrawal = async (
    amount: number,
    notes?: string,
    reference?: string
  ): Promise<boolean> => {
    try {
      if (!user) {
        toast.error("يجب تسجيل الدخول أولاً");
        return false;
      }
      await withdrawalMutation.mutateAsync({
        amount,
        notes,
        reference,
      });
      return true;
    } catch {
      return false;
    }
  };

  return { addWithdrawal, isAddingWithdrawal: withdrawalMutation.isPending };
}
