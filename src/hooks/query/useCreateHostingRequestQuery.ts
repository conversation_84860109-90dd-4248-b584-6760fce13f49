import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { HostingProduct, HostingRequest } from "@/types";
import { Store } from "@/types/store";
import { toast } from "sonner";

interface CreateHostingRequestParams {
  request: Partial<HostingRequest>;
  products: HostingProduct[];
}

export function useCreateHostingRequestQuery() {
  const queryClient = useQueryClient();

  const { mutateAsync: createHostingRequest, isPending: isSubmitting } =
    useMutation({
      mutationFn: async ({ request, products }: CreateHostingRequestParams) => {
        const { data, error } = await supabase.rpc("create_hosting_request", {
          request_data: {
            ecommerce_id: request.ecommerce_id,
            ecommerce_name: request.ecommerce_name,
            store_id: request.store_id,
            store_name: request.store_name,
            status: "pending",
            notes: request.notes,
            ecommerce_legal_activity: request.ecommerce_legal_activity,
            subscription_type: request.subscription_type,
          },
          products_data: products.map((product) => ({
            product_id: product.product_id,
            product_name: product.product_name,
            price: product.price,
            quantity: product.quantity,
            image: product.image,
          })),
        });

        console.log(data);

        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["hostingRequests"] });
        toast.success("Hosting request created successfully");
      },
      onError: (error) => {
        console.error("Error creating hosting request:", error);
        toast.error("Failed to create hosting request");
      },
    });

  return {
    createHostingRequest,
    isSubmitting,
  };
}
