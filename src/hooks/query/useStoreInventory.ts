import {useAuth} from "@/contexts/auth";
import {supabase} from "@/integrations/supabase/client";
import {HostingProduct} from "@/types";
import {useMutation, useQuery, useQueryClient} from "@tanstack/react-query";
import {toast} from "sonner";
import {useStoreData} from "@/hooks/store/useStoreData.ts";
import {Store} from "@/types/store.ts";

export function useStoreInventory() {
  const { user } = useAuth();
  const {storeData} = useStoreData()
  const queryClient = useQueryClient();

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["storeInventory", user?.id],
    queryFn: async () => {
      if (!user) return [];
      const x: Store = await queryClient.getQueryData([
        "storeData",
        user?.id,
      ]);
      const id = x?.id || storeData?.id;
      if (!id) return [];

      // استعلام من جدول الطلبات أولاً
      const { data: approvedRequests, error: requestsError } = await supabase
        .from("hosting_requests")
        .select("id , ecommerce_name")
        .eq("store_id", id).eq("status", "on_sale");

      console.log("Approved requests data:", approvedRequests);

      if (requestsError) throw requestsError;
      if (!approvedRequests || approvedRequests.length === 0) return [];

      // ثم استعلام المنتجات المرتبطة
      const { data: hostedProducts, error: productsError } = await supabase
        .from("hosting_products")
        .select(
          `
            id,
            product_id,
            product_name,
            price,
            quantity,
            image,
            hosting_request_id
          `
        )
        .in(
          "hosting_request_id",
          approvedRequests.map((r) => r.id)
        );

      if (productsError) throw productsError;
      console.log("Hosted products data:", hostedProducts);

      if (!hostedProducts || hostedProducts.length === 0) return [];
      const newHostedProducts = hostedProducts.map((item) => ({
        ...item,
        seller_name:
          approvedRequests.find((i) => i.id === item.hosting_request_id)
            ?.ecommerce_name || "",
      }));

      return newHostedProducts;
    },
    enabled: !!user,
  });

  const { mutate: updateProduct, isPending: isMutating } = useMutation({
    mutationKey: ["updateProduct"],
    onSuccess: (data) => {
      queryClient.setQueryData(["storeInventory", user?.id], (oldData: any) => {
        return oldData.map((product: HostingProduct) =>
          product.id === data.id
            ? { ...product, quantity: data.quantity }
            : product
        );
      });
      toast.success("تم تحديث المنتج بنجاح");
    },
    onError: (error) => {
      console.error("Error updating product:", error);
      toast.error("فشل في تحديث المنتج");
    },
    mutationFn: async ({
      productId,
      newQuantity,
    }: {
      productId: string;
      newQuantity: number;
    }) => {
      const { data, error } = await supabase
        .from("hosting_products")
        .update({ quantity: newQuantity })
        .eq("id", productId)
        .select()
        .single();

      if (error) throw error;
      return data as HostingProduct;
    },
  });

  return {
    products: data || [],
    isLoading,
    error,
    isMutating,
    updateProduct: (productId: string, newQuantity: number) =>
      updateProduct({ productId, newQuantity }),

    refetch,
  };
}

/* 
  const loadInventory = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // First check if the hosting_products table exists and has data
      console.log("Fetching hosted products for store ID:", user.id);

      // Loading hosted products in the physical store
      const { data: hostedProducts, error: hostedError } = await supabase
        .from("hosting_products")
        .select(
          `
              id,
              product_id,
              product_name,
              price,
              quantity,
              image,
              hosting_request_id,
              hosting_requests(
                id,
                ecommerce_id,
                ecommerce_name,
                status
              )
            `
        )
        .eq("hosting_requests.store_id", user.id)
        .eq("hosting_requests.status", "approved");

      if (hostedError) {
        console.error("Error fetching hosted products:", hostedError);
        throw hostedError;
      }

      console.log("Hosted products data:", hostedProducts);

      if (hostedProducts && hostedProducts.length > 0) {
        // Transform the data to match Product type
        const formattedProducts: Product[] = hostedProducts.map((item) => ({
          id: item.product_id,
          name: item.product_name,
          price: item.price,
          description: "",
          quantity: item.quantity,
          images: item.image ? [item.image] : [],
          status: "active",
          category: "",
          seller_id: (item.hosting_requests as any)?.ecommerce_id || "",
          seller_name: (item.hosting_requests as any)?.ecommerce_name || "",
          user_id: user.id,
          created_at: "",
          updated_at: "",
        }));

        console.log("Formatted products:", formattedProducts);
        setProducts(formattedProducts);
        setFilteredProducts(formattedProducts);
      } else {
        // If no hosting products, check for regular products owned by the store
        console.log("No hosted products found, checking regular products");
        const { data: storeProducts, error: storeError } = await supabase
          .from("products")
          .select("*")
          .eq("user_id", user.id);

        if (storeError) {
          console.error("Error fetching store products:", storeError);
          throw storeError;
        }

        console.log("Store products data:", storeProducts);
        if (storeProducts && storeProducts.length > 0) {
          setProducts(storeProducts);
          setFilteredProducts(storeProducts);
        } else {
          // No products found
          setProducts([]);
          setFilteredProducts([]);
        }
      }
    } catch (err: any) {
      console.error("Error loading inventory:", err);
      setError(err.message || "حدث خطأ أثناء تحميل المخزون");
      toast.error("فشل تحميل بيانات المخزون");
    } finally {
      setIsLoading(false);
    }
  }; */
