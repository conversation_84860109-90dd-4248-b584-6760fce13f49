import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { HostingRequest, OrderStatus } from "@/types";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import axios from "axios";
import { useParams } from "react-router-dom";

export function useHostingRequestsQuery() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { id } = useParams();
  // Query for fetching hosting requests
  const {
    data: hostingRequests = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["hostingRequests", user?.id],
    queryFn: async () => {
      let query = supabase
        .from("hosting_requests")
        .select(`*`)
        .order("created_at", {
          ascending: false,
        })
        .order("updated_at", {
          ascending: false,
        });

      // Filter by user role
      if (user?.role === "ecommerce") {
        query = query.eq("ecommerce_id", user.id);
      } else if (user?.role === "store") {
        const { id }: { id: string } = await queryClient.getQueryData([
          "storeData",
          user?.id,
        ]);
        query = query.eq("store_id", id);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as HostingRequest[];
    },
    enabled: !!user,
  });

  // Mutation for updating hosting request status
  const updateStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: OrderStatus }) => {
      console.log("Updating status for request ID:", id, "to status:", status);
      const {
        data: {
          session: { access_token },
        },
      } = await supabase.auth.getSession();

      const { data } = await axios.post(
        "https://dzhrjkuwvfqgljowmnsy.supabase.co/functions/v1/handle-hosting-request",
        {
          request_id: id,
          status: status,
        },
        {
          headers: {
            Authorization: `Bearer ${access_token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!data) {
        throw new Error("Failed to update request status");
      }

      return data[0] as HostingRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hostingRequests"] });
      toast.success("تم تحديث حالة الطلب بنجاح");
    },
    onError: (error) => {
      console.error("Error updating request status:", error);
      toast.error("فشل في تحديث حالة الطلب");
    },
  });

  // Function to get request by ID
  const getRequestById = (id: string): HostingRequest | undefined => {
    return hostingRequests.find((req) => req.id === id);
  };

  const { data: selectedHostingRequest, isLoading: currentReqLoading } =
    useQuery({
      queryKey: ["hostingRequest", id],
      queryFn: async () => {
        if (!id) return null;
        const { data, error } = await supabase
          .from("hosting_requests")
          .select(`*`)
          .eq("id", id)
          .single();

        const { data: products } = await supabase
          .from("hosting_products")
          .select(`*`)
          .eq("hosting_request_id", id);

        if (error) throw error;

        return {
          ...data,
          products: products || [],
        } as HostingRequest;
      },
      enabled: !!id,
    });

  // Refetch function to refresh the data

  return {
    hostingRequests,
    isLoading: isLoading,
    currentReqLoading: currentReqLoading,
    error: error ? (error as Error).message : null,
    updateHostingRequestStatus: (id: string, status: OrderStatus) =>
      updateStatusMutation.mutateAsync({ id, status }),
    isMutating: updateStatusMutation.isPending,
    getRequestById,
    refetch,
    selectedHostingRequest,
  };
}
