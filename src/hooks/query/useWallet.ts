
import { useMemo, useCallback } from "react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { useUserBalance } from "./useUserBalance";
import { useUserTransactions } from "./useUserTransactions";
import { useAddDeposit } from "./useAddDeposit";
import { useAddWithdrawal } from "./useAddWithdrawal";
import { useUpdateTransactionStatus } from "./useUpdateTransactionStatus";
import { usePlatformData, PLATFORM_DATA_KEY } from "./usePlatformData";

export const TRANSACTIONS_KEY = "transactions";
export const USER_BALANCE_KEY = "userBalance";

export function useWallet() {
  const { user } = useAuth();
  const { balance, isBalanceLoading, userId } = useUserBalance();
  const {
    transactions,
    isTransactionsLoading,
    refetchTransactions,
  } = useUserTransactions();

  const { addDeposit, isAddingDeposit } = useAddDeposit(userId);
  const { addWithdrawal, isAddingWithdrawal } = useAddWithdrawal(balance);
  const { updateTransactionStatus, isUpdatingStatus } = useUpdateTransactionStatus(
    transactions,
    balance
  );

  const fetchTransactions = useCallback(async () => {
    if (!userId) return;
    await refetchTransactions();
  }, [userId, refetchTransactions]);

  const getTransactionById = useCallback(
    (id: string) => transactions.find((tx) => tx.id === id),
    [transactions]
  );

  const prefetchPlatformData = useCallback(() => {
    if (userId) {
      // Platform data hook already memoizes and fetches
      // This is just for compatibility / eager load
    }
  }, [userId]);

  const { platformNumber, isLoading: isPlatformLoading } = usePlatformData();

  const isLoading =
    isBalanceLoading || isTransactionsLoading || isAddingDeposit || isAddingWithdrawal || isUpdatingStatus;

  return {
    balance,
    transactions,
    isLoading,
    addDeposit,
    addWithdrawal,
    fetchTransactions,
    updateTransactionStatus,
    getTransactionById,
    platformNumber,
    prefetchPlatformData,
  };
}
