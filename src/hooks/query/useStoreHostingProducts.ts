
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';

export interface HostingProduct {
  id: string;
  product_id: string;
  product_name: string;
  price: number;
  quantity: number;
  image?: string;
  hosting_request_id: string;
}

export function useStoreHostingProducts() {
  const { user } = useAuth();
  
  const {
    data: hostingProducts,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['storeHostingProducts', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      console.log("Fetching hosting products for store:", user.id);
      
      const { data: hostingRequests, error: requestsError } = await supabase
        .from('hosting_requests')
        .select(`
          id,
          store_id,
          ecommerce_id,
          status
        `)
        .eq('store_id', user.id)
        .in('status', ['accepted', 'ready']);
      
      if (requestsError) {
        console.error('Error fetching hosting requests:', requestsError);
        throw requestsError;
      }
      
      if (!hostingRequests || hostingRequests.length === 0) {
        console.log('No hosting requests found for store');
        return [];
      }
      
      const requestIds = hostingRequests.map(req => req.id);
      console.log(`Found ${requestIds.length} hosting requests, fetching products`);
      
      const { data, error } = await supabase
        .from('hosting_products')
        .select('*')
        .in('hosting_request_id', requestIds);
      
      if (error) {
        console.error('Error fetching hosting products:', error);
        throw error;
      }
      
      console.log(`Fetched ${data?.length || 0} hosting products`);
      return data as HostingProduct[] || [];
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  
  return {
    hostingProducts,
    isLoading,
    error: error ? (error as Error).message : null,
    refetch
  };
}
