
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { Transaction, TransactionStatus } from "@/types";
import { TRANSACTIONS_KEY, USER_BALANCE_KEY } from "./useWallet";

export function useUpdateTransactionStatus(transactions: Transaction[], balance: number) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: TransactionStatus;
    }) => {
      if (!user) throw new Error("User not authenticated");

      // Update transaction status
      const { error } = await supabase
        .from("transactions")
        .update({ status, updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) throw error;

      // Balance update for completed transactions
      if (status === "completed") {
        const transaction = transactions.find((tx) => tx.id === id);
        if (transaction) {
          let newBalance = balance;
          if (
            transaction.type === "deposit" ||
            transaction.type === "commission"
          ) {
            newBalance += transaction.amount;
          } else if (
            transaction.type === "withdraw" ||
            transaction.type === "fee"
          ) {
            newBalance -= transaction.amount;
          }
          const { error: balanceError } = await supabase
            .from("profiles")
            .update({ balance: newBalance })
            .eq("id", user.id);

          if (balanceError) throw balanceError;
        }
      }
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TRANSACTIONS_KEY, user?.id] });
      queryClient.invalidateQueries({ queryKey: [USER_BALANCE_KEY, user?.id] });
      toast.success("تم تحديث حالة المعاملة بنجاح");
    },
    onError: () => {
      toast.error("حدث خطأ أثناء تحديث حالة المعاملة");
    },
  });

  const updateTransactionStatus = async (
    id: string,
    status: TransactionStatus
  ): Promise<boolean> => {
    try {
      await mutation.mutateAsync({ id, status });
      return true;
    } catch {
      return false;
    }
  };

  return { updateTransactionStatus, isUpdatingStatus: mutation.isPending };
}
