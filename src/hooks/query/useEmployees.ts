import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useStoreData } from "../store/useStoreData";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Employee, StoreEmployee } from "@/types";

export function useEmployees() {
  const queryClient = useQueryClient();
  const { storeData } = useStoreData();
  const {
    data: employees,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["employees", storeData?.id],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("store_employees")
          .select("*")
          .eq("store_id", storeData?.id);

        if (error) {
          throw error;
        }
        return data as Partial<Employee>[];
      } catch (err) {
        console.error("Error loading employees:", err);
        toast.error("Failed to load employees", {
          description: err.message || "Please try again later",
        });
      }
    },
    enabled: !!storeData?.id,
  });
  const { mutate: deleteEmployee } = useMutation({
    mutationFn: async (employeeId: string) => {
      try {
        const { data, error } = await supabase
          .from("store_employees")
          .delete()
          .eq("id", employeeId);

        if (error) {
          throw error;
        }
        return data;
      } catch (err) {
        console.error("Error deleting employee:", err);
        toast.error("Failed to delete employee", {
          description: err.message || "Please try again later",
        });
      }
    },
    onSuccess: (data, employeeId) => {
      queryClient.setQueryData(["employees", storeData?.id], (old: any) => {
        return old.filter((employee: any) => employee.id !== employeeId);
      });
      toast.success("Employee deleted successfully", {
        description: "The employee has been removed from the store.",
      });
    },
  });

  const { mutate: updateEmployee } = useMutation({
    mutationFn: async (employee: StoreEmployee) => {
      try {
        const { data, error } = await supabase
          .from("store_employees")
          .update({
            email: employee.email,
            name: employee.name,
            phone_number: employee.phone_number,
            position: employee.position,
            active: employee.active,
          })
          .eq("id", employee.id);

        if (error) {
          throw error;
        }
        return data;
      } catch (err) {
        console.error("Error updating employee:", err);
        toast.error("Failed to update employee", {
          description: err.message || "Please try again later",
        });
      }
    },
    onSuccess: (data, employee) => {
      queryClient.setQueryData(["employees", storeData?.id], (old: any) => {
        return old.map((emp: any) => (emp.id === employee.id ? employee : emp));
      });
      toast.success("Employee updated successfully", {
        description: "The employee details have been updated.",
      });
    },
  });

  const { mutate: addEmployee } = useMutation({
    mutationFn: async (employee: Employee) => {
      try {
        const { data, error } = await supabase.from("store_employees").insert({
          email: employee.email,
          name: employee.name,
          phone_number: employee.phone_number,
          position: employee.position,
          store_id: storeData?.id,
        });

        if (error) {
          throw error;
        }
        return data;
      } catch (err) {
        console.error("Error adding employee:", err);
        toast.error("Failed to add employee", {
          description: err.message || "Please try again later",
        });
      }
    },
    onSuccess: (data, employee) => {
      queryClient.setQueryData(["employees", storeData?.id], (old: any) => {
        return [...old, employee];
      });
      toast.success("Employee added successfully", {
        description: "The employee has been added to the store.",
      });
    },
    onError: (error) => {
      toast.error("Failed to add employee", {
        description: error.message || "Please try again later",
      });
    },
  });

  return {
    deleteEmployee,
    employees,
    isLoading,
    error,
    updateEmployee,
    addEmployee,
    refetch: () =>
      queryClient.invalidateQueries({
        queryKey: ["employees", storeData?.id],
      }),
  };
}
