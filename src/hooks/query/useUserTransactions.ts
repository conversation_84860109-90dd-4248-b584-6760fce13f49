
import { useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { Transaction, TransactionType, TransactionStatus } from "@/types";

export function useUserTransactions() {
  const { user } = useAuth();
  const userId = useMemo(() => user?.id, [user?.id]);

  const fetchUserTransactions = useCallback(async () => {
    if (!userId) throw new Error("User not authenticated");
    const { data, error } = await supabase
      .from("transactions")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });
    if (error) throw error;
    return (data || []).map((tx) => ({
      id: tx.id,
      user_id: tx.user_id,
      user_name: tx.user_name || "",
      from_user_id: tx.from_user_id || null,
      to_user_id: tx.to_user_id || null,
      amount: tx.amount,
      type: tx.type as TransactionType,
      status: tx.status as TransactionStatus,
      description: tx.description || "",
      receipt_url: tx.receipt_url || "",
      reference: tx.reference || "",
      related_order_id: tx.related_order_id || "",
      created_at: tx.created_at,
      updated_at: tx.updated_at || tx.created_at,
    })) as Transaction[];
  }, [userId]);

  const {
    data: transactions = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["transactions", userId],
    queryFn: fetchUserTransactions,
    enabled: !!userId,
    staleTime: 60 * 1000,
    gcTime: 15 * 60 * 1000,
  });

  return { transactions, isTransactionsLoading: isLoading, userId, refetchTransactions: refetch };
}
