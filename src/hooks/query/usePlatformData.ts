import { useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";

// Query key for platform data
export const PLATFORM_DATA_KEY = "platformData";

/**
 * Custom hook to fetch platform data like bank account information
 * @returns Platform data including bank account number and loading state
 */
export const usePlatformData = () => {
  const { user } = useAuth();

  const fetchPlatformData = useCallback(async () => {
    // Use cache-first strategy with Supabase
    const { data, error } = await supabase
      .from("admin_public_data")
      .select("bank_account,iban")
      .single();

    if (error) {
      console.error("Error fetching platform data:", error);
      throw error; // Let React Query handle the error
    }

    return data || "";
  }, []);

  const {
    data: platformNumber = "",
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [PLATFORM_DATA_KEY, user?.id],
    queryFn: fetchPlatformData,
    // Only fetch if user is not an admin, as admins don't need this data
    enabled: !!user && user?.role !== "admin",
    // Cache the data for longer as this rarely changes
    staleTime: 60 * 60 * 1000 * 24, // 24 hours
    // Keep in cache for a long time
    gcTime: 60 * 60 * 1000 * 72, // 72 hours
    // Only retry once for network issues
    retry: 1,
  });

  return {
    platformNumber,
    isLoading,
    error,
    refetch,
  };
};
