
import { useState } from 'react';
import { useHosting } from '@/contexts/HostingContext';
import { HostingProduct, HostingRequest } from '@/types';
import { Store } from '@/types/store';
import { toast } from 'sonner';

export const useCreateHostingRequest = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addHostingRequest } = useHosting();

  const createHostingRequest = async (
    store: Store,
    products: HostingProduct[],
    notes: string,
    userId: string,
    userName: string,
    userLegalActivity?: string,
    durationStr?: string,
    subscriptionType?: string
  ) => {
    if (products.length === 0) {
      toast.error('يرجى اختيار منتج واحد على الأقل');
      return null;
    }

    setIsSubmitting(true);
    
    try {
      // Convert duration string to number if provided
      const duration = durationStr ? parseInt(durationStr) : undefined;
      
      const newRequest: Partial<HostingRequest> = {
        store_id: store.id,
        store_name: store.name,
        ecommerce_id: userId,
        ecommerce_name: userName,
        ecommerce_legal_activity: userLegalActivity,
        notes,
        products,
        duration,
        subscription_type: subscriptionType
      };
      
      const result = await addHostingRequest(newRequest);
      toast.success(`تم إرسال طلب استضافة إلى ${store.name} بنجاح`);
      return result;
    } catch (error) {
      console.error('Error creating hosting request:', error);
      toast.error('حدث خطأ أثناء إرسال طلب الاستضافة');
      return null;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    createHostingRequest,
    isSubmitting
  };
};
