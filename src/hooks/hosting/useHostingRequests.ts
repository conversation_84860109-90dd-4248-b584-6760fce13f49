import { useState, useEffect } from "react";
import { useHostingRequestsQuery } from "../query/useHostingRequestsQuery";
import { HostingRequest, OrderStatus } from "@/types";
import { sendHostingRequestNotification } from "@/services/notificationService";
import { supabase } from "@/integrations/supabase/client";

export const useHostingRequests = () => {
  const {
    hostingRequests,
    isLoading: queryLoading,
    updateHostingRequestStatus,
    refetch,
    isMutating,
    getRequestById: queryGetRequestById,
    selectedHostingRequest,
    currentReqLoading,
  } = useHostingRequestsQuery();

  const [requests, setRequests] = useState<HostingRequest[]>(hostingRequests);
  const [isLoading, setIsLoading] = useState(queryLoading || currentReqLoading);

  useEffect(() => {
    setRequests(hostingRequests);
  }, [hostingRequests]);

  const updateStatus = async (id: string, status: OrderStatus) => {
    setIsLoading(true);
    try {
      await updateHostingRequestStatus(id, status);

      // Get request details to send notification
      const request = getRequestById(id);
      if (request) {
        // Get ecommerce user email for notification
        const { data: userData, error: userError } = await supabase
          .from("profiles")
          .select("email")
          .eq("id", request.ecommerce_id)
          .single();

        if (!userError && userData?.email) {
          // Send notification email and in-app notification
          await sendHostingRequestNotification(
            id,
            status,
            request.store_name,
            request.ecommerce_id,
            userData.email,
            request.ecommerce_name
          );
        }
      }

      // Update local state
      setRequests((prev) =>
        prev.map((req) =>
          req.id === id
            ? { ...req, status, updated_at: new Date().toISOString() }
            : req
        )
      );
    } catch (error) {
      console.error("Error updating request status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRequestById = (id: string) => {
    return queryGetRequestById(id) || requests.find((req) => req.id === id);
  };

  return {
    requests,
    isLoading: isLoading || queryLoading,
    refetch,
    getRequestById,
    updateStatus,
    isMutating,
    currentReqLoading,
    selectedHostingRequest,
  };
};
