
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export function useLogout() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const logout = async () => {
    setIsLoading(true);
    
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error("Logout error:", error);
        toast.error("خطأ في تسجيل الخروج", {
          description: "حدث خطأ أثناء محاولة تسجيل الخروج، يرجى المحاولة مرة أخرى"
        });
        return false;
      }
      
      toast.success('تم تسجيل الخروج بنجاح');
      navigate('/');
      return true;
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("خطأ في تسجيل الخروج", {
        description: "حدث خطأ أثناء محاولة تسجيل الخروج، يرجى المحاولة مرة أخرى"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    logout,
    isLoading
  };
}
