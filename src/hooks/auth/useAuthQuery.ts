
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { User, Session } from '@supabase/supabase-js';
import { UserProfile, UserRole } from '@/contexts/auth/types';

interface AuthState {
  user: UserProfile | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export function useAuthQuery() {
  const queryClient = useQueryClient();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  // Session query
  const {
    data: sessionData,
    refetch: refetchSession
  } = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) throw error;
        return data.session;
      } catch (error) {
        console.error('Error fetching session:', error);
        return null;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // User profile query, dependent on session
  const { data: userProfile } = useQuery({
    queryKey: ['auth', 'profile', sessionData?.user?.id],
    queryFn: async () => {
      if (!sessionData?.user?.id) return null;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', sessionData.user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user profile:', error);
          throw error;
        }
        
        const profileData: UserProfile = {
          id: data.id,
          email: sessionData.user.email || '',
          name: data.name || '',
          role: data.role as UserRole,
          avatar: data.avatar || '',
          balance: data.balance || 0,
          created_at: data.created_at,
          phone_number: data.phone_number || '',
          address: data.address || '',
          bank_account: data.bank_account || '',
          legal_activity: data.legal_activity || '',
          permissions: data.permissions || [],
          active: data.active !== undefined ? data.active : true,
        };

        // Merge any additional data from auth.user that's not in the profile
        return profileData;
      } catch (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }
    },
    enabled: !!sessionData?.user?.id,
  });

  // Auth listener effect
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state changed:', event);
        
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          queryClient.invalidateQueries({ queryKey: ['auth', 'session'] });
          // Don't directly update state here to avoid recursive renders
          // Let the queries handle it
        } else if (event === 'SIGNED_OUT') {
          setAuthState({
            user: null,
            session: null,
            isLoading: false,
            isAuthenticated: false,
            error: null,
          });
          queryClient.resetQueries({ queryKey: ['auth'] });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);

  // Update auth state based on queries
  useEffect(() => {
    setAuthState(prev => ({
      ...prev,
      user: userProfile,
      session: sessionData,
      isAuthenticated: !!sessionData?.user,
      isLoading: false,
    }));
  }, [sessionData, userProfile]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      try {
        const { data, error } = await supabase.auth.signInWithPassword({ 
          email, 
          password 
        });
        
        if (error) throw error;
        
        return data;
      } catch (error: any) {
        console.error('Login error:', error);
        throw new Error(error.message);
      } finally {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    },
    onSuccess: () => {
      toast.success('تم تسجيل الدخول بنجاح');
      refetchSession();
    },
    onError: (error: Error) => {
      setAuthState(prev => ({ ...prev, error: error.message }));
      
      // Translate common error messages
      if (error.message.includes('Invalid login')) {
        toast.error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      } else if (error.message.includes('Email not confirmed')) {
        toast.error('لم يتم تأكيد البريد الإلكتروني بعد، يرجى التحقق من بريدك');
      } else {
        toast.error(error.message || 'حدث خطأ أثناء تسجيل الدخول');
      }
    }
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      try {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
      } catch (error: any) {
        console.error('Logout error:', error);
        throw new Error(error.message);
      } finally {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    },
    onSuccess: () => {
      setAuthState({
        user: null,
        session: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      });
      queryClient.resetQueries();
      toast.success('تم تسجيل الخروج بنجاح');
    },
    onError: (error: Error) => {
      toast.error('حدث خطأ أثناء تسجيل الخروج');
    }
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: async ({ 
      email, 
      password, 
      name, 
      role = 'ecommerce'
    }: { 
      email: string; 
      password: string; 
      name: string; 
      role?: UserRole;
    }) => {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name,
              role,
              balance: 0,
              active: true,
              avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`,
            },
            emailRedirectTo: `${window.location.origin}/dashboard`,
          }
        });
        
        if (error) throw error;
        
        return data;
      } catch (error: any) {
        console.error('Registration error:', error);
        throw new Error(error.message);
      } finally {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    },
    onSuccess: () => {
      toast.success('تم التسجيل بنجاح', {
        description: 'تم إرسال رابط التأكيد إلى بريدك الإلكتروني'
      });
    },
    onError: (error: Error) => {
      setAuthState(prev => ({ ...prev, error: error.message }));
      
      if (error.message.includes('already registered')) {
        toast.error('البريد الإلكتروني مسجل بالفعل');
      } else {
        toast.error(error.message || 'حدث خطأ أثناء التسجيل');
      }
    }
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (updates: Partial<UserProfile>) => {
      if (!authState.user?.id) {
        throw new Error('يجب تسجيل الدخول أولاً');
      }
      
      try {
        // Update auth user metadata
        const authUpdates: Record<string, any> = {};
        for (const [key, value] of Object.entries(updates)) {
          if (key !== 'id' && key !== 'email' && key !== 'created_at') {
            authUpdates[key] = value;
          }
        }
        
        if (Object.keys(authUpdates).length > 0) {
          const { error: authError } = await supabase.auth.updateUser({
            data: authUpdates
          });
          
          if (authError) throw authError;
        }
        
        // Update profile in database
        const { error: profileError } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', authState.user.id);
        
        if (profileError) throw profileError;
        
        return true;
      } catch (error: any) {
        console.error('Profile update error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('تم تحديث الملف الشخصي بنجاح');
      queryClient.invalidateQueries({ queryKey: ['auth', 'profile'] });
    },
    onError: (error: any) => {
      toast.error('فشل في تحديث الملف الشخصي');
    }
  });

  return {
    user: authState.user,
    session: authState.session,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,
    login: loginMutation.mutateAsync,
    logout: logoutMutation.mutateAsync,
    register: registerMutation.mutateAsync,
    updateProfile: updateProfileMutation.mutateAsync,
    refetchSession,
    clearError: () => setAuthState(prev => ({ ...prev, error: null }))
  };
}
