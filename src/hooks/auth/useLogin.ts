
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export function useLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Login error:", error);
        
        // تحسين رسائل الخطأ لتكون أكثر وضوحاً للمستخدم
        if (error.message.includes('Invalid login credentials')) {
          setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
        } else if (error.message.includes('Email not confirmed')) {
          setError('لم يتم تأكيد البريد الإلكتروني بعد. يرجى التحقق من بريدك الإلكتروني');
        } else {
          setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
        }
        
        return false;
      } 
      
      if (data && data.user) {
        console.log("Login successful:", data.user);
        
        // التحقق من صلاحيات المستخدم وتوجيهه للصفحة المناسبة
        const userRole = data.user.user_metadata.role;
        
        toast.success('تم تسجيل الدخول بنجاح', {
          description: `مرحباً ${data.user.user_metadata.name || 'بك'}`
        });
        
        if (userRole === 'admin') {
          navigate('/dashboard/admin');
        } else {
          navigate('/dashboard');
        }
        
        return true;
      } 
      
      setError('حدث خطأ ما، يرجى المحاولة مرة أخرى');
      return false;
    } catch (error: any) {
      console.error("Login error:", error);
      setError(error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    login,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}
