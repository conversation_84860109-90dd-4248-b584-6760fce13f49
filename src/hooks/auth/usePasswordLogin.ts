import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface LoginCredentials {
  email: string;
  password: string;
}

export function usePasswordLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error("Login error:", error);
        setError(error.message || "البريد الإلكتروني أو كلمة المرور غير صحيحة");
        return false;
      }
      if (data.user.role === "ecommerce" || data.user.role === "store") {
        const { data: isFinishedLegelData, error: isFinishedLegelError } =
          await supabase
            .from("legal_details")
            .select("*")
            .eq("profile_id", data.user.id)
            .single();
        const { data: updatedSession, error: updateError } =
          await supabase.auth.updateUser({
            data: {
              legal_details: !!isFinishedLegelData,
            },
          });
        if (updateError) {
          console.error(
            "Error updating session with legal details:",
            updateError
          );

          setError(updateError.message || "حدث خطأ ما، يرجى المحاولة مرة أخرى");
          return false;
          // You might want to handle this error, but continue with login anyway
        }
        return true;
      }

      if (data && data.user) {
        console.log("Login successful:", data.user);

        const userRole = data.user.user_metadata.role;

        if (userRole === "admin") {
          navigate("/dashboard/admin");
        } else {
          navigate("/dashboard");
        }

        return true;
      }

      setError("حدث خطأ ما، يرجى المحاولة مرة أخرى");
      return false;
    } catch (error: any) {
      console.error("Login error:", error);
      setError(error.message || "حدث خطأ ما، يرجى المحاولة مرة أخرى");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    login,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}
