
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useLogin } from './useLogin';
import { useLogout } from './useLogout';
import { useOTPAuthentication } from './useOTPAuthentication';

/**
 * Unified authentication service hook that combines login, logout and OTP functionality
 */
export function useAuthService() {
  const [isAuthLoading, setIsAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const navigate = useNavigate();
  
  // Import individual auth hooks
  const { login, isLoading: isLoginLoading } = useLogin();
  const { logout, isLoading: isLogoutLoading } = useLogout();
  const { 
    sendOTP, 
    verifyOTP, 
    isLoading: isOTPLoading 
  } = useOTPAuthentication();
  
  // Reset password functionality
  const resetPassword = async (email: string): Promise<boolean> => {
    setIsAuthLoading(true);
    setAuthError(null);
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });
      
      if (error) {
        console.error('Reset password error:', error);
        setAuthError(error.message);
        toast.error('خطأ في إعادة تعيين كلمة المرور', { 
          description: error.message 
        });
        return false;
      }
      
      toast.success('تم إرسال رابط إعادة تعيين كلمة المرور', { 
        description: 'يرجى التحقق من بريدك الإلكتروني' 
      });
      return true;
    } catch (error: any) {
      console.error('Reset password error:', error);
      setAuthError(error.message);
      toast.error('خطأ في إعادة تعيين كلمة المرور', { 
        description: error.message 
      });
      return false;
    } finally {
      setIsAuthLoading(false);
    }
  };
  
  // Update password functionality with or without auth session
  const updatePassword = async (password: string, token?: string): Promise<boolean> => {
    setIsAuthLoading(true);
    setAuthError(null);
    
    try {
      // If token is provided, use the manual password reset flow
      if (token) {
        // First, authenticate using the token
        const { error: signInError } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: 'recovery',
        });
        
        if (signInError) {
          console.error('Token verification error:', signInError);
          setAuthError(signInError.message);
          toast.error('خطأ في التحقق من الرمز', { 
            description: signInError.message 
          });
          return false;
        }
      }
      
      // Now update the password
      const { error } = await supabase.auth.updateUser({
        password: password,
      });
      
      if (error) {
        console.error('Update password error:', error);
        setAuthError(error.message);
        toast.error('خطأ في تحديث كلمة المرور', { 
          description: error.message 
        });
        return false;
      }
      
      toast.success('تم تحديث كلمة المرور بنجاح');
      return true;
    } catch (error: any) {
      console.error('Update password error:', error);
      setAuthError(error.message);
      toast.error('خطأ في تحديث كلمة المرور', { 
        description: error.message 
      });
      return false;
    } finally {
      setIsAuthLoading(false);
    }
  };
  
  return {
    // Login functionality
    login,
    
    // Logout functionality
    logout,
    
    // OTP functionality
    sendOTP,
    verifyOTP,
    
    // Reset password functionality
    resetPassword,
    updatePassword,
    
    // Loading states
    isLoading: isAuthLoading || isLoginLoading || isLogoutLoading || isOTPLoading,
    authError
  };
}
