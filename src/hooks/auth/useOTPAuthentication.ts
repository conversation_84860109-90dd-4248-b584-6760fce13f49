
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export function useOTPAuthentication() {
  const [isLoading, setIsLoading] = useState(false);
  
  const sendOTP = async (email: string, type: 'login' | 'register' | 'recovery' = 'login') => {
    setIsLoading(true);
    
    try {
      // Generate OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      
      // Set expiry time (15 minutes from now)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 15);
      
      // Store OTP in database
      const { error: storeError } = await supabase
        .from('otps')
        .upsert([
          {
            email,
            otp,
            expires_at: expiresAt.toISOString(),
            type: type
          }
        ], { onConflict: 'email,type' });
      
      if (storeError) {
        console.error('Error storing OTP:', storeError);
        toast.error('خطأ في إرسال لينك التحقق', {
          description: 'حدث خطأ أثناء محاولة إرسال لينك التحقق، يرجى المحاولة مرة أخرى'
        });
        return false;
      }
      
      // Send OTP via edge function
      const { error: sendError } = await supabase.functions.invoke('send-otp-email', {
        body: { 
          email, 
          otp,
          type
        }
      });
      
      if (sendError) {
        console.error('Error sending OTP:', sendError);
        toast.error('خطأ في إرسال لينك التحقق', {
          description: 'حدث خطأ أثناء محاولة إرسال لينك التحقق، يرجى المحاولة مرة أخرى'
        });
        return false;
      }
      
      toast.success('تم إرسال لينك التحقق', {
        description: 'تم إرسال لينك التحقق إلى بريدك الإلكتروني'
      });
      return true;
    } catch (error) {
      console.error('Error in sendOTP:', error);
      toast.error('خطأ في إرسال لينك التحقق', {
        description: error.message || 'حدث خطأ أثناء محاولة إرسال لينك التحقق، يرجى المحاولة مرة أخرى'
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOTP = async (email: string, otp: string, type: 'login' | 'register' | 'recovery' = 'login') => {
    setIsLoading(true);
    
    try {
      // Verify OTP from database
      const { data, error } = await supabase
        .from('otps')
        .select('*')
        .eq('email', email)
        .eq('otp', otp)
        .eq('type', type);

      if (error || !data || data.length === 0) {
        console.error('Error verifying OTP:', error);
        toast.error('لينك التحقق غير صحيح', {
          description: 'يرجى التأكد من اللينك المدخل أو طلب لينك جديد'
        });
        return false;
      }
      
      // Check if OTP is expired
      const expiresAt = new Date(data[0].expires_at);
      const now = new Date();
      
      if (now > expiresAt) {
        toast.error('انتهت صلاحية لينك التحقق', {
          description: 'يرجى طلب لينك جديد'
        });
        return false;
      }
      
      // Delete used OTP
      await supabase
        .from('otps')
        .delete()
        .eq('email', email)
        .eq('type', type);
      
      // If login OTP, sign in the user
      if (type === 'login') {
        const { data: signInData, error: signInError } = await supabase.auth.signInWithOtp({
          email,
          options:{
            emailRedirectTo:'https://rofof.sa/dashboard'
          }
        });
        
        if (signInError) {
          console.error('Error signing in with OTP:', signInError);
          toast.error('خطأ في تسجيل الدخول', {
            description: signInError.message
          });
          return false;
        }
      }
      
      toast.success('تم التحقق بنجاح');
      return true;
    } catch (error) {
      console.error('Error in verifyOTP:', error);
      toast.error('خطأ في التحقق من اللينك', {
        description: error.message || 'حدث خطأ أثناء محاولة التحقق من اللينك، يرجى المحاولة مرة أخرى'
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    sendOTP,
    verifyOTP,
    isLoading
  };
}
