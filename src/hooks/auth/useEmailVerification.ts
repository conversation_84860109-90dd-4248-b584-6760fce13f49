
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export function useEmailVerification() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendVerificationEmail = async (email: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
      });
      
      if (error) {
        console.error('Error sending verification email:', error);
        setError(error.message);
        toast.error('خطأ في إرسال رسالة التحقق', {
          description: error.message
        });
        return false;
      }
      
      toast.success('تم إرسال رسالة التحقق', {
        description: 'يرجى التحقق من بريدك الإلكتروني'
      });
      return true;
    } catch (error: any) {
      console.error('Error sending verification email:', error);
      setError(error.message);
      toast.error('خطأ في إرسال رسالة التحقق', {
        description: error.message
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyEmail = async (token: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // For Supabase, email verification is typically handled via URL
      // This is just a placeholder function in case custom verification is needed
      // Usually Supabase redirects the user and handles this automatically
      
      // You might need to implement custom verification logic if needed
      return true;
    } catch (error: any) {
      console.error('Error verifying email:', error);
      setError(error.message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    sendVerificationEmail,
    verifyEmail,
    isLoading,
    error
  };
}
