import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/auth';
import { onboardingService } from '@/services/onboardingService';

export function useOnboardingStatus() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      // Only check for store and ecommerce users
      if (user.role !== 'store' && user.role !== 'ecommerce') {
        setHasCompletedOnboarding(true);
        setIsLoading(false);
        return;
      }

      try {
        // Check if user has completed onboarding
        const { data, error } = await onboardingService.getUserOnboardingProgress(user.id);

        if (error) {
          console.error('Error checking onboarding status:', error);
          setHasCompletedOnboarding(false); // Default to false to show onboarding
        } else {
          setHasCompletedOnboarding(data?.is_completed || false);
        }
      } catch (error) {
        console.error('Exception checking onboarding status:', error);
        setHasCompletedOnboarding(false); // Default to false to show onboarding
      } finally {
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user]);

  const redirectToOnboardingIfNeeded = () => {
    if (user && !isLoading && hasCompletedOnboarding === false) {
      // Only redirect store or ecommerce users
      if (user.role === 'store' || user.role === 'ecommerce') {
        navigate('/onboarding');
        return true;
      }
    }
    return false;
  };

  const markOnboardingComplete = () => {
    setHasCompletedOnboarding(true);
  };

  return {
    hasCompletedOnboarding,
    isLoading,
    redirectToOnboardingIfNeeded,
    markOnboardingComplete
  };
}
