
import { useState } from 'react';
import { toast } from 'sonner';
import { useOTPAuthentication } from './useOTPAuthentication';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/auth';

export function useOTPLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const [showVerification, setShowVerification] = useState(false);
  const { verifyOTP, sendOTP } = useOTPAuthentication();
  const navigate = useNavigate();
  const auth = useAuth();

  const initiateOTPLogin = async (userEmail: string): Promise<boolean> => {
    if (!userEmail || !userEmail.trim()) {
      setError('يرجى إدخال البريد الإلكتروني');
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    setEmail(userEmail);
    
    try {
      const sent = await sendOTP(userEmail, 'login');
      if (sent) {
        setShowVerification(true);
        toast.success('تم إرسال رمز التحقق', {
          description: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
        });
        return true;
      } else {
        setError('فشل إرسال لينك التحقق');
        return false;
      }
    } catch (error) {
      console.error('OTP login error:', error);
      setError(error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى');
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSuccessfulVerification = async () => {
    try {
      toast.success('تم تسجيل الدخول بنجاح');
      navigate('/dashboard');
    } catch (error) {
      console.error('Sign-in error:', error);
      setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
    }
  };
  
  const resetOTPLogin = () => {
    setShowVerification(false);
    setError(null);
  };
  
  return {
    isLoading,
    error,
    email,
    showVerification,
    initiateOTPLogin,
    setError,
    clearError: () => setError(null),
    maxAttempts: 3,
    handleSuccessfulVerification,
    resetOTPLogin
  };
}
