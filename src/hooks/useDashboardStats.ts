import { useState, useEffect, useCallback } from "react";
import { useAuth, UserProfile } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useQuery, useQueryClient, useQueries } from "@tanstack/react-query";
import { Order } from "@/types";

interface DashboardStats {
  users: number;
  products: number;
  transactions: number;
  stores: number;
  branches: number;
  orders: number;
  revenue: number;
}

// Define a simpler interface for orders
interface SimpleOrder {
  id: string;
  total?: number;
  total_amount?: number;
  user_id?: string;
}

export const useDashboardStats = () => {
  const { stats, isLoading, isError, error, refreshStats } = useStats();

  return {
    stats,
    isLoading,
    isError,
    error,
    refreshStats,
  };
};

export default useDashboardStats;

const useStats = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const ordersQuery = useQuery({
    queryKey: ["orders", user?.id, user?.role],
    queryFn: () => fetchOrders(user),
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const statsQuery = useQuery({
    queryKey: ["stats", user?.id, user?.role, ordersQuery.data?.length],
    queryFn: () => fetchStats(user, ordersQuery.data || []),
    enabled: !!user && !ordersQuery.isLoading && ordersQuery.data !== undefined,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const refreshStats = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["orders", user?.id] });
    queryClient.invalidateQueries({ queryKey: ["stats", user?.id] });
  }, [queryClient, user?.id]);

  return {
    stats: statsQuery.data || {},
    isLoading: ordersQuery.isLoading || statsQuery.isLoading,
    isError: ordersQuery.isError || statsQuery.isError,
    error: ordersQuery.error || statsQuery.error,
    refreshStats,
  };
};

// Fetch orders function
const fetchOrders = async (user: UserProfile): Promise<SimpleOrder[]> => {
  if (!user) return [];

  try {
    if (user.role === "admin" || user.role === "sub-admin") {
      const { data, error } = await supabase
        .from("hosting_requests")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } else if (user.role === "store") {
      const { data: store, error: storeError } = await supabase
        .from("stores")
        .select("id")
        .eq("owner_id", user.id)
        .single();

      if (storeError) throw storeError;

      const { data, error } = await supabase
        .from("hosting_requests")
        .select("*")
        .eq("store_id", store.id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } else if (user.role === "ecommerce") {
      const { data, error } = await supabase
        .from("hosting_requests")
        .select("*")
        .eq("ecommerce_id", user.id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    }
    return [];
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw error;
  }
};

// Fetch stats function
const fetchStats = async (
  user: UserProfile,
  orders: SimpleOrder[]
): Promise<Partial<DashboardStats>> => {
  if (!user) return {};

  try {
    const statsData: Partial<DashboardStats> = {};

    if (user.role === "admin" || user.role === "sub-admin") {
      // Execute queries in parallel for better performance
      const [
        usersResponse,
        productsResponse,
        transactionsResponse,
        storesResponse,
      ] = await Promise.all([
        supabase.from("profiles").select("*", { count: "exact", head: true }),
        supabase.from("products").select("*", { count: "exact", head: true }),
        supabase
          .from("transactions")
          .select("*", { count: "exact", head: true }),
        supabase
          .from("profiles")
          .select("*", { count: "exact", head: true })
          .or("role.eq.store,role.eq.ecommerce"),
      ]);

      if (usersResponse.error) throw usersResponse.error;
      if (productsResponse.error) throw productsResponse.error;
      if (transactionsResponse.error) throw transactionsResponse.error;
      if (storesResponse.error) throw storesResponse.error;

      statsData.users = usersResponse.count || 0;
      statsData.products = productsResponse.count || 0;
      statsData.transactions = transactionsResponse.count || 0;
      statsData.stores = storesResponse.count || 0;
    } else if (user.role === "store") {
      // Get store info
      const { data: store, error: storeError } = await supabase
        .from("stores")
        .select("id,subscription")
        .eq("owner_id", user.id)
        .single();

      if (storeError) throw storeError;

      // Execute branch count and approved requests queries in parallel
      const [branchesResponse, approvedRequestsResponse] = await Promise.all([
        supabase
          .from("branches")
          .select("*", { count: "exact", head: true })
          .eq("store_id", store.id),
        supabase
          .from("hosting_requests")
          .select("id")
          .eq("store_id", store.id)
          .eq("status", "accepted"),
      ]);

      if (branchesResponse.error) throw branchesResponse.error;
      if (approvedRequestsResponse.error) throw approvedRequestsResponse.error;

      statsData.branches = branchesResponse.count || 0;

      // Only query products if we have approved requests

      if (approvedRequestsResponse.data.length > 0) {
        const { count: productsCount, error: productsError } = await supabase
          .from("hosting_products")
          .select("*", { count: "exact", head: true })
          .in(
            "hosting_request_id",
            approvedRequestsResponse.data.map((r) => r.id)
          );

        if (productsError) throw productsError;
        statsData.products = productsCount || 0;
      } else {
        statsData.products = 0;
      }
      statsData.orders = orders.length;
      statsData.revenue =
        approvedRequestsResponse.data.length * Number(store.subscription || 0);
    } else if (user.role === "ecommerce") {
      const { data: products, error: productsError } = await supabase
        .from("products")
        .select("*")
        .eq("seller_id", user.id);

      if (productsError) throw productsError;

      statsData.products = products.length || 0;

      if (orders.length > 0) {
        statsData.orders = orders.length;
        // statsData.revenue = orders.reduce(
        //   (total, order) => total + (order.total || order.total_amount || 0),
        //   0
        // );
      }
      const { data: transactions, error: transactionsError } = await supabase
        .from("transactions")
        .select("*")
        .eq("user_id", user.id)
        .eq("type", "transfer")
        .eq("to_user_id", user.id);
      console.log("transactions", transactions);

      if (transactionsError) throw transactionsError;
      statsData.revenue = transactions.reduce(
        (total, transaction) => total + (transaction.amount || 0),
        0
      );
    } else {
      if (orders.length > 0) {
        const userOrders = orders.filter((order) => order.user_id === user.id);
        statsData.orders = userOrders.length;
        statsData.revenue = userOrders.reduce(
          (total, order) => total + (order.total || order.total_amount || 0),
          0
        );
      }
    }

    return statsData;
  } catch (error) {
    console.error("Error fetching stats:", error);
    throw error;
  }
};
