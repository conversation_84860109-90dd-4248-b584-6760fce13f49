
import { useAuth } from "@/contexts/auth";
import { useEffect, useState } from "react";
import { UserRole, AdminPermission } from "@/types";
import { useNavigate } from "react-router-dom";
import { hasPermission, hasAnyPermission, hasAllPermissions } from "@/utils/roleUtils";
import { toast } from "sonner";

/**
 * Hook محسن للتحقق من دور المستخدم وتقييد الوصول إلى الصفحات
 * يوفر إمكانيات متقدمة للتحقق من الصلاحيات المتعددة
 */
export function useAuthRole(allowedRoles: UserRole[] = []) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [hasAccess, setHasAccess] = useState(false);
  const [checkedPermissions, setCheckedPermissions] = useState<{[key: string]: boolean}>({});
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAccess = async () => {
      setIsCheckingAccess(true);
      
      // إذا كنا ما زلنا في مرحلة التحميل، لا نفعل شيئًا بعد
      if (isLoading) return;
      
      // إذا لم يكن المستخدم مصادقًا، نوجهه إلى صفحة تسجيل الدخول
      if (!isAuthenticated) {
        toast.error("يجب تسجيل الدخول للوصول إلى هذه الصفحة");
        navigate('/login');
        return;
      }
      
      // إذا لم يتم تحديد أدوار مسموحة، أي مستخدم مصادق له حق الوصول
      if (allowedRoles.length === 0) {
        setHasAccess(true);
        setIsCheckingAccess(false);
        return;
      }
      
      // التحقق مما إذا كان للمستخدم أحد الأدوار المسموحة
      if (user && allowedRoles.includes(user.role as UserRole)) {
        setHasAccess(true);
      } else {
        // إعادة التوجيه بناءً على دور المستخدم
        if (user) {
          switch (user.role) {
            case 'admin':
              navigate('/dashboard/admin');
              break;
            case 'sub-admin':
              navigate('/dashboard/admin');
              break;
            case 'store':
            case 'ecommerce':
            default:
              navigate('/dashboard');
              break;
          }
          toast.error("ليس لديك صلاحية للوصول إلى هذه الصفحة");
        }
      }
      
      setIsCheckingAccess(false);
    };
    
    checkAccess();
  }, [isAuthenticated, isLoading, user, allowedRoles, navigate]);
  
  /**
   * التحقق من وجود صلاحية معينة
   */
  const checkPermission = (permission: AdminPermission): boolean => {
    if (!user || !isAuthenticated) return false;
    
    // مدير النظام دائمًا لديه جميع الصلاحيات
    if (user.role === 'admin') return true;
    
    // التحقق من وجود الصلاحية في مصفوفة صلاحيات المستخدم
    return hasPermission(user.permissions as AdminPermission[], permission);
  };

  /**
   * التحقق من وجود أي من الصلاحيات المطلوبة
   */
  const checkAnyPermission = (permissions: AdminPermission[]): boolean => {
    if (!user || !isAuthenticated) return false;
    
    // مدير النظام دائمًا لديه جميع الصلاحيات
    if (user.role === 'admin') return true;
    
    return hasAnyPermission(user.permissions as AdminPermission[], permissions);
  };

  /**
   * التحقق من وجود جميع الصلاحيات المطلوبة
   */
  const checkAllPermissions = (permissions: AdminPermission[]): boolean => {
    if (!user || !isAuthenticated) return false;
    
    // مدير النظام دائمًا لديه جميع الصلاحيات
    if (user.role === 'admin') return true;
    
    return hasAllPermissions(user.permissions as AdminPermission[], permissions);
  };

  /**
   * حفظ نتيجة التحقق من الصلاحية في الذاكرة المؤقتة
   */
  const checkPermissionMemoized = (permission: AdminPermission): boolean => {
    if (checkedPermissions[permission] !== undefined) {
      return checkedPermissions[permission];
    }
    
    const result = checkPermission(permission);
    setCheckedPermissions(prev => ({...prev, [permission]: result}));
    return result;
  };

  return {
    hasAccess,
    isCheckingAccess,
    isAdmin: user?.role === 'admin',
    isSubAdmin: user?.role === 'sub-admin',
    isStore: user?.role === 'store',
    isEcommerce: user?.role === 'ecommerce',
    userRole: user?.role,
    hasPermission: checkPermissionMemoized,
    hasAnyPermission: checkAnyPermission,
    hasAllPermissions: checkAllPermissions,
    userPermissions: user?.permissions as AdminPermission[] | undefined
  };
}
