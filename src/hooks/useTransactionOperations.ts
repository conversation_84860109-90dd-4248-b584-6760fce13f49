
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface DepositParams {
  amount: number;
  description?: string;
  receipts?: File[];
}

export interface WithdrawalParams {
  amount: number;
  description?: string;
}

export const useTransactionOperations = (userId?: string, userName?: string) => {
  const queryClient = useQueryClient();
  
  // Deposit money mutation
  const depositMutation = useMutation({
    mutationFn: async ({ amount, description = '', receipts = [] }: DepositParams): Promise<boolean> => {
      try {
        console.log('Creating deposit transaction:', { amount, description, receipts });
        
        if (!userId || !userName) {
          console.error('Missing user information for deposit');
          throw new Error('معلومات المستخدم غير متوفرة، يرجى تسجيل الدخول مرة أخرى');
        }

        if (amount <= 0) {
          throw new Error('يجب أن يكون المبلغ أكبر من صفر');
        }
        
        let receiptUrl = '';
        
        if (receipts.length > 0) {
          const file = receipts[0];
          const fileExt = file.name.split('.').pop();
          const fileName = `${userId}-${Date.now()}.${fileExt}`;
          
          try {
            // Check if receipts bucket exists, if not create it
            const { data: buckets } = await supabase.storage.listBuckets();
            
            if (!buckets || !buckets.find(b => b.name === 'receipts')) {
              await supabase.storage.createBucket('receipts', {
                public: true
              });
              console.log('Created new receipts bucket');
            }
          } catch (bucketError) {
            console.error('Error checking/creating bucket:', bucketError);
          }
          
          const { error: uploadError, data: uploadData } = await supabase.storage
            .from('receipts')
            .upload(fileName, file);
            
          if (uploadError) {
            console.error('Error uploading receipt:', uploadError);
            throw new Error('فشل رفع الإيصال، جرب مرة أخرى');
          }
          
          const { data: urlData } = supabase.storage
            .from('receipts')
            .getPublicUrl(fileName);
            
          if (urlData) {
            receiptUrl = urlData.publicUrl;
            console.log('Receipt URL:', receiptUrl);
          }
        }
        
        const timestamp = new Date().toISOString();
        
        // Use the create-transaction edge function
        const { data, error } = await supabase.functions.invoke('create-transaction', {
          body: {
            user_id: userId,
            user_name: userName,
            amount,
            type: 'deposit',
            description,
            receipt_url: receiptUrl,
            reference: `DEP-${Math.floor(Math.random() * 1000000)}`,
            created_at: timestamp,
            updated_at: timestamp
          }
        });
        
        if (error) {
          console.error('Error creating deposit:', error);
          throw new Error('حدث خطأ أثناء إنشاء الإيداع: ' + error.message);
        }
        
        console.log('Deposit created successfully:', data);
        return true;
      } catch (error: any) {
        console.error('Exception creating deposit:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('تم استلام طلب الإيداع بنجاح وسيتم مراجعته');
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
    }
  });
  
  // Withdraw money mutation
  const withdrawMutation = useMutation({
    mutationFn: async ({ amount, description = '' }: WithdrawalParams): Promise<boolean> => {
      try {
        console.log('Creating withdrawal transaction:', { amount, description });
        
        if (!userId || !userName) {
          console.error('Missing user information for withdrawal');
          throw new Error('معلومات المستخدم غير متوفرة، يرجى تسجيل الدخول مرة أخرى');
        }

        if (amount <= 0) {
          throw new Error('يجب أن يكون المبلغ أكبر من صفر');
        }
        
        // Fetch user balance to verify sufficient funds
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('balance')
          .eq('id', userId)
          .single();
          
        if (userError) {
          console.error('Error fetching user balance:', userError);
          throw new Error('فشل في التحقق من الرصيد المتاح');
        }
        
        const balance = userData?.balance || 0;
        if (amount > balance) {
          throw new Error('رصيدك غير كافي لإتمام عملية السحب');
        }
        
        const timestamp = new Date().toISOString();
        
        // Use the create-transaction edge function
        const { data, error } = await supabase.functions.invoke('create-transaction', {
          body: {
            user_id: userId,
            user_name: userName,
            amount,
            type: 'withdraw',
            description,
            reference: `WTH-${Math.floor(Math.random() * 1000000)}`,
            created_at: timestamp,
            updated_at: timestamp
          }
        });
        
        if (error) {
          console.error('Error creating withdrawal:', error);
          throw new Error('حدث خطأ أثناء إنشاء طلب السحب: ' + error.message);
        }
        
        console.log('Withdrawal created successfully:', data);
        return true;
      } catch (error: any) {
        console.error('Exception creating withdrawal:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('تم استلام طلب السحب بنجاح وسيتم مراجعته');
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
    }
  });
  
  return {
    depositMoney: depositMutation.mutateAsync,
    withdrawMoney: withdrawMutation.mutateAsync,
    isDepositing: depositMutation.isPending,
    isWithdrawing: withdrawMutation.isPending
  };
};
