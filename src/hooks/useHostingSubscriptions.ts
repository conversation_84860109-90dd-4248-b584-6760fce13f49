
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { toast } from 'sonner';

export function useHostingSubscriptions() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Get all active hosting subscriptions for current user
  const {
    data: activeSubscriptions,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['hostingSubscriptions', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      // Different query based on user role
      let query = supabase
        .from('hosting_requests')
        .select(`
          *,
          hosting_products(*)
        `)
        .in('status', ['pending', 'accepted', 'ready']);
        
      if (user.role === 'store') {
        query = query.eq('store_id', user.id);
      } else if (user.role === 'ecommerce') {
        query = query.eq('ecommerce_id', user.id);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching active subscriptions:', error);
        throw error;
      }
      
      return data || [];
    },
    enabled: !!user
  });
  
  // Get expired but not renewed subscriptions
  const {
    data: expiredSubscriptions,
    isLoading: isLoadingExpired
  } = useQuery({
    queryKey: ['expiredSubscriptions', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      let query = supabase
        .from('hosting_requests')
        .select(`
          *,
          hosting_products(*)
        `)
        .lt('expires_at', new Date().toISOString())
        .eq('status', 'accepted');
        
      if (user.role === 'store') {
        query = query.eq('store_id', user.id);
      } else if (user.role === 'ecommerce') {
        query = query.eq('ecommerce_id', user.id);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching expired subscriptions:', error);
        throw error;
      }
      
      return data || [];
    },
    enabled: !!user
  });
  
  // Get subscriptions expiring soon (within 7 days)
  const {
    data: expiringSubscriptions
  } = useQuery({
    queryKey: ['expiringSubscriptions', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
      
      let query = supabase
        .from('hosting_requests')
        .select(`
          *,
          hosting_products(*)
        `)
        .lt('expires_at', sevenDaysFromNow.toISOString())
        .gt('expires_at', new Date().toISOString())
        .eq('status', 'accepted');
        
      if (user.role === 'store') {
        query = query.eq('store_id', user.id);
      } else if (user.role === 'ecommerce') {
        query = query.eq('ecommerce_id', user.id);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching expiring subscriptions:', error);
        throw error;
      }
      
      return data || [];
    },
    enabled: !!user
  });
  
  // Extend a hosting subscription
  const extendSubscriptionMutation = useMutation({
    mutationFn: async ({ 
      requestId, 
      extensionMonths = 1 
    }: { 
      requestId: string; 
      extensionMonths?: number 
    }) => {
      if (!user) throw new Error('User not authenticated');
      
      // Get the current subscription
      const { data: currentSubscription, error: fetchError } = await supabase
        .from('hosting_requests')
        .select('*')
        .eq('id', requestId)
        .single();
      
      if (fetchError) {
        console.error('Error fetching subscription details:', fetchError);
        throw new Error('فشل في العثور على الاشتراك');
      }
      
      // Calculate new expiration date
      let newExpiryDate;
      
      if (new Date(currentSubscription.expires_at) < new Date()) {
        // If already expired, set new date from now
        newExpiryDate = new Date();
      } else {
        // If not expired, extend from current expiry date
        newExpiryDate = new Date(currentSubscription.expires_at);
      }
      
      // Add extension months
      newExpiryDate.setMonth(newExpiryDate.getMonth() + extensionMonths);
      
      // Update subscription with new expiry date
      const { error: updateError } = await supabase
        .from('hosting_requests')
        .update({
          expires_at: newExpiryDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);
      
      if (updateError) {
        console.error('Error updating subscription expiry:', updateError);
        throw new Error('فشل في تحديث فترة الاشتراك');
      }
      
      return { success: true, newExpiryDate };
    },
    onSuccess: () => {
      toast.success('تم تمديد فترة الاشتراك بنجاح');
      queryClient.invalidateQueries({ queryKey: ['hostingSubscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['expiredSubscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['expiringSubscriptions'] });
    },
    onError: (error) => {
      toast.error('فشل في تمديد فترة الاشتراك');
      console.error('Error extending subscription:', error);
    }
  });
  
  // Cancel a hosting subscription
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (requestId: string) => {
      if (!user) throw new Error('User not authenticated');
      
      // Update subscription status to cancelled
      const { error } = await supabase
        .from('hosting_requests')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);
      
      if (error) {
        console.error('Error cancelling subscription:', error);
        throw new Error('فشل في إلغاء الاشتراك');
      }
      
      return { success: true };
    },
    onSuccess: () => {
      toast.success('تم إلغاء الاشتراك بنجاح');
      queryClient.invalidateQueries({ queryKey: ['hostingSubscriptions'] });
    },
    onError: (error) => {
      toast.error('فشل في إلغاء الاشتراك');
      console.error('Error cancelling subscription:', error);
    }
  });

  return {
    activeSubscriptions,
    expiredSubscriptions,
    expiringSubscriptions,
    isLoading: isLoading || isLoadingExpired,
    error: error ? (error as Error).message : null,
    extendSubscription: extendSubscriptionMutation.mutateAsync,
    cancelSubscription: cancelSubscriptionMutation.mutateAsync,
    refetch
  };
}
