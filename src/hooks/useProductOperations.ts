import { useProducts } from "@/contexts/ProductContext";
import { Product } from "@/types";
import { useAddProduct } from "@/hooks/product/useAddProduct";

export const useProductOperations = () => {
  const { addProduct: addNewProduct, isLoading: isAddingProduct } =
    useAddProduct();
  try {
    // Try to use the products context
    return useProducts();
  } catch (error) {
    // If the context is not available, use direct hooks
    console.warn(
      "ProductProvider not found, using direct product operations hooks"
    );

    return {
      products: [],
      userProducts: [],
      isLoading: isAddingProduct,
      error: null,
      loadProducts: async () => [],
      addProduct: async (productData: any): Promise<Product | null> => {
        return await addNewProduct(productData);
      },
      updateProduct: async () => null,
      deleteProduct: async () => false,
      getProductById: () => null,
    };
  }
};
