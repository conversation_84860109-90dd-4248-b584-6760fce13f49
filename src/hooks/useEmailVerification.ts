
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { toast } from 'sonner';

export function useEmailVerification() {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Process email verification from URL params
  const verifyEmailFromURL = async () => {
    try {
      setIsVerifying(true);
      setVerificationError(null);
      
      const url = new URL(window.location.href);
      const token = url.searchParams.get('token');
      const type = url.searchParams.get('type');
      
      // If no token or not a verification URL, ignore
      if (!token || type !== 'email') {
        setIsVerifying(false);
        return;
      }
      
      // Use Supabase to verify the email token
      const { error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'email',
      });
      
      if (error) {
        console.error('Email verification failed:', error);
        setVerificationError(error.message);
        toast.error('فشل التحقق من البريد الإلكتروني', {
          description: error.message || 'حدث خطأ أثناء محاولة التحقق من البريد الإلكتروني'
        });
        
        // Redirect to error page
        navigate('/auth/verification-error', { 
          state: { error: error.message } 
        });
        return;
      }
      
      // Verification successful
      setVerificationSuccess(true);
      toast.success('تم التحقق بنجاح', {
        description: 'تم التحقق من بريدك الإلكتروني بنجاح'
      });
      
      // Redirect based on authentication status
      if (isAuthenticated) {
        navigate('/dashboard');
      } else {
        navigate('/auth/verification-success');
      }
    } catch (err: any) {
      console.error('Error processing verification:', err);
      setVerificationError(err.message);
      navigate('/auth/verification-error', { 
        state: { error: err.message } 
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return {
    isVerifying,
    verificationError,
    verificationSuccess,
    verifyEmailFromURL
  };
}
