
import { useState, useEffect, useCallback } from 'react';
import { User, UserRole } from '@/types';
import { fetchUsers, fetchUserById, updateUser, deleteUser, createUser, changeUserRole } from '@/services/userService';
import { useToast } from '@/hooks/toast';

interface UseUsersOptions {
  initialPage?: number;
  pageSize?: number;
  initialSearch?: string;
  initialRole?: UserRole | 'all';
  autoFetch?: boolean;
}

export function useUsers(options: UseUsersOptions = {}) {
  const {
    initialPage = 1,
    pageSize = 10,
    initialSearch = '',
    initialRole = 'all',
    autoFetch = true,
  } = options;

  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(autoFetch);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(initialPage);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [search, setSearch] = useState<string>(initialSearch);
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>(initialRole);
  const { toast } = useToast();

  const fetchUsersList = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await fetchUsers(
        page, 
        pageSize, 
        search, 
        roleFilter === 'all' ? undefined : roleFilter as UserRole
      );
      
      setUsers(result.users);
      setTotalCount(result.count);
    } catch (err: any) {
      setError(err.message || 'Failed to load users');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to load users',
      });
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, search, roleFilter, toast]);

  useEffect(() => {
    if (autoFetch) {
      fetchUsersList();
    }
  }, [fetchUsersList, autoFetch]);

  const refreshUsers = () => {
    fetchUsersList();
  };

  const getUser = async (id: string): Promise<User | null> => {
    setIsLoading(true);
    try {
      const user = await fetchUserById(id);
      return user;
    } catch (err: any) {
      setError(err.message || 'Failed to load user');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to load user',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserData = async (id: string, userData: Partial<User>): Promise<boolean> => {
    setIsLoading(true);
    try {
      const result = await updateUser(id, userData);
      
      if (result.success) {
        toast({
          title: 'User updated',
          description: 'The user has been updated successfully',
        });
        
        // Update the user in the local state
        setUsers(prevUsers => 
          prevUsers.map(user => 
            user.id === id ? { ...user, ...userData } : user
          )
        );
        
        return true;
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.error || 'Failed to update user',
        });
        return false;
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update user');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to update user',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const removeUser = async (id: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const result = await deleteUser(id);
      
      if (result.success) {
        toast({
          title: 'User deleted',
          description: 'The user has been deleted successfully',
        });
        
        // Remove the user from the local state
        setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
        return true;
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.error || 'Failed to delete user',
        });
        return false;
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete user');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to delete user',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const addUser = async (userData: {
    email: string;
    password: string;
    name: string;
    role: UserRole;
    phoneNumber?: string;
    address?: string;
  }): Promise<boolean> => {
    setIsLoading(true);
    try {
      const result = await createUser(userData);
      
      if (result.success) {
        toast({
          title: 'User created',
          description: 'A new user has been created successfully',
        });
        
        // Refresh the user list
        await fetchUsersList();
        return true;
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.error || 'Failed to create user',
        });
        return false;
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create user');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to create user',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const changeRole = async (id: string, role: UserRole): Promise<boolean> => {
    setIsLoading(true);
    try {
      const result = await changeUserRole(id, role);
      
      if (result.success) {
        toast({
          title: 'Role updated',
          description: `User role has been changed to ${role}`,
        });
        
        // Update the user in the local state
        setUsers(prevUsers => 
          prevUsers.map(user => 
            user.id === id ? { ...user, role } : user
          )
        );
        
        return true;
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.error || 'Failed to update role',
        });
        return false;
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update role');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to update role',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    users,
    isLoading,
    error,
    page,
    totalCount,
    search,
    roleFilter,
    totalPages: Math.ceil(totalCount / pageSize),
    setPage,
    setSearch,
    setRoleFilter,
    refreshUsers,
    getUser,
    updateUser: updateUserData,
    deleteUser: removeUser,
    createUser: addUser,
    changeUserRole: changeRole,
  };
}
