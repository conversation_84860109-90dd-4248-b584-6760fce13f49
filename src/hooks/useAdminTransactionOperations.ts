import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useAdminTransactionOperations = () => {
  const queryClient = useQueryClient();

  const approveMutation = useMutation({
    mutationFn: async (transaction_id: string): Promise<boolean> => {
      try {
        console.log(`Approving transaction: ${transaction_id}`);

        // Call the approve-transaction edge function
        const { data, error } = await supabase.functions.invoke(
          "approve-transaction",
          {
            body: {
              transaction_id,
              approved: true,
              reason: "",
            },
          }
        );

        if (error) {
          console.error("Error approving transaction:", error);
          toast.error("حدث خطأ أثناء الموافقة على المعاملة");
          throw error;
        }

        return true;
      } catch (error) {
        console.error("Error in approveTransaction:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success("تم الموافقة على المعاملة بنجاح");
      queryClient.invalidateQueries({ queryKey: ["adminTransactions"] });
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: () => {
      toast.error("حدث خطأ أثناء الموافقة على المعاملة");
    },
  });

  const rejectMutation = useMutation({
    mutationFn: async ({
      id,
      reason = "",
    }: {
      id: string;
      reason?: string;
    }): Promise<boolean> => {
      try {
        console.log(
          `Rejecting transaction: ${id}, reason: ${reason || "None provided"}`
        );

        // Call the approve-transaction edge function with approved=false
        const { data, error } = await supabase.functions.invoke(
          "approve-transaction",
          {
            body: {
              transaction_id: id,
              approved: false,
              reseon: reason,
            },
          }
        );

        if (error) {
          console.error("Error rejecting transaction:", error);
          toast.error("حدث خطأ أثناء رفض المعاملة");
          throw error;
        }

        return true;
      } catch (error) {
        console.error("Error in rejectTransaction:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success("تم رفض المعاملة بنجاح");
      queryClient.invalidateQueries({ queryKey: ["adminTransactions"] });
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: () => {
      toast.error("حدث خطأ أثناء رفض المعاملة");
    },
  });

  return {
    approveTransaction: approveMutation.mutateAsync,
    rejectTransaction: rejectMutation.mutateAsync,
    isApproving: approveMutation.isPending,
    isRejecting: rejectMutation.isPending,
  };
};
