
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Product, ProductStatus } from '@/types';

interface UseDeleteProductOptions {
  onSuccess?: (id: string) => void;
  onError?: (error) => void;
  updateLocalState?: boolean;
  getProducts?: () => Product[];
  setProducts?: (products: Product[]) => void;
}

export const useDeleteProduct = (options: UseDeleteProductOptions = {}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const deleteProduct = useCallback(
    async (productId: string) => {
      if (!productId) {
        const err = new Error('Product ID is required');
        setError(err);
        options.onError?.(err);
        return false;
      }

      setIsDeleting(true);
      setError(null);

      try {
        // Option 1: Mark the product as deleted (soft delete)
        const { error: updateError } = await supabase
          .from('products')
          .update({ status: 'deleted' as ProductStatus, updated_at: new Date().toISOString() })
          .eq('id', productId);

        if (updateError) {
          // Option 2: If soft delete fails, try to actually delete the product
          const { error: deleteError } = await supabase
            .from('products')
            .delete()
            .eq('id', productId);

          if (deleteError) {
            throw new Error(deleteError.message);
          }
        }

        // Update local state if requested
        if (options.updateLocalState && options.getProducts && options.setProducts) {
          const currentProducts = options.getProducts();
          // Either filter out the product or mark it as deleted
          options.setProducts(
            currentProducts.map((p) => {
              if (p.id === productId) {
                return { ...p, status: 'deleted' as ProductStatus };
              }
              return p;
            })
          );
        }

        toast.success('تم حذف المنتج بنجاح');
        options.onSuccess?.(productId);
        return true;
      } catch (err) {
        const error = new Error(
          err.message || 'حدث خطأ أثناء حذف المنتج'
        );
        setError(error);
        toast.error(error.message);
        options.onError?.(error);
        return false;
      } finally {
        setIsDeleting(false);
      }
    },
    [options]
  );

  return {
    deleteProduct,
    isDeleting,
    error,
  };
};
