
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Product } from '@/types';
import { useAuth } from '@/contexts/auth';

export const useAddProduct = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const addProduct = async (productData: {
    name: string;
    price: number;
    description: string;
    category: string;
    quantity: number;
    images?: string[];
    status?: string;
  }): Promise<Product | null> => {
    if (!user) {
      toast.error('You must be logged in to add products');
      return null;
    }

    setIsLoading(true);
    try {
      // Create a product with the current user as the seller
      const newProduct = {
        ...productData,
        seller_id: user.id,
        seller_name: user.name,
        user_id: user.id, // For compatibility with existing code
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: productData.status || 'active',
      };

      const { data, error } = await supabase
        .from('products')
        .insert(newProduct)
        .select()
        .single();

      if (error) {
        console.error('Error adding product:', error);
        toast.error('Failed to add product', {
          description: error.message,
        });
        return null;
      }

      toast.success('Product added successfully');
      
      // Map stock to quantity if needed for compatibility
      const productWithStock = {
        ...data,
        stock: data.quantity
      };

      return productWithStock as Product;
    } catch (error) {
      console.error('Error adding product:', error);
      toast.error('Failed to add product', {
        description: error.message,
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { addProduct, isLoading };
};
