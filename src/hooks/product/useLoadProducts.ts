import {useCallback, useEffect, useState} from 'react';
import {supabase} from '@/integrations/supabase/client';
import {Product} from '@/types';
import {useAuth} from '@/contexts/auth';
import {toast} from 'sonner';

export function useLoadProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const loadProducts = useCallback(async (filters?: {
    userId?: string;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    status?: string;
  }) => {
    if (!user) {
      return [];
    }

    setIsLoading(true);
    setError(null);
    try {
      let query = supabase
        .from('products')
        .select('*');

      // تطبيق الفلاتر المختلفة
      if (filters?.userId) {
        query = query.eq('user_id', filters.userId);
      } else if (user.role !== 'admin') {
        // للمستخدمين العاديين، أظهر فقط منتجاتهم
        query = query.eq('user_id', user.id);
      }

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      if (filters?.minPrice !== undefined) {
        query = query.gte('price', filters.minPrice);
      }

      if (filters?.maxPrice !== undefined) {
        query = query.lte('price', filters.maxPrice);
      }

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      const { data, error } = await query;

      if (error) throw error;
      console.log("Loaded products:", data);
      setProducts(data as Product[]);
      return data as Product[];
    } catch (err) {
      console.error('Error loading products:', err);
      setError(err.message || 'Failed to load products');
      toast(err?.message)
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // تحميل المنتجات عند تغيير المستخدم
  useEffect(() => {
    if (user) {
      loadProducts();
    }
  }, [user, loadProducts]);

  return { products, isLoading, error, loadProducts };
}
