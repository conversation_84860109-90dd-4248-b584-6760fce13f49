
import { useState } from 'react';
import { Product } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/toast';

export const useUpdateProduct = (
  products: Product[],
  setProducts: React.Dispatch<React.SetStateAction<Product[]>>
) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const updateProduct = async (id: string, productData: Partial<Product>): Promise<boolean> => {
    if (!id) return false;
    
    setIsUpdating(true);
    
    try {
      // Find the product in local state first
      const productIndex = products.findIndex(p => p.id === id);
      if (productIndex === -1) {
        toast.error('المنتج غير موجود');
        setIsUpdating(false);
        return false;
      }
      
      // If offline or dealing with an offline product
      if (!navigator.onLine || id.startsWith('offline-')) {
        return handleOfflineProductUpdate(id, productData);
      }
      
      // Create database update object using snake_case for fields
      const dbUpdateData: any = {};
      
      if (productData.name !== undefined) dbUpdateData.name = productData.name;
      if (productData.description !== undefined) dbUpdateData.description = productData.description;
      if (productData.price !== undefined) dbUpdateData.price = productData.price;
      if (productData.quantity !== undefined) dbUpdateData.quantity = productData.quantity;
      if (productData.category !== undefined) dbUpdateData.category = productData.category;
      if (productData.images !== undefined) dbUpdateData.images = productData.images;
      if (productData.status !== undefined) dbUpdateData.status = productData.status;
      
      console.log('Updating product in database:', id, dbUpdateData);
      
      // Send to database
      const { data, error } = await supabase
        .from('products')
        .update(dbUpdateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        console.error('Database error when updating product:', error);
        
        // Fall back to offline mode only for specific errors
        if (error.message.includes('network') || 
            error.code === '42P17' ||
            error.message.includes('recursion') ||
            !navigator.onLine) {
          console.warn('Using offline product update due to connectivity issues');
          return handleOfflineProductUpdate(id, productData);
        }
        
        toast.error('حدث خطأ أثناء تحديث المنتج: ' + error.message);
        setIsUpdating(false);
        return false;
      }
      
      if (!data) {
        toast.error('فشل تحديث المنتج');
        setIsUpdating(false);
        return false;
      }
      
      // Map database data to our Product type
      const updatedProduct: Product = {
        ...products[productIndex],
        ...productData,
        updated_at: new Date().toISOString()
      };
      
      // Update products state
      const updatedProducts = [...products];
      updatedProducts[productIndex] = updatedProduct;
      setProducts(updatedProducts);
      
      // Update local storage
      localStorage.setItem('products', JSON.stringify(updatedProducts));
      
      toast.success('تم تحديث المنتج بنجاح');
      
      setIsUpdating(false);
      return true;
    } catch (error) {
      console.error('Error in updateProduct:', error);
      setIsUpdating(false);
      toast.error('حدث خطأ غير متوقع أثناء تحديث المنتج');
      return false;
    }
  };

  // Helper function for offline product updates - used only as fallback
  const handleOfflineProductUpdate = (id: string, productData: Partial<Product>): boolean => {
    console.warn('Updating product in offline mode - will sync later when online');
    
    try {
      const productIndex = products.findIndex(p => p.id === id);
      if (productIndex === -1) return false;
      
      // Create updated product
      const updatedProduct: Product = {
        ...products[productIndex],
        ...productData,
        updated_at: new Date().toISOString()
      };
      
      // Update local state
      const updatedProducts = [...products];
      updatedProducts[productIndex] = updatedProduct;
      setProducts(updatedProducts);
      
      // Update local storage
      localStorage.setItem('products', JSON.stringify(updatedProducts));
      
      toast.success('تم تحديث المنتج بنجاح (وضع محلي - سيتم المزامنة لاحقاً)');
      setIsUpdating(false);
      return true;
    } catch (err) {
      console.error('Error in offline product update:', err);
      toast.error('حدث خطأ أثناء تحديث المنتج محلياً');
      setIsUpdating(false);
      return false;
    }
  };
  
  return { updateProduct, isUpdating };
};
