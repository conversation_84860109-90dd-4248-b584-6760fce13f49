import { useProductOperations } from "@/hooks/product/useProductOperations";
import { Product } from "@/types";
import { useEffect, useState } from "react";
import { useAddProduct } from "./useAddProduct";
import { useLoadProducts } from "./useLoadProducts";

export function useProductContext() {
  const [products, setProducts] = useState<Product[]>([]);
  const [userProducts, setUserProducts] = useState<Product[]>([]);
  const [error, setError] = useState<string | null>(null);

  // استخدام الهوكس
  const {
    loadProducts: fetchProducts,
    products: loadedProducts,
    isLoading: isLoadingProducts,
    error: loadError,
  } = useLoadProducts();
  const { addProduct: createProduct, isLoading: isAddingProduct } =
    useAddProduct();
  const {
    updateProduct,
    deleteProduct,
    getProductById,
    isLoading: isOperationLoading,
  } = useProductOperations();

  // تحديث المنتجات عند تغيير loadedProducts
  useEffect(() => {
    setProducts(loadedProducts);
    setUserProducts(loadedProducts);
  }, [loadedProducts]);

  // تحديث حالة الخطأ عند تغيير loadError
  useEffect(() => {
    setError(loadError);
  }, [loadError]);

  // تحميل جميع المنتجات
  const loadProducts = async () => {
    try {
      await fetchProducts();
    } catch (err) {
      console.error("Failed to load products:", err);
      setError("Failed to load products");
    }
  };

  // الحصول على منتج معين بواسطة المعرف
  const getProduct = (id: string) => {
    return products.find((product) => product.id === id);
  };

  const isLoading = isLoadingProducts || isAddingProduct || isOperationLoading;

  return {
    products,
    userProducts,
    isLoading,
    error,
    loadProducts,
    addProduct: createProduct,
    updateProduct,
    deleteProduct,
    getProduct,
  };
}
