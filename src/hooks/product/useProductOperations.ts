
import { useCallback, useState } from 'react';
import { Product } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export function useProductOperations() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProduct = useCallback(async (id: string, product: Partial<Product>): Promise<Product | null> => {
    try {
      setIsLoading(true);
      console.log('Updating product:', id, product);
      
      const { data, error } = await supabase.from('products').update(product).eq('id', id).select();
      
      if (error) {
        console.error('Error updating product:', error);
        toast.error('حدث خطأ أثناء تحديث المنتج: ' + error.message);
        return null;
      }
      
      if (data && data.length > 0) {
        console.log('Product updated successfully:', data[0]);
        toast.success('تم تحديث المنتج بنجاح');
        return data[0] as Product;
      }
      
      toast.error('فشل تحديث المنتج، لم يتم إرجاع بيانات');
      return null;
    } catch (err) {
      console.error('Exception during product update:', err);
      toast.error('حدث خطأ أثناء تحديث المنتج: ' + (err.message || 'خطأ غير معروف'));
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('Deleting product:', id);
      
      const { error } = await supabase.from('products').delete().eq('id', id);
      
      if (error) {
        console.error('Error deleting product:', error);
        toast.error('حدث خطأ أثناء حذف المنتج: ' + error.message);
        return false;
      }
      
      console.log('Product deleted successfully');
      toast.success('تم حذف المنتج بنجاح');
      return true;
    } catch (err) {
      console.error('Exception during product deletion:', err);
      toast.error('حدث خطأ أثناء حذف المنتج: ' + (err.message || 'خطأ غير معروف'));
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getProductById = useCallback(async (id: string): Promise<Product | null> => {
    try {
      console.log('Getting product by ID:', id);
      const { data, error } = await supabase.from('products').select('*').eq('id', id).single();
      
      if (error) {
        console.error('Error getting product by ID:', error);
        toast.error('حدث خطأ أثناء جلب بيانات المنتج: ' + error.message);
        return null;
      }
      
      return data as Product;
    } catch (err) {
      console.error('Exception getting product by ID:', err);
      toast.error('حدث خطأ أثناء جلب بيانات المنتج: ' + (err.message || 'خطأ غير معروف'));
      return null;
    }
  }, []);

  return {
    isLoading,
    error,
    updateProduct,
    deleteProduct,
    getProductById
  };
}
