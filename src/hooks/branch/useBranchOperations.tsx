
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Branch } from '@/types/branch';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

export function useBranchOperations() {
  const [isLoading, setIsLoading] = useState(false);

  // إنشاء فرع جديد
  const createBranch = useCallback(async (
    branchData: Omit<Branch, 'id' | 'updated_at'> & { store_id: string }
  ): Promise<Branch | null> => {
    try {
      setIsLoading(true);
      
      // التحقق من وجود المتجر قبل إنشاء الفرع
      const { data: storeExists, error: storeCheckError } = await supabase
        .from('stores')
        .select('id')
        .eq('id', branchData.store_id)
        .single();
  
      if (storeCheckError || !storeExists) {
        toast.error('لا يمكن إضافة الفرع لأن المتجر غير موجود');
        return null;
      }
  
      const newBranch = {
        id: uuidv4(),
        ...branchData,
        updated_at: new Date().toISOString(),
        active: branchData.active !== undefined ? branchData.active : true
      };
  
      const { data, error } = await supabase
        .from('branches')
        .insert(newBranch)
        .select('*')
        .single();
  
      if (error) {
        console.error('Error creating branch:', error);
        toast.error('فشل في إنشاء الفرع');
        return null;
      }
  
      return data as Branch;
    } catch (error) {
      console.error('Error in createBranch:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // تحديث فرع
  const updateBranch = useCallback(async (
    id: string,
    updates: Partial<Branch>
  ): Promise<Branch | null> => {
    try {
      setIsLoading(true);

      // تحديث تاريخ التعديل
      const updatedData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('branches')
        .update(updatedData)
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating branch:', error);
        toast.error('فشل في تحديث الفرع');
        return null;
      }

      return data as Branch;
    } catch (error) {
      console.error('Error in updateBranch:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // حذف فرع
  const deleteBranch = useCallback(async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('branches')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting branch:', error);
        toast.error('فشل في حذف الفرع');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteBranch:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // جلب فرع معين بواسطة المعرف
  const getBranchById = useCallback(async (id: string): Promise<Branch | null> => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error getting branch:', error);
        return null;
      }

      return data as Branch;
    } catch (error) {
      console.error('Error in getBranchById:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    createBranch,
    updateBranch,
    deleteBranch,
    getBranchById,
    isLoading
  };
}

export default useBranchOperations;
