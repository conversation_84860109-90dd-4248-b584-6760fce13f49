import {useCallback, useState} from 'react';
import {supabase} from '@/integrations/supabase/client';
import {Branch} from '@/types/branch';
import {useAuth} from '@/contexts/auth';
import {useStoreData} from "@/hooks/store/useStoreData.ts";

export function useBranchFetch() {
  const { user } = useAuth();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const {storeData} = useStoreData();


  // جلب المتاجر الخاصة بالمستخدم

  
  // جلب الفروع الخاصة بمتجر معين
  const fetchBranches = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // إذا لم يتم تمرير معرف المتجر، قم بجلبه أولاً
      const targetStoreId = storeData?.id;

      if (!targetStoreId) {
        setIsLoading(false);
        return;
      }
      
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .eq('store_id', targetStoreId)
        .order('created_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      setBranches(data as Branch[]);
    } catch (err: any) {
      console.error("Error fetching branches:", err);
      setError(err.message || "Failed to fetch branches");
    } finally {
      setIsLoading(false);
    }
  }, [user , storeData?.id]);

  return {
    branches,
    isLoading,
    error,
    storeId: storeData?.id,
    setBranches,
    fetchBranches
  };
}

export default useBranchFetch;
