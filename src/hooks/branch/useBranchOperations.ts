import {supabase} from "@/integrations/supabase/client";
import {Branch} from "@/types";
import {useCallback, useState} from "react";
import {toast} from "sonner";
import {useStoreData} from "@/hooks/store/useStoreData.ts";

export function useBranchOperations() {
  const [isLoading, setIsLoading] = useState(false);
  const {storeData} = useStoreData();
  // إنشاء فرع جديد
  const createBranch = useCallback(
      async (
          branchData: Partial<Branch> & { store_id: string }
      ): Promise<Branch | null> => {
        try {
          setIsLoading(true);

          const {data, error} = await supabase
              .from("branches")
              .insert([
                {
                  store_id: storeData.id,
                  name: branchData.name,
                  address: branchData.address,
                  city: branchData.city,
                  phone_number: branchData.phone_number,
                  manager_name: branchData.manager_name,
                  working_hours: branchData.working_hours,
                  active: branchData.active,
                  legal_activity: branchData.legal_activity,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                  images: branchData.images || [],
                },
              ])
              .select("*")
              .single();

          if (error) {
            console.error("Error creating branch:", error.message);
            toast.error("فشل في إنشاء الفرع");
            return null;
          }

          return data as Branch;
        } catch (error) {
          console.error("Error in createBranch:", error);
          return null;
        } finally {
          setIsLoading(false);
        }
      },
      [storeData?.id]
  );

  // تحديث فرع
  const updateBranch = useCallback(
      async (id: string, updates: Partial<Branch>): Promise<Branch | null> => {
        try {
          setIsLoading(true);

          // تحديث تاريخ التعديل
          const updatedData = {
            ...updates,
            updated_at: new Date().toISOString(),
          };


          const {data, error} = await supabase
              .from("branches")
              .update(updatedData)
              .eq("id", id)
              .select("*")
              .single();

          if (error) {
            console.error("Error updating branch:", error);
            toast.error("فشل في تحديث الفرع");
            return null;
          }

          return data as Branch;
        } catch (error) {
          console.error("Error in updateBranch:", error);
          return null;
        } finally {
          setIsLoading(false);
        }
      },
      []
  );

  // حذف فرع
  const deleteBranch = useCallback(async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      const {error} = await supabase.from("branches").delete().eq("id", id);

      if (error) {
        console.error("Error deleting branch:", error);
        toast.error("فشل في حذف الفرع");
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error in deleteBranch:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // جلب فرع معين بواسطة المعرف
  const getBranchById = useCallback(
      async (id: string): Promise<Branch | null> => {
        try {
          setIsLoading(true);

          const {data, error} = await supabase
              .from("branches")
              .select("*")
              .eq("id", id)
              .single();

          if (error) {
            console.error("Error getting branch:", error);
            return null;
          }

          return data as Branch;
        } catch (error) {
          console.error("Error in getBranchById:", error);
          return null;
        } finally {
          setIsLoading(false);
        }
      },
      []
  );

  return {
    createBranch,
    updateBranch,
    deleteBranch,
    getBranchById,
    isLoading,
  };
}

export default useBranchOperations;
