
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Branch } from '@/types/branch';
import { useAuth } from '@/contexts/auth';

export function useBranchFetch() {
  const { user } = useAuth();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storeId, setStoreId] = useState<string | null>(null);
  
  // جلب المتاجر الخاصة بالمستخدم
  const fetchUserStores = useCallback(async () => {
    if (!user) return null;
    
    try {
      const { data, error } = await supabase
        .from('stores')
        .select('id, name')
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      if (data && data.length > 0) {
        setStoreId(data[0].id);
        return data[0].id;
      } else {
        setError('لا توجد متاجر مسجلة. قم بإنشاء متجر أولاً.');
        return null;
      }
    } catch (err) {
      console.error("Error fetching stores:", err);
      setError(err.message || "Failed to fetch stores");
      return null;
    }
  }, [user]);
  
  // جلب الفروع الخاصة بمتجر معين
  const fetchBranches = useCallback(async (storeId?: string) => {
    if (!user) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // إذا لم يتم تمرير معرف المتجر، قم بجلبه أولاً
      const targetStoreId = storeId || await fetchUserStores();
      
      if (!targetStoreId) {
        setIsLoading(false);
        return;
      }
      
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .eq('store_id', targetStoreId)
        .order('created_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      setBranches(data as Branch[]);
    } catch (err) {
      console.error("Error fetching branches:", err);
      setError(err.message || "Failed to fetch branches");
    } finally {
      setIsLoading(false);
    }
  }, [user, fetchUserStores]);

  return {
    branches,
    isLoading,
    error,
    storeId,
    setBranches,
    fetchBranches,
    fetchUserStores
  };
}

export default useBranchFetch;
