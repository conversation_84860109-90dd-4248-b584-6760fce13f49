
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// Helper to cache usernames by userId and fetch them as needed
export function useUserName(userId?: string) {
  const queryClient = useQueryClient();
  return useQuery({
    queryKey: ["userName", userId],
    queryFn: async () => {
      if (!userId) return "-";
      // Check cache for user name to avoid unnecessary requests
      const cached: string | undefined = queryClient.getQueryData(["userName", userId]);
      if (cached) return cached;
      const { data, error } = await supabase
        .from("profiles")
        .select("name")
        .eq("id", userId)
        .maybeSingle();
      if (error) {
        console.error("Error fetching username for", userId, error);
        return "-";
      }
      if (!data?.name) return "-";
      return data.name;
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    enabled: !!userId,
  });
}
