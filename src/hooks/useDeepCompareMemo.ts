
import { useRef, useMemo } from 'react';
import { isEqual } from 'lodash';

/**
 * Custom hook for memoizing values with deep comparison
 * Prevents unnecessary re-renders when dealing with objects or arrays
 * that are structurally the same but have different references
 */
export function useDeepCompareMemo<T>(value: T): T {
  const ref = useRef<T>(value);
  
  // Only update the ref if the deep comparison shows the value has changed
  if (!isEqual(value, ref.current)) {
    ref.current = value;
  }
  
  // Use ref.current which has a stable reference if values are deeply equal
  return useMemo(() => ref.current, [ref.current]);
}

/**
 * Hook to track component renders for debugging performance issues
 */
export function useRenderCount(componentName: string): void {
  const renderCount = useRef(0);
  
  if (import.meta.env.DEV) {
    renderCount.current += 1;
    console.log(`${componentName} render count: ${renderCount.current}`);
  }
}
