
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/auth';
import { Order, OrderItem } from '@/types';

export const useOrders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { user } = useAuth();

  // بيانات وهمية للطلبات للعرض أثناء التطوير
  const mockOrders: Order[] = [
    {
      id: 'ORD-001',
      user_id: 'USR-001',
      customer_id: 'USR-001',
      customer_name: 'أحمد محمد',
      customer: 'أحمد محمد',
      date: '2025-04-01',
      total: 320,
      total_amount: 320,
      status: 'completed',
      items: [],
      created_at: '2025-04-01T10:30:00Z',
      updated_at: '2025-04-01T10:30:00Z',
    },
    {
      id: 'ORD-002',
      user_id: 'USR-002',
      customer_id: 'USR-002',
      customer_name: 'فاطمة علي',
      customer: 'فاطمة علي',
      date: '2025-04-02',
      total: 155,
      total_amount: 155,
      status: 'processing',
      items: [],
      created_at: '2025-04-02T14:20:00Z',
      updated_at: '2025-04-02T14:20:00Z',
    },
    {
      id: 'ORD-003',
      user_id: 'USR-003',
      customer_id: 'USR-003',
      customer_name: 'خالد عبدالله',
      customer: 'خالد عبدالله',
      date: '2025-04-03',
      total: 240,
      total_amount: 240,
      status: 'pending',
      items: [],
      created_at: '2025-04-03T09:15:00Z',
      updated_at: '2025-04-03T09:15:00Z',
    },
    {
      id: 'ORD-004',
      user_id: 'USR-004',
      customer_id: 'USR-004',
      customer_name: 'نورة سعد',
      customer: 'نورة سعد',
      date: '2025-04-04',
      total: 510,
      total_amount: 510,
      status: 'completed',
      items: [],
      created_at: '2025-04-04T16:45:00Z',
      updated_at: '2025-04-04T16:45:00Z',
    },
    {
      id: 'ORD-005',
      user_id: 'USR-005',
      customer_id: 'USR-005',
      customer_name: 'محمد العلي',
      customer: 'محمد العلي',
      date: '2025-04-05',
      total: 125,
      total_amount: 125,
      status: 'pending',
      items: [],
      created_at: '2025-04-05T11:10:00Z',
      updated_at: '2025-04-05T11:10:00Z',
    }
  ];

  const fetchOrders = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      // في الوقت الحالي نستخدم بيانات وهمية
      // في المستقبل عندما يتم إنشاء جدول الطلبات بقاعدة البيانات
      // سنستبدل هذا الكود بجلب البيانات من قاعدة البيانات
      
      // منطق استرجاع البيانات من Supabase سيكون هنا
      // const { data, error } = await supabase.from('orders').select('*');
      // if (error) throw error;
      // setOrders(data);
      
      // حالياً سنستخدم البيانات الوهمية
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('فشل في تحميل الطلبات');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [user]);

  const getOrderById = (id: string): Order | undefined => {
    return orders.find(order => order.id === id);
  };

  return {
    orders,
    isLoading,
    fetchOrders,
    getOrderById
  };
};

export default useOrders;
