import React, { createContext, useContext, ReactNode } from "react";
import { Notification } from "@/types";
import { useNotificationsQuery } from "@/hooks/useNotificationsQuery";

interface NotificationContextType {
  userNotifications: Notification[];
  isLoading: boolean;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({ children }: { children: ReactNode }) {
  const { userNotifications, isLoading, markAsRead, markAllAsRead } =
    useNotificationsQuery();

  return (
    <NotificationContext.Provider
      value={{
        userNotifications,
        isLoading,
        markAsRead,
        markAllAsRead,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
}
