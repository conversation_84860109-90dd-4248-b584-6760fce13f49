import React, { createContext, useCallback, useContext } from "react";
import { Branch } from "@/types/branch";
import { useAuth } from "@/contexts/auth";
import { useBranchOperations } from "@/hooks/branch/useBranchOperations";
import { useBranchFetch } from "@/hooks/branch/useBranchFetch";
import { toast } from "sonner";

interface BranchContextProps {
  branches: Branch[];
  isLoading: boolean;
  error: string;
  createBranch: (branchData: Omit<Branch, "id" | "store_id">) => Promise<void>;
  updateBranch: (id: string, updates: Partial<Branch>) => Promise<boolean>;
  deleteBranch: (id: string) => Promise<boolean>;
  loadBranches: (id?: string) => Promise<void>;
}

const BranchContext = createContext<BranchContextProps | undefined>(undefined);

export const useBranch = () => {
  const context = useContext(BranchContext);
  if (!context) {
    throw new Error("useBranch must be used within a BranchProvider");
  }
  return context;
};

export const BranchProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { user } = useAuth();
  const {
    isLoading: isOperationLoading,
    createBranch: createBranchOp,
    updateBranch: updateBranchOp,
    deleteBranch: deleteBranchOp,
  } = useBranchOperations();
  const {
    branches,
    isLoading: isFetchLoading,
    error,
    setBranches,
    fetchBranches,
  } = useBranchFetch();

  const createBranch = useCallback(
    async (branchData: Omit<Branch, "id">) => {
      if (!user) return;

      try {
        if (user.role !== "store") {
          toast.error("لا يمكنك إنشاء فرع جديد");
          return;
        }

        const newBranchData = {
          ...branchData,
          store_id: user.id,
        };
        console.log(newBranchData, "newBranchData", branchData);

        const newBranch = await createBranchOp(newBranchData);

        if (newBranch) {
          setBranches((prev) => [newBranch, ...prev]);
          toast.success("تم إنشاء الفرع بنجاح");
        }
      } catch (err) {
        console.error("Error creating branch:", err);
        toast.error("فشل في إنشاء الفرع");
      }
    },
    [user, createBranchOp, setBranches]
  );

  const updateBranch = useCallback(
    async (id: string, updates: Partial<Branch>) => {
      if (!user) return;

      try {
        const updatedBranch = await updateBranchOp(id, updates);

        if (updatedBranch) {
          setBranches((prev) =>
            prev.map((branch) => (branch.id === id ? updatedBranch : branch))
          );
          toast.success("تم تحديث الفرع بنجاح");
          return true;
        }
      } catch (err) {
        console.error("Error updating branch:", err);
        toast.error("فشل في تحديث الفرع");
        return false;
      }
    },
    [user, updateBranchOp, setBranches]
  );

  const deleteBranch = useCallback(
    async (id: string) => {
      if (!user) return;

      try {
        const success = await deleteBranchOp(id);

        if (success) {
          setBranches((prev) => prev.filter((branch) => branch.id !== id));
          toast.success("تم حذف الفرع بنجاح");
          return true;
        }
      } catch (err) {
        console.error("Error deleting branch:", err);
        toast.error("فشل في حذف الفرع");
        return false;
      }
    },
    [user, deleteBranchOp, setBranches]
  );

  const loadBranches = useCallback(async () => {
    await fetchBranches();
  }, [fetchBranches]);

  const isLoading = isFetchLoading || isOperationLoading;

  const value: BranchContextProps = {
    branches,
    isLoading,
    error,
    createBranch,
    updateBranch,
    deleteBranch,
    loadBranches,
  };

  return (
    <BranchContext.Provider value={value}>{children}</BranchContext.Provider>
  );
};

export default BranchProvider;
