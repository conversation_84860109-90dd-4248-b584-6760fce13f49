import React, {
  createContext,
  useContext,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { Transaction, TransactionStatus, TransactionType } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "./auth";
import { useWallet } from "@/hooks/query/useWallet";

interface FinanceContextType {
  balance: number;
  transactions: Transaction[];
  isLoading: boolean;
  addDeposit: (
    amount: number,
    receipt: File | null,
    notes?: string,
    reference?: string
  ) => Promise<boolean>;
  addWithdrawal: (
    amount: number,
    notes?: string,
    reference?: string
  ) => Promise<boolean>;
  fetchTransactions: () => Promise<void>;
  updateTransactionStatus: (
    id: string,
    status: TransactionStatus
  ) => Promise<boolean>;
  getTransactionById?: (id: string) => Transaction | undefined;
  platformNumber: {
    bank_account: string;
    iban: string;
  };
}

const FinanceContext = createContext<FinanceContextType | undefined>(undefined);

export const FinanceProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const {
    addDeposit,
    addWithdrawal,
    updateTransactionStatus,
    getTransactionById,
    balance,
    isLoading,
    fetchTransactions,
    platformNumber,
    transactions,
    prefetchPlatformData,
  } = useWallet();

  // Prefetch platform data when finance context loads
  React.useEffect(() => {
    prefetchPlatformData?.();
  }, [prefetchPlatformData]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      balance,
      transactions,
      isLoading,
      addDeposit,
      addWithdrawal,
      fetchTransactions,
      updateTransactionStatus,
      getTransactionById,
      platformNumber,
    }),
    [
      balance,
      transactions,
      isLoading,
      addDeposit,
      addWithdrawal,
      fetchTransactions,
      updateTransactionStatus,
      getTransactionById,
      platformNumber,
    ]
  );

  return (
    <FinanceContext.Provider value={contextValue}>
      {children}
    </FinanceContext.Provider>
  );
};

export const useFinance = (): FinanceContextType => {
  const context = useContext(FinanceContext);
  if (context === undefined) {
    throw new Error("useFinance must be used within a FinanceProvider");
  }
  return context;
};
