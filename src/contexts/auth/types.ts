import { UserRole, AdminPermission } from "@/types";

export interface UserProfile {
  id: string;
  legalDetails?: boolean;
  name: string;
  email: string;
  role: UserR<PERSON>;
  avatar?: string;
  balance: number;
  created_at: string;
  phone_number?: string;
  address?: string;
  bank_account?: string;
  legal_activity?: string;
  permissions?: AdminPermission[];
  active?: boolean;
  // Additional fields for Profile page
  companyName?: string;
  taxNumber?: string;
  commercialRegistration?: string;
  activity?: string;
  bankName?: string;
  accountNumber?: string;
  iban?: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
}

export interface AuthContextType {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  authError: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (password: string) => Promise<boolean>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<boolean>;
  sendVerificationEmail: (email: string) => Promise<boolean>;
  verifyOTP: (
    email: string,
    otp: string,
    type?: "register" | "login" | "recovery"
  ) => Promise<boolean>;
  completeRegistration: (userData: RegisterData) => Promise<boolean>;
  completeEmailVerification: (email: string, token: string) => Promise<boolean>;
  resendVerificationEmail: (email: string) => Promise<boolean>;
  sendConfirmationEmail: (
    email: string,
    type: "register" | "login"
  ) => Promise<boolean>;
  sendLoginOTP: (email: string) => Promise<boolean>;
  sendPasswordResetOTP: (email: string) => Promise<boolean>;
}

export interface AuthState {
  user: UserProfile | null;
  setUser: React.Dispatch<React.SetStateAction<UserProfile | null>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isAuthenticated: boolean;
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>;
  authError: string | null;
  setAuthError: React.Dispatch<React.SetStateAction<string | null>>;
}

// Re-export UserRole from types/index.ts
export type { UserRole };

// Add AuthResponse type
export interface AuthResponse {
  success: boolean;
  data?: unknown;
  error?: string;
}
