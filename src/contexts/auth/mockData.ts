
import { UserProfile } from './types';
import { UserRole } from '../../types';

// Mock users for testing
export const MOCK_USERS: Record<string, UserProfile> = {
  '1': {
    id: '1',
    name: 'متجر إلكتروني للتجربة',
    email: '<EMAIL>',
    role: 'ecommerce',
    avatar: 'https://ui-avatars.com/api/?name=متجر+إلكتروني&background=random',
    balance: 5000,
    created_at: '2023-01-15T12:00:00Z',
    phone_number: '**********',
    address: 'الرياض، السعودية',
    bank_account: '************************',
    active: true
  },
  '2': {
    id: '2',
    name: 'محل تجاري للتجربة',
    email: '<EMAIL>',
    role: 'store',
    avatar: 'https://ui-avatars.com/api/?name=محل+تجاري&background=random',
    balance: 2500,
    created_at: '2023-02-20T14:30:00Z',
    phone_number: '**********',
    address: 'جدة، السعودية',
    bank_account: '************************',
    active: true
  },
  '3': {
    id: '3',
    name: 'مدير النظام',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'https://ui-avatars.com/api/?name=مدير+النظام&background=random',
    balance: 10000,
    created_at: '2023-01-01T09:00:00Z',
    phone_number: '**********',
    address: 'الدمام، السعودية',
    bank_account: '************************',
    active: true
  },
  '4': {
    id: '4',
    name: 'متجر الأدوات المنزلية',
    email: '<EMAIL>',
    role: 'store',
    avatar: 'https://ui-avatars.com/api/?name=متجر+الأدوات+المنزلية&background=random',
    balance: 4000,
    created_at: '2023-03-10T15:45:00Z',
    phone_number: '**********',
    address: 'الخبر، السعودية',
    bank_account: '*********************',
    active: true
  },
  '5': {
    id: '5',
    name: 'متجر الإكسسوارات الفاخرة',
    email: '<EMAIL>',
    role: 'ecommerce',
    avatar: 'https://ui-avatars.com/api/?name=متجر+الإكسسوارات&background=random',
    balance: 7500,
    created_at: '2023-04-05T11:30:00Z',
    phone_number: '**********',
    address: 'المدينة المنورة، السعودية',
    bank_account: '*********************',
    active: true
  }
};
