import { User, UserRole, AdminPermission } from '@/types';
import { supabase } from '@/integrations/supabase/client';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  authError: string | null;
  setUser: (user: User | null) => void;
  setIsLoading: (loading: boolean) => void;
  setIsAuthenticated: (authenticated: boolean) => void;
  setAuthError: (error: string | null) => void;
}

export function useBasicAuthActions(authState: AuthState) {
  const { setUser, setIsLoading, setIsAuthenticated, setAuthError } = authState;

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setAuthError(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        setAuthError(error.message);
        return false;
      }
      
      if (data.user) {
        setUser(mapAuthToProfile(data.user));
        setIsAuthenticated(true);
        return true;
      } else {
        setAuthError('Invalid credentials');
        return false;
      }
    } catch (error: any) {
      setAuthError(error.message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string, role: UserRole = 'ecommerce'): Promise<boolean> => {
    setIsLoading(true);
    setAuthError(null);

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { 
            name, 
            role,
            active: true
          }
        }
      });

      if (error) {
        setAuthError(error.message);
        return false;
      }
      
      if (data.user) {
        setUser(mapAuthToProfile(data.user));
        setIsAuthenticated(true);
        return true;
      } else {
        setAuthError('Registration failed');
        return false;
      }
    } catch (error: any) {
      setAuthError(error.message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    setAuthError(null);

    try {
      await supabase.auth.signOut();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error: any) {
      setAuthError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string): Promise<boolean> => {
    setIsLoading(true);
    setAuthError(null);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      
      if (error) {
        setAuthError(error.message);
        return false;
      }
      
      return true;
    } catch (error: any) {
      setAuthError(error.message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return { login, register, logout, resetPassword };
}

function mapAuthToProfile(user: any): User {
  return {
    id: user.id,
    email: user.email || '',
    name: user.user_metadata?.name || user.email?.split('@')[0] || '',
    role: user.user_metadata?.role as UserRole || 'ecommerce',
    avatar: user.user_metadata?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.email || '')}&background=random`,
    balance: user.user_metadata?.balance || 0,
    created_at: user.created_at || new Date().toISOString(),
    phone_number: user.user_metadata?.phone_number || '',
    address: user.user_metadata?.address || '',
    bank_account: user.user_metadata?.bank_account || '',
    legal_activity: user.user_metadata?.legal_activity || '',
    permissions: user.user_metadata?.permissions as AdminPermission[] || [],
    active: user.user_metadata?.active !== undefined ? user.user_metadata?.active : true,
  };
}
