
/**
 * OTP (One-Time Password) verification service functions
 */

// Validate OTP format (6 digits)
export const isValidOTPFormat = (otp: string): boolean => {
  return /^\d{6}$/.test(otp);
};

// Generate random 6-digit OTP
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Validate OTP expiry
export const isOTPExpired = (expiresAt: string): boolean => {
  const expiryDate = new Date(expiresAt);
  const currentDate = new Date();
  return currentDate > expiryDate;
};

// Create expiry timestamp for OTP (default: 10 minutes from now)
export const createOTPExpiry = (minutes = 10): string => {
  const expiryDate = new Date();
  expiryDate.setMinutes(expiryDate.getMinutes() + minutes);
  return expiryDate.toISOString();
};

// Format a partially masked email for display
export const formatMaskedEmail = (email: string): string => {
  if (!email) return '';
  
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  
  const maskedUsername = username.length > 3 
    ? `${username.substring(0, 3)}${'*'.repeat(Math.min(username.length - 3, 5))}`
    : username;
    
  return `${maskedUsername}@${domain}`;
};
