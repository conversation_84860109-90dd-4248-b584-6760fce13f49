import React, { createContext, useEffect } from "react";
import { AuthContextType, RegisterData, UserProfile } from "./types";
import { supabase } from "@/integrations/supabase/client";
import { useAuthState } from "./useAuthState";
import { useAuthActions } from "./useAuthActions";
import { useAuthCompletionActions } from "./actions/useAuthCompletionActions";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Loading } from "@/components/ui/loading";

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const queryClient = useQueryClient();
  const authState = useAuthState();
  const authActions = useAuthActions();
  const authCompletionActions = useAuthCompletionActions();
  const navigate = useNavigate();

  // Use React Query for session management and user data
  const { data: sessionData } = useQuery({
    queryKey: ["auth", "session"],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      return session;
    },
    // Refresh every 10 minutes (Supabase tokens are usually valid for 1 hour)
    refetchInterval: 10 * 60 * 1000,
    staleTime: 5 * 60 * 1000, // Consider data stale after 5 minutes
  });

  const { data: profileData, isLoading: isProfileLoading } = useQuery({
    queryKey: ["auth", "profile", sessionData?.user?.id],
    queryFn: async () => {
      if (!sessionData?.user?.id) return null;

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", sessionData.user.id)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        // Return a profile constructed from user metadata as fallback
        return sessionData.user.user_metadata
          ? ({
              id: sessionData.user.id,
              email: sessionData.user.email || "",
              name: sessionData.user.user_metadata.name || "",
              role: sessionData.user.user_metadata.role || "user",
              ...sessionData.user.user_metadata,
            } as UserProfile)
          : null;
      }
      if (!data) {
        console.error("No profile found for user:", sessionData.user.id);
        return null;
      }
      const { data: isFinishedLegalData, error: isFinishedLegalError } =
        await supabase
          .from("legal_details")
          .select("*")
          .eq("profile_id", data.id)
          .single();

      return {
        ...data,
        legalDetails: !!isFinishedLegalData,
      } as UserProfile;
    },
    enabled: !!sessionData?.user?.id,
  });

  // Update auth state based on query results
  useEffect(() => {
    if (sessionData && profileData) {
      authState.setUser(profileData);
      authState.setIsAuthenticated(true);
    } else if (!sessionData) {
      authState.setUser(null);
      authState.setIsAuthenticated(false);
    }

    authState.setIsLoading(isProfileLoading);
  }, [sessionData, profileData, isProfileLoading, authState]);

  // Set up auth change listener
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event);

      // Invalidate and refetch queries when auth state changes
      if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        queryClient.invalidateQueries({ queryKey: ["auth", "session"] });
        if (session?.user?.id) {
          queryClient.invalidateQueries({
            queryKey: ["auth", "profile", session.user.id],
          });
        }
      } else if (event === "SIGNED_OUT") {
        queryClient.clear(); // Clear all queries on sign out
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);

  // React Query Mutations
  const loginMutation = useMutation({
    mutationFn: async ({
      email,
      password,
    }: {
      email: string;
      password: string;
    }) => {
      const result = await authActions.login({ email, password });
      if (typeof result === "boolean" && !result) {
        throw new Error(authState.authError || "Login failed");
      }
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth"] });
      authState.setAuthError(null);
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData: RegisterData) => {
      const result = await authActions.register({
        ...userData,
        email: userData.email,
        password: userData.password,
      });
      if (!result) {
        throw new Error(authState.authError || "Registration failed");
      }
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["auth"],
      });
      navigate("/login");
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      const controller = new AbortController();
      controller.abort(); // Abort the request to prevent memory leaks
      const { error } = await supabase.auth.signOut();
      if (error) throw new Error(error.message);
      return true;
    },
    onSuccess: () => {
      toast.success("Logout successful", {
        description: "See you later!",
      });
      queryClient.clear(); // Clear all queries on logout
      authState.setUser(null);
      authState.setIsAuthenticated(false);
      navigate("/login");
    },
  });

  const resetPasswordMutation = useMutation({
    mutationFn: authActions.resetPassword.mutateAsync,
  });

  const updateProfileMutation = useMutation({
    mutationFn: authActions.updateProfile.mutateAsync,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["auth"],
      });
      authState.setUser({
        ...authState.user,
      });
      authState.setIsAuthenticated(true);
      authState.setAuthError(null);
    },
  });

  const sendVerificationEmailMutation = useMutation({
    mutationFn: authCompletionActions.resendVerificationEmail.mutateAsync,
  });

  const sendLoginOTPMutation = useMutation({
    mutationFn: authCompletionActions.sendLoginOTP.mutateAsync,
  });

  const sendPasswordResetOTPMutation = useMutation({
    mutationFn: authCompletionActions.sendPasswordResetOTP.mutateAsync,
  });

  const verifyOTPMutation = useMutation({
    mutationFn: async ({
      email,
      otp,
      type,
    }: {
      email: string;
      otp: string;
      type: "register" | "login" | "recovery";
    }) => {
      const { data, error } = await supabase
        .from("otps")
        .select("*")
        .eq("email", email)
        .eq("type", type)
        .eq("otp", otp)
        .single();

      if (error || !data) {
        throw new Error("Invalid OTP");
      }

      const expiresAt = new Date(data.expires_at);
      if (new Date() > expiresAt) {
        throw new Error("OTP expired");
      }

      await supabase.from("otps").delete().eq("id", data.id);

      if (type === "login") {
        const { error: signInError } = await supabase.auth.signInWithOtp({
          email,
        });
        if (signInError) {
          throw new Error("Login failed");
        }
      }

      return true;
    },
  });

  const updatePasswordMutation = useMutation({
    mutationFn: async (password: string) => {
      const { error } = await supabase.auth.updateUser({ password });
      if (error) {
        throw new Error(error.message);
      }
      return true;
    },
  });

  const value: AuthContextType = {
    user: authState.user,
    isLoading: authState.isLoading,
    isAuthenticated: authState.isAuthenticated,
    authError: authState.authError,

    // Basic auth actions
    login: async (email, password) => {
      const result = await loginMutation.mutateAsync({ email, password });
      return !!result;
    },
    register: async (userData) => {
      const result = await registerMutation.mutateAsync(userData);
      return !!result;
    },
    logout: async () => {
      await logoutMutation.mutateAsync();
    },
    resetPassword: async (email) => {
      await resetPasswordMutation.mutateAsync(email);
      return true;
    },
    updatePassword: async (password) => {
      await updatePasswordMutation.mutateAsync(password);
      return true;
    },
    updateProfile: async (updates: UserProfile) => {
      const result = await updateProfileMutation.mutateAsync({
        ...updates,
      });
      return !!result;
    },

    // Verification actions
    sendVerificationEmail: async (email) => {
      await sendVerificationEmailMutation.mutateAsync(email);
      return true;
    },
    verifyOTP: async (email, otp, type = "recovery") => {
      const result = await verifyOTPMutation.mutateAsync({ email, otp, type });
      return !!result;
    },
    resendVerificationEmail: async (email) => {
      await sendVerificationEmailMutation.mutateAsync(email);
      return true;
    },
    sendConfirmationEmail: async (email) => {
      // Implement the sendConfirmationEmail logic here
      return true;
    },

    // OTP specific actions
    sendLoginOTP: async (email) => {
      await sendLoginOTPMutation.mutateAsync(email);
      return true;
    },
    sendPasswordResetOTP: async (email) => {
      await sendPasswordResetOTPMutation.mutateAsync(email);
      return true;
    },

    // Additional required actions
    completeRegistration: async (data) => {
      // Implement the completeRegistration logic here
      return true;
    },
    completeEmailVerification: async (email) => {
      // Implement the completeEmailVerification logic here
      return true;
    },
  };
  if (isProfileLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Loading />
      </div>
    );
  }
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
