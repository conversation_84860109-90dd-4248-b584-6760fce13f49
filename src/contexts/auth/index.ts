
// Export all auth related components and hooks
import { AuthContext, AuthProvider } from './AuthProvider';
import { useAuth } from './useAuth';
import type { UserProfile, AuthContextType, AuthState } from './types';
// Import UserRole and AdminPermission from the main types
import type { UserRole, AdminPermission } from '@/types';

// Unified exports for better imports throughout the app
export { 
  AuthContext, 
  useAuth, 
  AuthProvider
};

export type {
  UserRole,
  AdminPermission,
  UserProfile,
  AuthContextType,
  AuthState
};

// Re-export toast from the correct location
export { toast } from '@/hooks/toast';
