import { useLoginAction } from "./actions/loginAction";
import { useRegisterAction } from "./actions/registerAction";
import { useBasicAuthActions } from "./actions/basicAuthActions";

export function useAuthActions() {
  const loginAction = useLoginAction();
  const registerAction = useRegisterAction();
  const basicAuthActions = useBasicAuthActions();

  return {
    login: loginAction.login.mutateAsync,
    register: registerAction.register.mutateAsync,
    ...basicAuthActions,
  };
}
