import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Loader2, Check, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/hooks/toast';
import { useNavigate } from 'react-router-dom';

const resetPasswordSchema = z.object({
  password: z.string()
    .min(8, { message: 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل' })
    .regex(/[A-Z]/, { message: 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل' })
    .regex(/[0-9]/, { message: 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل' }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'كلمات المرور غير متطابقة',
  path: ['confirmPassword'],
});

export default function SetNewPasswordForm() {
  const [isResetting, setIsResetting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { updatePassword, authError } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate()

  const onBackToLogin = () => navigate('/login');

  const resetForm = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmitNewPassword = async (values: z.infer<typeof resetPasswordSchema>) => {
    setIsResetting(true);
    setError(null);
    
    try {
      console.log("Attempting to set new password");
      const success = await updatePassword(values.password);
      
      if (success) {
        console.log("Password reset successfully");
        setIsSuccess(true);
        resetForm.reset();
        toast({
          title: "تم إعادة تعيين كلمة المرور بنجاح",
          description: "يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة",
        });
      } else {
        // Display the specific error from auth context
        console.error("Password reset failed:", authError);
        setError(authError || "فشل إعادة تعيين كلمة المرور");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      setError(error.message || "حدث خطأ أثناء محاولة إعادة تعيين كلمة المرور");
    } finally {
      setIsResetting(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="space-y-4 text-center">
        <div className="rounded-full bg-green-50 p-3 w-20 h-20 mx-auto flex items-center justify-center">
          <Check className="h-10 w-10 text-green-500" />
        </div>
        <h2 className="text-xl font-semibold">تم إعادة تعيين كلمة المرور</h2>
        <p className="text-muted-foreground">
          تم تغيير كلمة المرور الخاصة بك بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.
        </p>
        <Button 
          variant="outline" 
          className="w-full mt-4"
          onClick={onBackToLogin}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          العودة إلى تسجيل الدخول
        </Button>
      </div>
    );
  }

  return (
    <>
      <h2 className="text-xl font-semibold mb-4 text-center">إعادة تعيين كلمة المرور</h2>
      <p className="text-muted-foreground mb-6 text-center">
        يرجى إدخال كلمة المرور الجديدة الخاصة بك
      </p>
      
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <Form {...resetForm}>
        <form onSubmit={resetForm.handleSubmit(onSubmitNewPassword)} className="space-y-4">
          <FormField
            control={resetForm.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>كلمة المرور الجديدة</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="********"
                    {...field}
                    autoComplete="new-password"
                    disabled={isResetting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={resetForm.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>تأكيد كلمة المرور</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="********"
                    {...field}
                    autoComplete="new-password"
                    disabled={isResetting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col space-y-2">
            <Button type="submit" className="w-full" disabled={isResetting}>
              {isResetting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري المعالجة...
                </>
              ) : (
                'إعادة تعيين كلمة المرور'
              )}
            </Button>
            
            <Button 
              type="button" 
              variant="ghost" 
              className="w-full" 
              onClick={onBackToLogin} 
              disabled={isResetting}
            >
              العودة إلى تسجيل الدخول
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}