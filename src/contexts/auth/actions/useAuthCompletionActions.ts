import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

export function useAuthCompletionActions() {
  const resendVerificationEmail = useMutation({
    mutationFn: async (email: string) => {
      const { error } = await supabase.auth.resend({ type: "signup", email });
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("تم إعادة إرسال رابط التحقق", {
        description: "تم إرسال رابط التحقق إلى بريدك الإلكتروني",
      });
    },
    onError: (error: Error) => {
      toast.error("خطأ في إعادة إرسال رابط التحقق", {
        description: error.message,
      });
    },
  });

  const sendLoginOTP = useMutation({
    mutationFn: async (email: string) => {
      const { error } = await supabase.functions.invoke("send-otp-email", {
        body: {
          email,
          type: "login",
          otp: Math.floor(100000 + Math.random() * 900000).toString(),
        },
      });
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("تم إرسال رمز التحقق", {
        description: "تم إرسال رمز التحقق إلى بريدك الإلكتروني",
      });
    },
    onError: (error: Error) => {
      toast.error("خطأ في إرسال رمز التحقق", {
        description: error.message,
      });
    },
  });

  const sendPasswordResetOTP = useMutation({
    mutationFn: async (email: string) => {
      const { error } = await supabase.functions.invoke("send-otp-email", {
        body: {
          email,
          type: "recovery",
          otp: Math.floor(100000 + Math.random() * 900000).toString(),
        },
      });
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("تم إرسال رمز التحقق", {
        description: "تم إرسال رمز التحقق إلى بريدك الإلكتروني",
      });
    },
    onError: (error: Error) => {
      toast.error("خطأ في إرسال رمز التحقق", {
        description: error.message,
      });
    },
  });

  return {
    resendVerificationEmail,
    sendLoginOTP,
    sendPasswordResetOTP,
  };
}
