import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { UserProfile } from "../types";
import { useAuth } from "../useAuth";

export function useBasicAuthActions() {
  const logout = useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("Logout Successful", { description: "See you later!" });
    },
    onError: (error: Error) => {
      toast.error("Logout Failed", { description: error.message });
    },
  });

  const resetPassword = useMutation({
    mutationFn: async (email: string) => {
      const resetUrl = `${window.location.origin}/reset-password`;
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: resetUrl,
      });
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("تم إرسال رابط إعادة تعيين كلمة المرور", {
        description:
          "يرجى التحقق من بريدك الإلكتروني واتباع التعليمات لإعادة تعيين كلمة المرور الخاصة بك.",
      });
    },
    onError: (error: Error) => {
      toast.error("فشل في إرسال رابط إعادة تعيين كلمة المرور", {
        description: error.message,
      });
    },
  });

  const resendVerificationEmail = useMutation({
    mutationFn: async (email: string) => {
      const timestamp = new Date().getTime();
      const { error } = await supabase.auth.resend({
        type: "signup",
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/verification-pending?type=register&email=${email}&t=${timestamp}`,
        },
      });
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("Verification email resent", {
        description:
          "Please check your inbox and follow the instructions to verify your email.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to resend verification email", {
        description: error.message,
      });
    },
  });

  const updateProfile = useMutation({
    mutationFn: async (updates: UserProfile) => {
      console.log(updates);

      const { data, error } = await supabase
        .from("profiles")
        .update({
          ...updates,
        })
        .eq("id", updates.id)
        .select();
      if (error) {
        throw new Error(error.message);
      }
      return data[0];
    },
    onSuccess: () => {
      toast.success("Profile updated", {
        description: "Your profile has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to update profile", { description: error.message });
    },
  });

  return {
    logout,
    resetPassword,
    resendVerificationEmail,
    updateProfile,
  };
}
