import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

export function useRegisterAction() {
  const register = useMutation({
    mutationFn: async ({
      email,
      password,
      name,
      role,
    }: {
      email: string;
      password: string;
      name: string;
      role: string;
    }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role,
            balance: 0,
            permissions:
              role === "admin" ? ["manage_users", "approve_payments"] : [],
            active: true,
          },
          emailRedirectTo: `${window.location.origin}/dashboard`,
        },
      });
      if (error) {
        throw new Error(error.message);
      }
      return data.user;
    },
    onSuccess: () => {
      toast.success("تم التسجيل بنجاح", {
        description: "تم إرسال رابط التحقق إلى بريدك الإلكتروني",
      });
    },
    onError: (error: Error) => {
      toast.error("خطأ في التسجيل", { description: error.message });
    },
  });

  return { register };
}
