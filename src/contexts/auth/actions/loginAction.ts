import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { useAuthState } from "../useAuthState";

export function useLoginAction() {
  const authState = useAuthState();

  const login = useMutation({
    mutationFn: async ({
      email,
      password,
    }: {
      email: string;
      password: string;
    }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      // Get profile data
      if (data.user) {
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", data.user.id)
          .single();

        if (profileError) {
          console.error("Error fetching profile:", profileError);
        } else if (profileData) {
          authState.setUser(profileData);
          authState.setIsAuthenticated(true);
        }
      }

      return data.user;
    },
    onSuccess: (data) => {
      toast.success("Login successful");
    },
    onError: (error: Error) => {
      if (error.message.includes("Email not confirmed")) {
        throw new Error("email_not_verified");
      }
      toast.error("Login error", { description: error.message });
    },
  });

  return { login };
}
