
import { UserRole, AdminPermission } from '@/types';
import { RegisterData, AuthResponse } from './types';
import { supabase } from '@/integrations/supabase/client';

// Fix the customer role issue by using the proper UserRole type
export async function registerUser(userData: RegisterData): Promise<AuthResponse> {
  try {
    const { name, email, password, role } = userData;
    
    // Ensure the role is one of the allowed UserRole types
    const userRole = role as UserRole; // This will ensure we only use valid UserRole values
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role: userRole,
        }
      }
    });

    // Check for errors during signup
    if (error) {
      console.error("Error during signup:", error.message);
      return { success: false, error: error.message };
    }

    // If signup is successful, return success
    return { success: true, data: data };
  }
  catch (error) {
    console.error("Error during signup:", error);
    return { success: false, error: "An unexpected error occurred during signup." };
  }
}
