import { useEffect } from "react";
import { AuthState } from "./types";
import { supabase } from "@/integrations/supabase/client";
import { UserRole, AdminPermission } from "@/types";
import { toast } from "sonner";

export function useSessionManager(authState: AuthState) {
  const { setUser, setIsAuthenticated, setIsLoading, setAuthError } = authState;

  // Fetch user profile from Supabase 'profiles' table
  const fetchUserProfile = async (userId: string) => {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    const { data: isFinishedLegalData, error: isFinishedLegalError } =
      await supabase
        .from("legal_details")
        .select("*")
        .eq("profile_id", data.id)
        .single();

    return {
      ...data,
      legalDetails: !!isFinishedLegalData,
    };
  };

  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      try {
        // Check if there's an existing session
        const {
          data: { session },
        } = await supabase.auth.getSession();
        // Set up auth state listener FIRST to prevent missing auth events
        const {
          data: { subscription },
        } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
          if (currentSession) {
            try {
              const userData = await fetchUserProfile(currentSession.user.id);
              if (userData) {
                setUser({
                  id: currentSession.user.id,
                  email: currentSession.user.email || "",
                  name: userData.name || "",
                  role: (userData.role as UserRole) || "ecommerce",
                  phone_number: userData.phone_number || "",
                  address: userData.address || "",
                  bank_account: userData.bank_account || "",
                  avatar:
                    userData.avatar ||
                    `https://ui-avatars.com/api/?name=${encodeURIComponent(
                      userData.name || ""
                    )}&background=random`,
                  balance: userData.balance || 0,
                  created_at:
                    currentSession.user.created_at || new Date().toISOString(),
                  legal_activity: userData.legal_activity || "",
                  permissions:
                    (userData.permissions as AdminPermission[]) || [],
                  active:
                    userData.active !== undefined ? userData.active : true,
                  legalDetails: userData.legalDetails || false,
                });
                setIsAuthenticated(true);
              } else {
                console.error("No user profile found after auth change");
                setIsAuthenticated(false);
                setUser(null);
              }
            } catch (error) {
              console.error(
                "Error processing profile after auth change:",
                error
              );
              setIsAuthenticated(false);
              setUser(null);
            }
          } else {
            setIsAuthenticated(false);
            setUser(null);
          }

          setIsLoading(false);
        });

        // Try to load initial session if it exists
        if (session) {
          try {
            const userData = await fetchUserProfile(session.user.id);
            if (userData) {
              setUser({
                id: session.user.id,
                email: session.user.email || "",
                name: userData.name || "",
                role: (userData.role as UserRole) || "ecommerce",
                phone_number: userData.phone_number || "",
                address: userData.address || "",
                bank_account: userData.bank_account || "",
                avatar:
                  userData.avatar ||
                  `https://ui-avatars.com/api/?name=${encodeURIComponent(
                    userData.name || ""
                  )}&background=random`,
                balance: userData.balance || 0,
                created_at: session.user.created_at || new Date().toISOString(),
                legal_activity: userData.legal_activity || "",
                permissions: (userData.permissions as AdminPermission[]) || [],
                active: userData.active !== undefined ? userData.active : true,
              });
              setIsAuthenticated(true);
              toast.success("مرحباً بعودتك!");
            } else {
              console.error("No user profile found for session");
              setIsAuthenticated(false);
              setUser(null);
            }
          } catch (error) {
            console.error("Error loading profile data:", error);
            setIsAuthenticated(false);
            setUser(null);
          }
        }

        // Cleanup
        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error("Error in session manager:", error);
        setAuthError("فشل في تهيئة عملية تسجيل الدخول");
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [setUser, setIsAuthenticated, setIsLoading, setAuthError]);
}
