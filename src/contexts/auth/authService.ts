
import { supabase } from '@/integrations/supabase/client';
import { UserProfile } from './types';

/**
 * Check if a user with the given email exists in the profiles table
 */
export async function emailExists(email: string): Promise<boolean> {
  try {
    // First check if the email exists in profiles table
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .limit(1);

    if (profileError) {
      console.error('Error checking email existence in profiles:', profileError);
      // Don't return false here yet, as we still need to check auth.users
    }

    // If profile exists with this email
    if (profileData && profileData.length > 0) {
      return true;
    }

    // As a fallback, try to sign in with a fake password to see if the email exists
    // This will return an error, but the error message will tell us if the email exists
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password: 'check-if-email-exists-only', // This will always fail, but the error type will tell us if the email exists
    });

    // If the error message indicates invalid credentials but not invalid email,
    // that means the email exists but password was wrong
    if (error && error.message && (
        error.message.includes('Invalid login credentials') || 
        error.message.includes('كلمة المرور') ||
        error.message.includes('password')
      )) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error in emailExists:', error);
    // In case of an error, return false to allow the user to try signing up
    // Better UX to let them try than block them due to an error
    return false;
  }
}

/**
 * Map Supabase auth user data to a UserProfile object
 */

export function mapAuthToProfile(user): UserProfile {
  return {
    id: user.id,
    name: user.user_metadata.name || user.email.split('@')[0],
    email: user.email,
    role: user.user_metadata.role || 'customer',
    avatar: user.user_metadata.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.user_metadata.name || user.email.split('@')[0])}&background=random`,
    balance: user.user_metadata.balance || 0,
    created_at: user.created_at,
    phone_number: user.user_metadata.phone_number || '',
    address: user.user_metadata.address || '',
    bank_account: user.user_metadata.bank_account || '',
    legal_activity: user.user_metadata.legal_activity || '',
    permissions: user.user_metadata.permissions || [],
    active: user.user_metadata.active !== undefined ? user.user_metadata.active : true,
  };
}

/**
 * Map a profile database record to a UserProfile object
 */
export function mapProfileToUser(profile): UserProfile {
  return {
    id: profile.id,
    name: profile.name,
    email: profile.email,
    role: profile.role,
    balance: profile.balance || 0,
    created_at: profile.created_at,
    phone_number: profile.phone_number || '',
    address: profile.address || '',
    bank_account: profile.bank_account || '',
    avatar: profile.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.name)}&background=random`,
    legal_activity: profile.legal_activity || '',
    permissions: profile.permissions || [],
    active: profile.active !== undefined ? profile.active : true,
  };
}
