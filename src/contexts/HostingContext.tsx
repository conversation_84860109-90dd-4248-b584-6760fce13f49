import React, { createContext, ReactNode, useContext } from "react";
import { HostingRequest, OrderStatus } from "@/types";
import { useHostingRequestsQuery } from "@/hooks/query/useHostingRequestsQuery";
import { useCreateHostingRequestQuery } from "@/hooks/query/useCreateHostingRequestQuery";
import { toast } from "sonner";

interface HostingContextProps {
  hostingRequests: HostingRequest[];
  isLoading: boolean;
  getRequestById: (id: string) => HostingRequest | undefined;
  updateHostingRequestStatus: (id: string, status: OrderStatus) => void;
  addHostingRequest: (request: Partial<HostingRequest>) => void;
}

const HostingContext = createContext<HostingContextProps | undefined>(
  undefined
);

export const HostingProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const {
    hostingRequests,
    isLoading: queryLoading,
    updateHostingRequestStatus,
    getRequestById,
  } = useHostingRequestsQuery();

  const {
    createHostingRequest: createRequestMutation,
    isSubmitting: isMutating,
  } = useCreateHostingRequestQuery();

  const addHostingRequest = (request: Partial<HostingRequest>) => {
    const toastId = toast.loading("جاري إضافة طلب عرض منتجات جديد...");
    try {
      const newRequest: HostingRequest = {
        ecommerce_id: request.ecommerce_id || "",
        store_id: request.store_id || "",
        ecommerce_name: request.ecommerce_name || "",
        store_name: request.store_name || "",
        status: "pending",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        notes: request.notes,
        ecommerce_legal_activity: request.ecommerce_legal_activity,
        subscription_type: request.subscription_type,
        description: request.description,
        duration: request.duration,
      };

      // Using mutation to create request
      createRequestMutation({
        request: newRequest,
        products: request.products || [],
      })
        .then(() => {
          toast.success("تم إضافة طلب عرض منتجات جديد بنجاح", {
            id: toastId,
          });
        })
        .catch((error) => {
          console.error("Error adding hosting request:", error);
          toast.error("فشل في إضافة طلب عرض منتجات جديد", {
            id: toastId,
          });
        });
    } catch (error) {
      console.error("Error adding hosting request:", error);
      toast.error("فشل في إضافة طلب عرض منتجات جديد", {
        id: toastId,
      });
    }
  };

  const value: HostingContextProps = {
    hostingRequests,
    isLoading: queryLoading || isMutating,
    getRequestById,
    updateHostingRequestStatus,
    addHostingRequest,
  };

  return (
    <HostingContext.Provider value={value}>{children}</HostingContext.Provider>
  );
};

export const useHosting = () => {
  const context = useContext(HostingContext);
  if (!context) {
    throw new Error("useHosting must be used within a HostingProvider");
  }
  return context;
};
