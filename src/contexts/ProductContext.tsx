import React, { createContext, ReactNode, useContext } from "react";
import { Product, ProductStatus } from "@/types";
import { useProductsQuery } from "@/hooks/query/useProductsQuery";

interface ProductContextType {
  products: Product[];
  userProducts: Product[];
  isLoading: boolean;
  error: string;
  loadProducts: (filters?: {
    userId?: string;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    status?: string;
  }) => Promise<Product[]>;
  addProduct: (params: {
    productData: {
      name: string;
      price: number;
      description: string;
      category: string;
      quantity: number;
      user_id?: string;
      seller_id?: string;
      seller_name?: string;
      status?: ProductStatus;
    };
    imageFiles?: File[];
  }) => Promise<Product | null>;
  updateProduct: (
    id: string,
    productData: Partial<Product>,
    imageFiles?: File[]
  ) => Promise<Product | null>;
  deleteProduct: (id: string) => Promise<boolean>;
  getProductById: (id: string) => Promise<Product | null>;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export function ProductProvider({ children }: { children: ReactNode }) {
  const {
    products,
    isLoading,
    error,
    addProduct: addProductMutation,
    updateProduct: updateProductMutation,
    deleteProduct: deleteProductMutation,
    getProductById,
  } = useProductsQuery();

  const loadProducts = async (filters?: {
    userId?: string;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    status?: string;
  }): Promise<Product[]> => {
    // Implementation uses React Query under the hood
    // Kept for backward compatibility
    return products;
  };

  const addProduct = async ({
    productData,
    imageFiles = [],
  }: {
    productData: {
      name: string;
      price: number;
      description: string;
      category: string;
      quantity: number;
      user_id?: string;
      seller_id?: string;
      seller_name?: string;
      status?: ProductStatus;
    };
    imageFiles?: File[];
  }): Promise<Product | null> => {
    try {
      return await addProductMutation({
        ...productData,
        imageFiles,
      });
    } catch (error) {
      console.error("Error adding product:", error);
      return null;
    }
  };

  const updateProduct = async (
    id: string,
    productData: Partial<Product>,
    imageFiles: File[] = []
  ): Promise<Product | null> => {
    try {
      return await updateProductMutation({ id, data: productData, imageFiles });
    } catch (error) {
      console.error("Error updating product:", error);
      return null;
    }
  };

  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      await deleteProductMutation(id);
      return true;
    } catch (error) {
      console.error("Error deleting product:", error);
      return false;
    }
  };

  const getProductByIdAsync = async (id: string): Promise<Product | null> => {
    return getProductById(id);
  };

  return (
    <ProductContext.Provider
      value={{
        products,
        userProducts: products,
        isLoading,
        error: error || "",
        loadProducts,
        addProduct,
        updateProduct,
        deleteProduct,
        getProductById: getProductByIdAsync,
      }}
    >
      {children}
    </ProductContext.Provider>
  );
}

export function useProducts() {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error("useProducts must be used within a ProductProvider");
  }
  return context;
}
