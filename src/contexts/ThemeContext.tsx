
import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode; defaultTheme?: Theme; storageKey?: string }> = ({ 
  children, 
  defaultTheme = 'light', 
  storageKey = 'theme' 
}) => {
  const [theme, setTheme] = useState<Theme>(() => {
    // محاولة الحصول على السمة من التخزين المحلي
    if (typeof window !== "object") {
      return defaultTheme;
    }
    const savedTheme = localStorage.getItem(storageKey) as Theme | null;
    return savedTheme || defaultTheme;
  });

  useEffect(() => {
    // تحديث التخزين المحلي عند تغيير السمة
    if (typeof window !== "object") {
      return;
    }
    localStorage.setItem(storageKey, theme);

    // تحديث الفئة على عنصر html
    const root = window.document.documentElement;
    
    // إزالة أي فئات موضوع موجودة
    root.classList.remove('light', 'dark');

    // تطبيق الموضوع المحدد أو تفضيل النظام
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }
  }, [theme, storageKey]);

  // الاستماع لتغييرات تفضيلات النظام إذا كان يستخدم موضوع النظام
  useEffect(() => {
    if (theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(mediaQuery.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('يجب استخدام useTheme داخل ThemeProvider');
  }
  return context;
};
