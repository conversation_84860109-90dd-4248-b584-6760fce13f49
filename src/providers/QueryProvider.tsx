import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client with optimized default settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      // Increase stale time to reduce unnecessary refetches
      staleTime: 1000 * 60 * 10, // 10 minutes
      // Keep cached data longer
      gcTime: 1000 * 60 * 60, // 1 hour
      retry: (failureCount, error) => {
        // Limit retries based on error type
        const err = error as Error;
        if (err.message?.includes('404') || err.message?.includes('403')) {
          return false; // Don't retry client errors
        }
        return failureCount < 2; // Only retry twice for other errors
      },
    },
  },
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const isDevelopment = import.meta.env.DEV;

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {isDevelopment && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}
