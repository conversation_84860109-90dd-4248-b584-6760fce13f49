// User related types
export interface User {
  id: string;
  name?: string;
  email?: string;
  role?: UserRole;
  phone_number?: string;
  address?: string;
  balance?: number;
  created_at?: string;
  updated_at?: string;
  permissions?: AdminPermission[];
  avatar?: string;
  active?: boolean;
  legal_activity?: string;
  bank_account?: string;
  iban?: string;
}

export type UserRole =
  | "admin"
  | "user"
  | "store"
  | "ecommerce"
  | "employee"
  | "sub-admin";

export type AdminPermission =
  | "all"
  | "users"
  | "products"
  | "orders"
  | "stores"
  | "finance"
  | "manage_users"
  | "approve_payments"
  | "manage_transactions"
  | "manage_admins"
  | "manage_stores"
  | "read:users"
  | "read:products";
