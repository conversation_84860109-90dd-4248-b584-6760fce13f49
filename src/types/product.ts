
// Product related types
export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  category?: string;
  quantity: number;
  images?: string[];
  user_id?: string;
  seller_id: string;
  seller_name: string;
  status?: ProductStatus;
  created_at: string;
  updated_at: string;
  store_name?: string;
  stock?: number;
  active?: boolean;
}

export type ProductStatus = 'active' | 'inactive' | 'archived' | 'out_of_stock' | 'approved' | 'rejected' | 'pending' | 'deleted';
