
export interface RetailStoreDetails {
  id?: string;
  user_id: string;
  store_name?: string;
  commercial_registration?: string;
  tax_number?: string;
  address?: string;
  city?: string;
  phone?: string;
  email?: string;
  created_at?: string;
  updated_at?: string;
  documents?: any[];
  

  // Old fields for backwards compatibility
  legalName?: string;
  registrationNumber?: string;
  taxNumber?: string;
  contactPhone?: string;
  contact_phone?: string; // Adding this for compatibility
  contact_email?: string; // Adding this for compatibility
  bankAccount?: string;
  legalActivity?: string;
  
  // Documents
  commercialRegistry?: any;
  taxCertificate?: any;
  nationalId?: any;
}
