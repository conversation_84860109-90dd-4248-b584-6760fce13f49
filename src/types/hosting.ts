// Hosting related types
export interface HostingRequest {
  id?: string;
  ecommerce_id: string;
  ecommerce_name: string;
  store_id: string;
  store_name: string;
  status: import("./order").OrderStatus;
  created_at: string;
  updated_at: string;
  notes?: string;
  products?: HostingProduct[];
  ecommerce_legal_activity?: string;
  contract_id?: string;
  expires_at?: string;
  shipping_confirmed?: boolean;
  receipt_confirmed?: boolean;
  subscription_type?: string;
  description?: string;
  duration?: number; // Duration property for subscription length
}

export interface HostingProduct {
  id?: string;
  hosting_request_id?: string;
  product_id: string;
  product_name: string;
  price: number;
  quantity: number;
  image?: string;
  name?: string; // Name property
}
