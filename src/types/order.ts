// Order related types
export interface Order {
  id: string;
  user_id: string;
  status: OrderStatus;
  total: number;
  created_at: string;
  updated_at: string;
  items?: OrderItem[];
  customer?: string;
  customer_name?: string;
  date?: string;
  phone_number?: string;
  address?: string;
  payment_method?: string;
  tracking_number?: string;
  customer_id?: string;
  total_amount?: number;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product_name?: string;
  total?: number;
}

export type OrderStatus = 
  | 'pending'
  | 'accepted'
  | 'rejected'
  | 'ready'
  | 'delivered'
  | 'cancelled'
  | 'awaiting_shipping'
  | 'on_sale'
  | 'expired';
