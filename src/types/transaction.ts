
// Transaction related types
export interface Transaction {
  id: string;
  user_id: string;
  from_user_id?: string;
  to_user_id?: string;
  user_name?: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  reference?: string;
  created_at: string;
  updated_at: string;
  description?: string;
  receipt_url?: string;
  related_order_id?: string;
}

export type TransactionType =
  | "deposit"
  | "withdrawal"
  | "withdraw"
  | "transfer"
  | "payment"
  | "refund"
  | "commission"
  | "fee";
export type TransactionStatus =
  | "pending"
  | "completed"
  | "failed"
  | "cancelled"
  | "approved"
  | "rejected"
  | "processing";
