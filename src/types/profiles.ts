
import { UserRole, AdminPermission } from './index';

/**
 * Type definition for profile table operations
 * This matches exactly what's in the database schema
 */
export interface ProfileRecord {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  phone_number?: string;
  address?: string;
  balance?: number;
  created_at?: string;
  avatar?: string;
  bank_account?: string;
  legal_activity?: string;
  active?: boolean;
  permissions?: AdminPermission[];
}
