// Employee types
export interface StoreEmployee {
  id: string;
  name: string;
  email: string;
  phone_number: string; // Standardized to phone_number, required as per DB schema
  position: string;
  store_id: string; // Required as per DB schema
  branch_id?: string;
  user_id?: string; // Made optional since it's not in DB schema
  created_at: string;
  updated_at: string;
  role?: string; // Kept optional since it's not in DB schema
  active?: boolean;
  permissions?: string[];
  branch_name?: string; // Kept optional since it's not in DB schema
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  phone_number: string; // Updated to match StoreEmployee
  position: string;
  store_id?: string; // Added to match database structure
  branch_id?: string;
  user_id?: string; // Made optional to be consistent with StoreEmployee
}
