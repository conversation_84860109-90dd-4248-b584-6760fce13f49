// Onboarding and Welcome Screen types
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  component: string;
  completed: boolean;
  required: boolean;
  order: number;
  userTypes: ('store' | 'ecommerce')[];
}

export interface OnboardingProgress {
  id?: string;
  user_id: string;
  current_step: number;
  completed_steps: string[];
  total_steps: number;
  is_completed: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ProfileCompletionData {
  personal_info?: {
    name?: string;
    phone?: string;
    avatar?: string;
    email?: string;
  };
  business_info?: {
    business_name?: string;
    business_type?: string;
    description?: string;
    website?: string;
    social_media?: Record<string, string>;
    industry?: string;
    established_year?: number;
  };
  location_info?: {
    country?: string;
    city?: string;
    address?: string;
    postal_code?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  preferences?: {
    language?: string;
    currency?: string;
    notifications?: Record<string, boolean>;
    theme?: 'light' | 'dark';
  };
  verification?: {
    phone_verified?: boolean;
    email_verified?: boolean;
    business_verified?: boolean;
    documents_uploaded?: boolean;
  };
}

export interface WelcomeScreenData {
  user_type: 'store' | 'ecommerce';
  welcome_message: string;
  subtitle: string;
  features: Array<{
    title: string;
    description: string;
    icon: string;
    color?: string;
  }>;
  next_steps: Array<{
    title: string;
    description: string;
    action: string;
    icon: string;
  }>;
  statistics?: {
    total_users: number;
    total_stores: number;
    success_rate: string;
  };
}

// Survey related types (moved from survey.ts)
export interface SurveyResponse {
  id?: string;
  user_id: string;
  user_type: 'store' | 'ecommerce';
  responses: Record<string, any>;
  created_at?: string;
  completed?: boolean;
}

export interface SurveyQuestion {
  id: string;
  text: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'number' | 'file' | 'date';
  options?: { value: string; label: string; icon?: string }[];
  required: boolean;
  userType: ('store' | 'ecommerce')[];
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
    fileTypes?: string[];
    maxFileSize?: number;
  };
  conditional?: {
    dependsOn: string;
    showWhen: string | string[];
  };
}

// Step completion tracking
export interface StepCompletion {
  step_id: string;
  completed_at: string;
  data?: Record<string, any>;
}

// Onboarding configuration
export interface OnboardingConfig {
  steps: OnboardingStep[];
  welcome_screen: WelcomeScreenData;
  completion_rewards?: {
    points?: number;
    badges?: string[];
    features_unlocked?: string[];
  };
}

// User onboarding state
export interface UserOnboardingState {
  has_seen_welcome: boolean;
  current_step_index: number;
  completed_steps: string[];
  profile_completion_percentage: number;
  is_onboarding_completed: boolean;
  skipped_steps: string[];
  last_activity: string;
}
