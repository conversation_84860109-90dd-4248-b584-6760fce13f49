import React from "react";
import { DashboardStat } from "@/components/ui/dashboard-stat";
import {
  Store,
  ShoppingBag,
  CircleDollarSign,
  Users,
  Layers,
  BarChart3,
  Building,
} from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { UserRole } from "@/types";

// مؤشرات الأداء حسب نوع المستخدم
interface DashboardStatsProps {
  userCounts?: {
    users?: number;
    products?: number;
    transactions?: number;
    stores?: number;
    branches?: number;
    orders?: number;
    revenue?: number;
  };
  isLoading?: boolean;
}

export const UserRoleDashboardStats: React.FC<DashboardStatsProps> = ({
  userCounts = {},
  isLoading = false,
}) => {
  const { user } = useAuth();
  const userRole = user?.role as UserRole;

  // عرض مؤشرات خاصة بمدير النظام
  if (userRole === "admin" || userRole === "sub-admin") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <DashboardStat
          title="المستخدمين"
          value={isLoading ? "..." : userCounts.users?.toString() || "0"}
          icon={Users}
          description="إجمالي المستخدمين"
          trend="up"
        />
        <DashboardStat
          title="المتاجر"
          value={isLoading ? "..." : userCounts.stores?.toString() || "0"}
          icon={Store}
          description="إجمالي المتاجر"
          trend="up"
        />
        <DashboardStat
          title="المنتجات"
          value={isLoading ? "..." : userCounts.products?.toString() || "0"}
          icon={ShoppingBag}
          description="إجمالي المنتجات"
          trend="up"
        />
        <DashboardStat
          title="العمليات المالية"
          value={isLoading ? "..." : userCounts.transactions?.toString() || "0"}
          icon={CircleDollarSign}
          description="إجمالي المعاملات"
          trend="stable"
        />
      </div>
    );
  }

  // عرض مؤشرات خاصة بالمتاجر التقليدية
  if (userRole === "store") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {/* <DashboardStat
          title="الفروع"
          value={isLoading ? "..." : (userCounts.branches?.toString() || "0")}
          icon={Building}
          description="إجمالي الفروع"
          trend="up"
        /> */}
        <DashboardStat
          title="المنتجات"
          value={isLoading ? "..." : userCounts.products?.toString() || "0"}
          icon={ShoppingBag}
          description="إجمالي المنتجات"
          trend="up"
        />
        <DashboardStat
          title="الطلبات"
          value={isLoading ? "..." : userCounts.orders?.toString() || "0"}
          icon={Layers}
          description="إجمالي الطلبات"
          trend="up"
        />
        <DashboardStat
          title="الإيرادات"
          value={
            isLoading ? "..." : (userCounts.revenue?.toString() || "0") + " ر.س"
          }
          icon={CircleDollarSign}
          description="إجمالي الإيرادات"
          trend="up"
        />
      </div>
    );
  }

  // عرض مؤشرات خاصة بالمتاجر الإلكترونية
  if (userRole === "ecommerce") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <DashboardStat
          title="المنتجات"
          value={isLoading ? "..." : userCounts.products?.toString() || "0"}
          icon={ShoppingBag}
          description="إجمالي المنتجات"
          trend="up"
        />
        <DashboardStat
          title="الطلبات"
          value={isLoading ? "..." : userCounts.orders?.toString() || "0"}
          icon={Layers}
          description="إجمالي الطلبات"
          trend="up"
        />
        <DashboardStat
          title="الإيرادات"
          value={
            isLoading ? "..." : (userCounts.revenue?.toString() || "0") + " ر.س"
          }
          icon={CircleDollarSign}
          description="إجمالي الإيرادات"
          trend="stable"
        />
        <DashboardStat
          title="الأداء"
          value={isLoading ? "..." : "جيد"}
          icon={BarChart3}
          description="أداء المتجر"
          trend="up"
        />
      </div>
    );
  }

  // للمستخدم العادي
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4 mb-8">
      <DashboardStat
        title="طلباتي"
        value={isLoading ? "..." : userCounts.orders?.toString() || "0"}
        icon={Layers}
        description="إجمالي الطلبات"
        trend="up"
      />
      <DashboardStat
        title="مشترياتي"
        value={
          isLoading ? "..." : (userCounts.revenue?.toString() || "0") + " ر.س"
        }
        icon={CircleDollarSign}
        description="إجمالي المشتريات"
        trend="stable"
      />
    </div>
  );
};

export default UserRoleDashboardStats;
