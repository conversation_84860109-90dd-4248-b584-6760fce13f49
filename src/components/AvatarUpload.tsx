import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { toast } from "sonner";
import { uploadImage } from "@/utils/supabaseActions";

interface AvatarUploadProps {
  initialAvatar?: string;
  onAvatarChange?: (url: string) => void;
  size?: "sm" | "md" | "lg";
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  initialAvatar,
  onAvatarChange,
  size = "lg",
}) => {
  const [avatar, setAvatar] = useState<string>(initialAvatar || "");
  const [isUploading, setIsUploading] = useState(false);
  const { user } = useAuth();

  const sizes = {
    sm: "h-16 w-16",
    md: "h-24 w-24",
    lg: "h-32 w-32",
  };

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    try {
      // Create a unique file name
      const fileExt = file.name.split(".").pop();
      const fileName = `${user?.id || "anonymous"}-${Date.now()}.${fileExt}`;
      const filePath = `${fileName}`;

      // Upload to Supabase Storage
      const url = await uploadImage({
        storageName: "avatars",
        file: file,
        path: filePath,
      });
      console.log(url);

      setAvatar(url);
      onAvatarChange?.(url);

      toast.success("تم رفع الصورة بنجاح");
    } catch (error) {
      console.error("Error uploading avatar:", error);
      toast.error(`فشل في رفع الصورة: ${error.message || "خطأ غير معروف"}`);
    } finally {
      setIsUploading(false);
    }
  };

  const getInitials = (name: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <Avatar className={`${sizes[size]} border-2 border-primary`}>
        <AvatarImage src={avatar} alt="Profile" />
        <AvatarFallback>
          {initialAvatar ? getInitials(initialAvatar) : "U"}
        </AvatarFallback>
      </Avatar>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          className="relative"
          disabled={isUploading}
        >
          <input
            placeholder="img"
            type="file"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleUpload}
            accept="image/*"
            disabled={isUploading}
          />
          <Upload className="h-4 w-4 mr-2" />
          {isUploading ? "جاري الرفع..." : "تغيير الصورة"}
        </Button>
      </div>
    </div>
  );
};

export default AvatarUpload;
