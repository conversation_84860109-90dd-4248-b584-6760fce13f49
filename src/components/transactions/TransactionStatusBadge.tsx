
import { Badge } from "@/components/ui/badge";

interface TransactionStatusBadgeProps {
  status: string;
}

export function TransactionStatusBadge({ status }: TransactionStatusBadgeProps) {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-300";
      case "approved":
        return "bg-blue-100 text-blue-800 border-blue-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <Badge className={getStatusBadgeColor(status)}>
      {status === "completed" && "مكتملة"}
      {status === "pending" && "قيد الانتظار"}
      {status === "rejected" && "مرفوضة"}
      {status === "approved" && "معتمدة"}
      {status === "failed" && "فشلت"}
      {status === "cancelled" && "ملغية"}
      {status === "processing" && "جاري المعالجة"}
    </Badge>
  );
}

export default TransactionStatusBadge;
