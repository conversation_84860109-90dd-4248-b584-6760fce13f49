
import React, { useState } from "react";
import { Transaction } from "@/types";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Eye, Receipt } from "lucide-react";

interface TransactionReceiptPreviewProps {
  transaction: Transaction | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TransactionReceiptPreview({
  transaction,
  open,
  onOpenChange,
}: TransactionReceiptPreviewProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>إيصال المعاملة</DialogTitle>
        </DialogHeader>
        {transaction?.receipt_url && (
          <div className="flex items-center justify-center">
            <a
              href={transaction.receipt_url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src={transaction.receipt_url}
                alt="إيصال المعاملة"
                className="max-h-[70vh] max-w-full object-contain"
              />
            </a>
          </div>
        )}
        <div className="flex justify-end gap-2 mt-4">
          {transaction?.receipt_url && (
            <Button asChild variant="outline">
              <a
                href={transaction.receipt_url}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Eye className="h-4 w-4 ml-2" />
                فتح في نافذة جديدة
              </a>
            </Button>
          )}
          <Button onClick={() => onOpenChange(false)}>إغلاق</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function TransactionReceiptButton({
  transaction,
  onViewReceipt,
}: {
  transaction: Transaction;
  onViewReceipt: (transaction: Transaction) => void;
}) {
  return transaction.receipt_url ? (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onViewReceipt(transaction)}
    >
      <Receipt className="h-4 w-4" />
    </Button>
  ) : (
    <span className="text-muted-foreground">-</span>
  );
}

export default TransactionReceiptPreview;
