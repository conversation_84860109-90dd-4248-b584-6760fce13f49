
import React from "react";
import { TransactionType } from "@/types";

interface TransactionTypeDisplayProps {
  type: TransactionType;
}

export function TransactionTypeDisplay({ type }: TransactionTypeDisplayProps) {
  switch (type) {
    case "deposit":
      return <>إيداع</>;
    case "withdraw":
    case "withdrawal":
      return <>سحب</>;
    case "transfer":
      return <>تحويل</>;
    case "payment":
      return <>دفع</>;
    case "refund":
      return <>استرداد</>;
    case "commission":
      return <>عمولة</>;
    case "fee":
      return <>رسوم</>;
    default:
      return <>{type}</>;
  }
}

export default TransactionTypeDisplay;
