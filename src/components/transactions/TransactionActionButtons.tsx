
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, X } from "lucide-react";

interface TransactionActionButtonsProps {
  status: string;
  onApprove?: () => void;
  onReject?: () => void;
  isLoading: boolean;
}

export function TransactionActionButtons({
  status,
  onApprove,
  onReject,
  isLoading,
}: TransactionActionButtonsProps) {
  if (status !== "pending" || (!onApprove && !onReject)) {
    return null;
  }

  return (
    <div className="flex space-x-2 space-x-reverse">
      {onApprove && (
        <Button
          variant="outline"
          size="sm"
          disabled={isLoading}
          className="text-green-600 hover:text-green-700"
          onClick={onApprove}
        >
          <Check className="h-4 w-4 ml-1" />
          قبول
        </Button>
      )}
      {onReject && (
        <Button
          variant="outline"
          size="sm"
          disabled={isLoading}
          className="text-red-600 hover:text-red-700"
          onClick={onReject}
        >
          <X className="h-4 w-4 ml-1" />
          رفض
        </Button>
      )}
    </div>
  );
}

export default TransactionActionButtons;
