import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from "lucide-react";
import { AdminPermission, UserRole } from "@/types";
import { useAuth } from "@/contexts/auth";
import { useNavItems } from "./navbar/NavItems";
import { getIconComponent, SupportedIcon } from "./navbar/NavItems";
import { NavItem } from "./navbar/types";
import { useNotifications } from "@/contexts/NotificationContext";

type SidebarProps = {
  className?: string;
  isOpen?: boolean;
  onClose?: () => void;
  isMobile?: boolean;
};

export function Sidebar({
  className,
  isOpen = true,
  onClose,
  isMobile = false,
}: SidebarProps) {
  const { user } = useAuth();
  const location = useLocation();
  const navItems = useNavItems();
  const { userNotifications } = useNotifications();

  // Count unread notifications
  const unreadCount = userNotifications.filter((n) => !n.read).length;

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  const hasPermission = (requiredPermissions?: AdminPermission[]): boolean => {
    if (!requiredPermissions || requiredPermissions.length === 0) return true;
    if (!user?.permissions || user.permissions.length === 0) return false;

    return requiredPermissions.some((permission) =>
      user.permissions?.includes(permission)
    );
  };

  const getFilteredNavItems = (): NavItem[] => {
    if (!user) return [];

    let items: NavItem[] = [...navItems.commonItems];

    navItems.ecommerceItems.forEach((item) => {
      if (
        !item.roles ||
        (item.roles &&
          Array.isArray(item.roles) &&
          item.roles.some((role) => role === (user.role as UserRole)))
      ) {
        items.push(item);
      }
    });

    if (user.role === "store") {
      items = [
        ...items,
        ...navItems.storeItems.filter(
          (item) =>
            !item.roles ||
            (item.roles &&
              Array.isArray(item.roles) &&
              item.roles.some((role) => role === "store"))
        ),
      ];
    }

    if (user.role === "admin" || user.role === "sub-admin") {
      const filteredAdminItems = navItems.adminItems.filter((item) => {
        if (user.role === "admin") return true;

        return !item.permissions || hasPermission(item.permissions);
      });

      items = [...items, ...filteredAdminItems];
    }

    items.push(navItems.settingsItem);

    return items;
  };

  const filteredNavItems = getFilteredNavItems();

  // Safely convert React nodes to string
  const getLabelString = (label: React.ReactNode): string => {
    if (typeof label === "string") return label;
    if (typeof label === "number") return String(label);
    if (typeof label === "boolean") return String(label);
    // For complex React elements or objects, return a fallback string
    return "قائمة";
  };

  return (
    <aside
      className={cn(
        "pb-12 rtl border-l bg-background transition-all duration-300 ease-in-out",
        className
      )}
    >
      {isMobile && (
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold tracking-tight">لوحة التحكم</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-5 w-5" />
            <span className="sr-only">إغلاق</span>
          </Button>
        </div>
      )}
      <div className="space-y-4 py-4">
        <div className="px-4 py-2">
          {!isMobile && (
            <h2 className="mb-2 px-2 text-lg font-semibold tracking-tight">
              لوحة التحكم
            </h2>
          )}
          <ScrollArea className="h-[calc(100vh-10rem)]">
            <div className="space-y-1">
              {filteredNavItems.map((item, index) => {
                const IconComponent =
                  item.icon && typeof item.icon === "string"
                    ? getIconComponent(item.icon as SupportedIcon)
                    : null;

                // Check if this is the notifications item
                const isNotificationItem =
                  item.href && item.href.includes("/notifications");

                return (
                  <Button
                    key={index}
                    variant={isActive(item.href) ? "secondary" : "ghost"}
                    size="sm"
                    className={cn(
                      "w-full justify-start",
                      !isOpen && "px-0 justify-center"
                    )}
                    onClick={isMobile ? onClose : undefined}
                  >
                    <Link
                      to={item.href}
                      className="relative flex flex-row gap-2 w-full h-full items-center justify-end "
                    >
                      {isOpen && getLabelString(item.label)}
                      <div className="relative">
                        {IconComponent && (
                          <IconComponent
                            className={cn("h-4 w-4", isOpen ? "mr-2" : "")}
                          />
                        )}
                        {/* Add notification badge directly on the icon */}
                        {isNotificationItem && unreadCount > 0 && (
                          <div className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground text-[10px] min-w-[16px] h-[16px] rounded-full flex items-center justify-center px-1">
                            {unreadCount > 99 ? "99+" : unreadCount}
                          </div>
                        )}
                      </div>
                    </Link>
                  </Button>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </div>
    </aside>
  );
}

export default Sidebar;
