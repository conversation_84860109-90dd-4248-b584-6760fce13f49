import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { StoreEmployee } from "@/types";
import { useAuth } from "@/contexts/auth";

// Validation schema
const employeeSchema = z.object({
  name: z.string().min(2, { message: "الاسم يجب أن يكون على الأقل حرفين" }),
  email: z.string().email({ message: "الرجاء إدخال بريد إلكتروني صالح" }),
  position: z.string().min(1, { message: "المنصب مطلوب" }),
  phone_number: z.string().min(1, { message: "رقم الهاتف مطلوب" }),
  active: z.boolean().default(true),
  branch_id: z.string().optional(),
  permissions: z.array(z.string()).optional(),
});

export interface EmployeeWizardProps {
  initialData?: StoreEmployee;
  onSubmit: (employeeData: StoreEmployee) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function EmployeeWizard({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}: EmployeeWizardProps) {
  const { user } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      position: initialData?.position || "",
      phone_number: initialData?.phone_number || "",
      active: initialData?.active !== false,
      branch_id: initialData?.branch_id || "",
      permissions: initialData?.permissions || [],
      user_id: initialData?.user_id || "", // Default to empty string if not provided
    },
  });

  const submitHandler = handleSubmit((data) => {
    // Ensure user_id is always set
    onSubmit(data as StoreEmployee);
  });

  return (
    <form onSubmit={submitHandler} className="space-y-4">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="name">
            الاسم <span className="text-red-500">*</span>
          </Label>
          <Input id="name" {...register("name")} placeholder="اسم الموظف" />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="email">
            البريد الإلكتروني <span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="position">
            المنصب <span className="text-red-500">*</span>
          </Label>
          <Input
            id="position"
            {...register("position")}
            placeholder="المسمى الوظيفي"
          />
          {errors.position && (
            <p className="text-sm text-red-500 mt-1">
              {errors.position.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="phone_number">
            رقم الهاتف <span className="text-red-500">*</span>
          </Label>
          <Input
            id="phone_number"
            {...register("phone_number")}
            placeholder="رقم الهاتف"
          />
          {errors.phone_number && (
            <p className="text-sm text-red-500 mt-1">
              {errors.phone_number.message}
            </p>
          )}
        </div>

        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Checkbox
            id="active"
            {...register("active")}
            defaultChecked={initialData?.active !== false}
          />
          <Label htmlFor="active" className="cursor-pointer">
            موظف نشط
          </Label>
        </div>

        {/* Branch selection could be added here when branch data is available */}

        {/* Permissions selection could be added here based on your permissions model */}
      </div>

      <div className="flex justify-end space-x-2 rtl:space-x-reverse pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          إلغاء
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "جاري الحفظ..." : initialData ? "تحديث" : "إنشاء"}
        </Button>
      </div>
    </form>
  );
}
