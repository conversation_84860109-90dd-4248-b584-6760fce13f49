import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Branch } from '@/types';
import { Mail, Phone, User, Briefcase, Building2, ShieldCheck } from 'lucide-react';

// Available permissions
const availablePermissions = [
  {
    id: 'manage_products',
    label: 'إدارة المنتجات',
    description: 'إضافة وتعديل وحذف المنتجات'
  },
  {
    id: 'manage_orders',
    label: 'إدارة الطلبات',
    description: 'قبول ورفض وإدارة طلبات الاستضافة'
  },
  {
    id: 'view_reports',
    label: 'عرض التقارير',
    description: 'الوصول إلى تقارير المبيعات والإحصائيات'
  },
  {
    id: 'manage_finances',
    label: 'إدارة المالية',
    description: 'الوصول إلى المعاملات المالية والفواتير'
  },
  {
    id: 'manage_employees',
    label: 'إدارة الموظفين',
    description: 'إضافة وتعديل وحذف الموظفين الآخرين'
  }
];

// Form schema for employee
export const employeeFormSchema = z.object({
  name: z.string().min(3, { message: 'لازم اسم الموظف يكون 3 أحرف على الأقل' }),
  email: z.string().email({ message: 'لازم تدخل بريد إلكتروني صحيح' }),
  phoneNumber: z.string().min(10, { message: 'لازم تدخل رقم جوال صحيح' }),
  position: z.string().min(2, { message: 'لازم تدخل المسمى الوظيفي' }),
  branchId: z.string().optional(),
  permissions: z.array(z.string()).min(1, { message: 'لازم تختار صلاحية وحدة على الأقل' }),
  active: z.boolean().default(true),
});

export type EmployeeFormValues = z.infer<typeof employeeFormSchema>;

interface EmployeeFormProps {
  defaultValues?: EmployeeFormValues;
  branches: Branch[];
  onSubmit: (values: EmployeeFormValues) => void;
  isSubmitting?: boolean;
}

const EmployeeForm: React.FC<EmployeeFormProps> = ({ 
  defaultValues, 
  branches, 
  onSubmit,
  isSubmitting = false
}) => {
  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: defaultValues || {
      name: '',
      email: '',
      phoneNumber: '',
      position: '',
      branchId: undefined,
      permissions: [],
      active: true,
    }
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
        <div className="flex items-center gap-2 mb-4 text-primary">
          <User className="h-5 w-5" />
          <h3 className="text-lg font-medium">البيانات الشخصية</h3>
        </div>
        
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم الموظف</FormLabel>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="الاسم الكامل" {...field} className="pl-10" />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>البريد الإلكتروني</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="<EMAIL>" {...field} className="pl-10" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>رقم الجوال</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="05xxxxxxxx" {...field} className="pl-10" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex items-center gap-2 mt-8 mb-4 text-primary">
          <Briefcase className="h-5 w-5" />
          <h3 className="text-lg font-medium">البيانات الوظيفية</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="position"
            render={({ field }) => (
              <FormItem>
                <FormLabel>المسمى الوظيفي</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="مدير مبيعات" {...field} className="pl-10" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="branchId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الفرع</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <div className="flex items-center">
                        <Building2 className="h-4 w-4 mr-2 text-muted-foreground" />
                        <SelectValue placeholder="اختر الفرع" />
                      </div>
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">غير محدد</SelectItem>
                    {branches.map(branch => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  الفرع اللي يشتغل فيه الموظف
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex items-center gap-2 mt-8 mb-4 text-primary">
          <ShieldCheck className="h-5 w-5" />
          <h3 className="text-lg font-medium">الصلاحيات</h3>
        </div>
        
        <FormField
          control={form.control}
          name="permissions"
          render={() => (
            <FormItem>
              <div className="mb-4">
                <FormDescription className="text-base">
                  حدد الصلاحيات اللي يقدر الموظف يوصل لها
                </FormDescription>
              </div>
              <div className="space-y-3 border rounded-md p-3 bg-slate-50">
                {availablePermissions.map((permission) => (
                  <FormField
                    key={permission.id}
                    control={form.control}
                    name="permissions"
                    render={({ field }) => {
                      return (
                        <FormItem
                          key={permission.id}
                          className="flex flex-row items-start space-x-3 space-y-0 rtl:space-x-reverse rounded-md p-2 hover:bg-slate-100"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(permission.id)}
                              onCheckedChange={(checked) => {
                                return checked
                                  ? field.onChange([...field.value, permission.id])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value) => value !== permission.id
                                      )
                                    )
                              }}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="font-medium">
                              {permission.label}
                            </FormLabel>
                            <FormDescription className="text-xs">
                              {permission.description}
                            </FormDescription>
                          </div>
                        </FormItem>
                      )
                    }}
                  />
                ))}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm mt-6">
              <div className="space-y-0.5">
                <FormLabel className="text-base">الموظف نشط</FormLabel>
                <FormDescription>
                  حدد إذا كان الموظف نشط ويقدر يستخدم النظام
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        
        <div className="flex justify-end pt-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "جاري الحفظ..." : "حفظ البيانات"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default EmployeeForm;
