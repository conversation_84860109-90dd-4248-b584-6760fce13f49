
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { StoreEmployee } from '@/types';

interface EmployeeListProps {
  employees: StoreEmployee[];
  onDelete: (employee: StoreEmployee) => void;
}

const EmployeeList = ({ employees, onDelete }: EmployeeListProps) => {
  if (employees.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">لا توجد بيانات موظفين للعرض</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {employees.map((employee) => (
        <Card key={employee.id} className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium">{employee.name}</h3>
              <p className="text-sm text-gray-500">{employee.position || employee.role}</p>
              <p className="text-sm mt-2">{employee.phone_number || employee.phone}</p>
            </div>
            <div className="space-x-2 rtl:space-x-reverse">
              <Button variant="destructive" size="sm" onClick={() => onDelete(employee)}>
                حذف
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default EmployeeList;
