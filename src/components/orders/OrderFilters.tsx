
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowUp, ArrowDown, FileText } from 'lucide-react';

interface OrderFiltersProps {
  selectedStatus: string | null;
  sortOrder: 'asc' | 'desc';
  onFilterChange: (status: string | null) => void;
  onSortOrderChange: () => void;
}

export const OrderFilters: React.FC<OrderFiltersProps> = ({
  selectedStatus,
  sortOrder,
  onFilterChange,
  onSortOrderChange,
}) => {
  return (
    <div className="flex flex-wrap gap-2">
      <Button 
        variant={selectedStatus === null ? "secondary" : "outline"} 
        size="sm"
        onClick={() => onFilterChange(null)}
      >
        الكل
      </Button>
      <Button 
        variant={selectedStatus === 'pending' ? "secondary" : "outline"} 
        size="sm"
        onClick={() => onFilterChange('pending')}
      >
        قيد الانتظار
      </Button>
      <Button 
        variant={selectedStatus === 'processing' ? "secondary" : "outline"} 
        size="sm"
        onClick={() => onFilterChange('processing')}
      >
        قيد المعالجة
      </Button>
      <Button 
        variant={selectedStatus === 'completed' ? "secondary" : "outline"} 
        size="sm"
        onClick={() => onFilterChange('completed')}
      >
        مكتمل
      </Button>
      <Button 
        variant="outline" 
        size="sm"
        onClick={onSortOrderChange}
        className="flex items-center gap-1"
      >
        {sortOrder === 'desc' ? (
          <>
            <ArrowDown className="h-4 w-4" />
            الأحدث أولاً
          </>
        ) : (
          <>
            <ArrowUp className="h-4 w-4" />
            الأقدم أولاً
          </>
        )}
      </Button>
      <Button 
        variant="outline" 
        size="sm"
        className="ml-auto sm:ml-4"
      >
        <FileText className="h-4 w-4 ml-2" />
        تصدير التقرير
      </Button>
    </div>
  );
};

export default OrderFilters;
