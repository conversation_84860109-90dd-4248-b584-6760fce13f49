
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Order, OrderStatus } from '@/types';
import { OrderStatusBadge } from './OrderStatusBadge';

interface OrdersListProps {
  orders: Order[];
  isLoading: boolean;
}

export const OrdersList: React.FC<OrdersListProps> = ({ orders, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">لا توجد طلبات لعرضها</p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم الطلب</TableHead>
            <TableHead>العميل</TableHead>
            <TableHead>التاريخ</TableHead>
            <TableHead>المجموع</TableHead>
            <TableHead>العناصر</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="w-[100px]">الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => (
            <TableRow key={order.id}>
              <TableCell className="font-medium">{order.id}</TableCell>
              <TableCell>{order.customer || order.customer_name}</TableCell>
              <TableCell>{order.date || new Date(order.created_at).toLocaleDateString('ar-SA')}</TableCell>
              <TableCell>{order.total} ر.س</TableCell>
              <TableCell>{order.items?.length || 0}</TableCell>
              <TableCell>
                <OrderStatusBadge status={order.status} />
              </TableCell>
              <TableCell>
                <Button variant="ghost" size="sm" onClick={() => console.log('View order', order.id)}>
                  عرض التفاصيل
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default OrdersList;
