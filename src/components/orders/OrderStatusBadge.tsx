
import { Badge } from "@/components/ui/badge";
import { OrderStatus } from "@/types";
import { Package, ShoppingBag, Timer } from "lucide-react";

interface OrderStatusBadgeProps {
  status: OrderStatus;
}

export const OrderStatusBadge = ({ status }: OrderStatusBadgeProps) => {
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case "awaiting_shipping":
        return {
          label: "بأنتظار شحن المنتجات",
          className: "bg-amber-100 text-amber-800",
          icon: <Package className="h-4 w-4 mr-1" />
        };
      case "on_sale":
        return {
          label: "منتجات معروضة للبيع",
          className: "bg-green-100 text-green-800",
          icon: <ShoppingBag className="h-4 w-4 mr-1" />
        };
      case "expired":
        return {
          label: "اشتراك منتهي",
          className: "bg-red-100 text-red-800",
          icon: <Timer className="h-4 w-4 mr-1" />
        };
      case "pending":
        return {
          label: "قيد الانتظار",
          className: "bg-yellow-100 text-yellow-800",
          icon: null
        };
      case "accepted":
        return {
          label: "مقبول",
          className: "bg-green-100 text-green-800",
          icon: null
        };
      case "rejected":
        return {
          label: "مرفوض",
          className: "bg-red-100 text-red-800",
          icon: null
        };
      default:
        return {
          label: status,
          className: "bg-gray-100 text-gray-800",
          icon: null
        };
    }
  };

  const { label, className, icon } = getStatusInfo(status);

  return (
    <Badge className={`flex items-center ${className}`}>
      {icon}
      {label}
    </Badge>
  );
};
