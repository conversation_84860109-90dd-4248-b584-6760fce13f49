
import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Order, OrderItem } from '@/types';
import { OrderStatusBadge } from './OrderStatusBadge';

interface OrderDetailProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  items?: OrderItem[];
}

export const OrderDetail: React.FC<OrderDetailProps> = ({
  order,
  isOpen,
  onClose,
  items = [],
}) => {
  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>تفاصيل الطلب #{order.id}</DialogTitle>
          <DialogDescription>
            تاريخ الطلب: {order.date}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium">معلومات العميل</h3>
              <p className="text-sm mt-1">{order.customer}</p>
              {order.phone_number && <p className="text-sm">{order.phone_number}</p>}
              {order.address && <p className="text-sm">{order.address}</p>}
            </div>
            <div>
              <h3 className="text-sm font-medium">معلومات الطلب</h3>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-sm">الحالة:</p>
                <OrderStatusBadge status={order.status} />
              </div>
              {order.payment_method && (
                <p className="text-sm">طريقة الدفع: {order.payment_method}</p>
              )}
              {order.tracking_number && (
                <p className="text-sm">رقم التتبع: {order.tracking_number}</p>
              )}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-sm font-medium mb-2">المنتجات</h3>
            <ScrollArea className="h-[200px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المنتج</TableHead>
                    <TableHead className="text-right">الكمية</TableHead>
                    <TableHead className="text-right">السعر</TableHead>
                    <TableHead className="text-right">المجموع</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.length > 0 ? (
                    items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.product_name}</TableCell>
                        <TableCell className="text-right">{item.quantity}</TableCell>
                        <TableCell className="text-right">{item.price} ر.س</TableCell>
                        <TableCell className="text-right">{item.total} ر.س</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center">
                        لا توجد منتجات لعرضها
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>

          <Separator />

          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm">المجموع: {order.total} ر.س</p>
            </div>
            <div className="space-x-2 rtl:space-x-reverse">
              <Button
                variant="outline"
                onClick={onClose}
              >
                إغلاق
              </Button>
              <Button>
                طباعة الفاتورة
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OrderDetail;
