
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const formSchema = z.object({
  email: z.string().email({ message: "البريد الإلكتروني غير صالح" }),
  password: z
    .string()
    .min(6, { message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" }),
});

export type PasswordFormData = z.infer<typeof formSchema>;

interface PasswordLoginFormProps {
  onSubmit: (data: PasswordFormData) => Promise<void>;
  isLoading: boolean;
  switchToRegister: () => void;
  switchToResetPassword: () => void;
}

const PasswordLoginForm = ({
  onSubmit,
  isLoading,
  switchToRegister,
  switchToResetPassword,
}: PasswordLoginFormProps) => {
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<PasswordFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>البريد الإلكتروني</FormLabel>
              <FormControl>
                <Input
                  placeholder="<EMAIL>"
                  {...field}
                  type="email"
                  autoComplete="email"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>كلمة المرور</FormLabel>
              <FormControl>
                <Input {...field} type="password" placeholder="********" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="link"
            size="sm"
            className="px-0 text-sm"
            onClick={switchToResetPassword}
          >
            نسيت كلمة المرور؟
          </Button>
        </div>
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "جاري التحميل..." : "تسجيل الدخول"}
        </Button>
        <div className="mt-4 text-center text-sm">
          لا تملك حسابًا؟{" "}
          <Button variant="link" className="p-0" onClick={switchToRegister}>
            إنشاء حساب
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default PasswordLoginForm;
