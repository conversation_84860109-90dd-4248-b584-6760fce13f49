
import React from 'react';
import { Form, FormField } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Loader2, ArrowLeft, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { OTPInputField, formSchema, FormValues } from './OTPInput';
import { ResendOTPButton } from './ResendOTPButton';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Export the schema and types for OTP verification form
export const otpSchema = z.object({
  otp: z.string().length(6, { message: 'رمز التحقق يجب أن يتكون من 6 أرقام' }),
});

export type OTPFormValues = z.infer<typeof otpSchema>;

interface SharedOTPVerificationFormProps {
  onSubmit: (values: OTPFormValues) => Promise<void>;
  onResend: () => Promise<void>;
  onBack: () => void;
  isLoading: boolean;
  isResending: boolean;
  resendCooldown: number;
  error: string | null;
  maxAttempts?: number;
  currentAttempts?: number;
}

export function SharedOTPVerificationForm({
  onSubmit,
  onResend,
  onBack,
  isLoading,
  isResending,
  resendCooldown,
  error,
  maxAttempts = 3,
  currentAttempts = 0
}: SharedOTPVerificationFormProps) {
  const form = useForm<OTPFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
    },
  });

  // Watch the OTP value for auto-submission when all digits are entered
  const otp = form.watch('otp');
  
  React.useEffect(() => {
    if (otp && otp.length === 6 && !isLoading) {
      form.handleSubmit(values => onSubmit(values))();
    }
  }, [otp, isLoading, form, onSubmit]);

  // Clear form error when user starts typing a new OTP
  React.useEffect(() => {
    if (otp && otp.length > 0 && form.formState.errors.otp) {
      form.clearErrors('otp');
    }
  }, [otp, form]);

  // Show remaining attempts information
  const showRemainingAttempts = maxAttempts > 0 && currentAttempts > 0;
  const attemptsLeft = maxAttempts - currentAttempts;
  const isInDangerZone = attemptsLeft <= Math.ceil(maxAttempts / 3);

  return (
    <>
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="text-center mb-4">
            <p className="text-sm text-muted-foreground mb-2">
              أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني
            </p>
            <p className="text-xs text-muted-foreground">
              قد يستغرق وصول الرمز بضع دقائق. يرجى التحقق من مجلد البريد غير الهام أيضاً.
            </p>
            
            {showRemainingAttempts && (
              <div className={`mt-2 text-xs ${isInDangerZone ? 'text-destructive' : 'text-amber-500'}`}>
                <p>محاولات متبقية: {attemptsLeft} من {maxAttempts}</p>
              </div>
            )}
          </div>

          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => <OTPInputField control={form.control} />}
          />

          <div className="space-y-2">
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || otp.length !== 6}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري التحقق...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" /> تحقق
                </>
              )}
            </Button>

            <div className="flex justify-between items-center mt-4">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="flex items-center"
              >
                <ArrowLeft className="ml-1 h-4 w-4" />
                العودة
              </Button>

              <ResendOTPButton 
                isResending={isResending}
                resendCooldown={resendCooldown}
                onResend={onResend}
              />
            </div>
          </div>
        </form>
      </Form>
    </>
  );
}
