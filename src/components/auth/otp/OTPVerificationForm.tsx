import React from 'react';
import { z } from 'zod';
import { SharedOTPVerificationForm, OTPFormValues, otpSchema } from './SharedOTPVerificationForm';

interface OTPVerificationFormProps {
  onSubmit: (values: OTPFormValues) => Promise<void>;
  onResend: () => Promise<void>;
  onBack: () => void;
  isLoading: boolean;
  isResending: boolean;
  resendCooldown: number;
  error: string | null;
  maxAttempts?: number;
  currentAttempts?: number;
}

export function OTPVerificationForm(props: OTPVerificationFormProps) {
  // Use the shared component with the same props
  return <SharedOTPVerificationForm {...props} />;
}

export { otpSchema };
export type { OTPFormValues };
