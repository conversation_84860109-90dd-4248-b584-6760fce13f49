
import React from 'react';
import { Shield, AlertCircle } from 'lucide-react';
import { formatMaskedEmail } from '@/contexts/auth/otpService';

export interface EmailDisplayProps {
  email: string;
  type?: 'login' | 'register' | 'reset';
}

export function EmailDisplay({ email, type = 'login' }: EmailDisplayProps) {
  // Get the appropriate heading based on verification type
  const getHeading = () => {
    switch (type) {
      case 'register': return 'تحقق من التسجيل';
      case 'reset': return 'إعادة تعيين كلمة المرور';
      default: return 'التحقق من البريد الإلكتروني';
    }
  };
  
  // Get appropriate security tips based on verification type
  const getSecurityTips = () => {
    const commonTips = [
      'يرجى التحقق من صندوق الوارد والبريد غير المرغوب فيه',
      'رمز التحقق صالح لمدة 10 دقائق فقط',
      'لا تشارك رمز التحقق مع أي شخص'
    ];
    
    if (type === 'login') {
      return [...commonTips, 'سيتم تسجيل دخولك تلقائيًا بعد التحقق من الرمز'];
    } else if (type === 'reset') {
      return [...commonTips, 'ستتمكن من تعيين كلمة مرور جديدة بعد التحقق من الرمز'];
    }
    
    return commonTips;
  };
  
  return (
    <div className="text-center mb-6">
      <div className="flex justify-center mb-3">
        <div className="bg-primary/10 p-2 rounded-full">
          <Shield className="h-6 w-6 text-primary" />
        </div>
      </div>
      
      <h3 className="text-2xl font-semibold">{getHeading()}</h3>
      
      <p className="mt-2 text-sm text-muted-foreground">
        تم إرسال رمز التحقق إلى
      </p>
      
      <div className="mt-3 inline-block px-4 py-2 bg-muted/60 rounded-md font-mono text-sm">
        <span dir="ltr" className="font-medium">{email}</span>
      </div>
      
      <div className="mt-4 text-xs text-muted-foreground bg-muted/50 p-3 rounded-md">
        <div className="flex items-center justify-center gap-1.5 mb-2 text-amber-500">
          <AlertCircle className="h-4 w-4" />
          <p className="font-medium">ملاحظات هامة:</p>
        </div>
        <ul className="text-right list-disc list-inside space-y-1">
          {getSecurityTips().map((tip, index) => (
            <li key={index}>{tip}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}
