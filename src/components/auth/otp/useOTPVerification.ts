
import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/toast';
import { FormValues, formSchema } from './OTPInput';
import { 
  generateOTP, 
  sendOTPEmail, 
  storeOTP, 
  verifyOTP 
} from './OTPService';
import { isValidOTPFormat } from '@/contexts/auth/otpService';

interface UseOTPVerificationProps {
  email: string;
  type: 'login' | 'register';
  onVerified: () => void;
}

export function useOTPVerification({ 
  email, 
  type, 
  onVerified 
}: UseOTPVerificationProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(60); // Start with 60 seconds cooldown
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: '',
    },
  });

  useEffect(() => {
    form.reset({
      otp: '',
    });
    
    setTimeout(() => {
      const firstInput = document.querySelector('input[id^="otp-"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      } else {
        console.warn('OTP input field not found in the DOM');
      }
    }, 100);
  }, [email, form, type]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [resendCooldown]);

  const otp = form.watch('otp');
  useEffect(() => {
    if (otp && otp.length === 6 && !isLoading) {
      form.handleSubmit(values => onSubmit(values))();
    }
  }, [otp, isLoading, form]);

  const handleResendOTP = useCallback(async () => {
    if (resendCooldown > 0) return;
    setIsResending(true);
    setError(null);
    
    try {
      const newOTP = generateOTP();
      
      const storedOTP = await storeOTP(email, newOTP, type);
      
      if (!storedOTP) {
        throw new Error('فشل تخزين رمز التحقق');
      }
      
      const sent = await sendOTPEmail(email, newOTP, type);
      
      if (sent) {
        toast({
          title: 'تم إرسال رمز جديد',
          description: 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني',
        });
        
        setResendCooldown(60);
        form.resetField('otp');
        
        setTimeout(() => {
          const firstInput = document.querySelector('input[id^="otp-"]') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          } else {
            console.warn('OTP input field not found in the DOM after resend');
          }
        }, 100);
      } else {
        throw new Error('فشل إرسال لينك التحقق');
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      setError(error.message);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message,
      });
    } finally {
      setIsResending(false);
    }
  }, [email, type, form, toast, resendCooldown]);

  const onSubmit = async (values: FormValues) => {
    if (!values.otp || !isValidOTPFormat(values.otp)) {
      setError('رمز التحقق يجب أن يتكون من 6 أرقام');
      return;
    }
    setIsLoading(true);
    setError(null);
    
    try {
      const { valid, message } = await verifyOTP(email, values.otp, type);
      
      if (valid) {
        toast({
          title: 'تم التحقق بنجاح',
          description: type === 'register' 
            ? 'تم التحقق من بريدك الإلكتروني بنجاح' 
            : 'تم تسجيل الدخول بنجاح',
        });
        onVerified();
      } else {
        console.error('OTP verification failed:', message);
        setError(message || 'رمز التحقق غير صحيح');
        toast({
          variant: 'destructive',
          title: 'فشل التحقق',
          description: message || 'رمز التحقق غير صحيح',
        });

        if (message && !message.includes('انتهت صلاحية')) {
          form.resetField('otp');
          
          setTimeout(() => {
            const firstInput = document.querySelector('input[id^="otp-"]') as HTMLInputElement;
            if (firstInput) {
              firstInput.focus();
            } else {
              console.warn('OTP input field not found in the DOM after error');
            }
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error during OTP verification:', error);
      setError(error.message);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    isLoading,
    isResending,
    resendCooldown,
    error,
    handleResendOTP,
    onSubmit,
    setError
  };
}
