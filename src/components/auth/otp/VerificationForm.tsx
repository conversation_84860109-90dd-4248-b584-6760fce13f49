
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Form, FormField } from '@/components/ui/form';
import { Loader2, ArrowLeft } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { OTPInputField } from './OTPInput';
import { ResendOTPButton } from './ResendOTPButton';
import { useOTPVerification } from './useOTPVerification';

interface VerificationFormProps {
  email: string;
  type: 'login' | 'register';
  onVerified: () => void;
  onBackToLogin: () => void;
}

export function VerificationForm({ 
  email, 
  type, 
  onVerified, 
  onBackToLogin 
}: VerificationFormProps) {
  const {
    form,
    isLoading,
    isResending,
    resendCooldown,
    error,
    handleResendOTP,
    onSubmit
  } = useOTPVerification({ email, type, onVerified });

  return (
    <>
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="text-center mb-4">
            <p className="text-sm text-muted-foreground mb-2">
              أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني
            </p>
            <p className="text-xs text-muted-foreground">
              قد يستغرق وصول الرمز بضع دقائق. يرجى التحقق من مجلد البريد غير الهام أيضاً.
            </p>
          </div>

          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => <OTPInputField control={form.control} />}
          />

          <div className="space-y-2">
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري التحقق...
                </>
              ) : (
                'تحقق'
              )}
            </Button>

            <div className="flex justify-between items-center mt-4">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onBackToLogin}
                className="flex items-center"
              >
                <ArrowLeft className="ml-1 h-4 w-4" />
                العودة
              </Button>

              <ResendOTPButton 
                isResending={isResending}
                resendCooldown={resendCooldown}
                onResend={handleResendOTP}
              />
            </div>
          </div>
        </form>
      </Form>
    </>
  );
}
