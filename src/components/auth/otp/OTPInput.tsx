
import React, { useRef, useEffect } from 'react';
import { Controller, Control, useWatch } from 'react-hook-form';
import { z } from 'zod';
import {
  FormControl,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';

export const formSchema = z.object({
  otp: z.string().length(6, {
    message: 'رمز التحقق يجب أن يتكون من 6 أرقام',
  }),
});

export type FormValues = z.infer<typeof formSchema>;

interface OTPInputProps {
  control: Control<FormValues>;
}

export const OTPInputField: React.FC<OTPInputProps> = ({ control }) => {
  const otpRef = useRef<HTMLInputElement>(null);

  const otpValue = useWatch({
    control,
    name: 'otp',
    defaultValue: '',
  });
  useEffect(() => {
    if (otpRef.current) {

      setTimeout(() => {
        otpRef.current?.focus();
      }, 100);
    }
  }, []);

  return (
    <FormItem className="space-y-2">
      <FormControl>
        <div className="flex flex-col items-center">
          <Controller
            control={control}
            name="otp"
            defaultValue=""
            render={({ field }) => (
              <InputOTP
                value={field.value}
                onChange={field.onChange}
                maxLength={6}
                className="gap-2 rtl:flex-row-reverse"
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} ref={otpRef} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            )}
          />
          {otpValue && otpValue.length > 0 && otpValue.length < 6 && (
            <p className="text-xs text-muted-foreground mt-2">
              يرجى إدخال باقي الرمز ({6 - otpValue.length} أرقام متبقية)
            </p>
          )}
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
};
