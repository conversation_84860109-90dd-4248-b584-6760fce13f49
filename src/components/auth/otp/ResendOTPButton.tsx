
import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';

interface ResendOTPButtonProps {
  isResending: boolean;
  resendCooldown: number;
  onResend: () => void;
  maxCooldown?: number;
}

export function ResendOTPButton({
  isResending,
  resendCooldown,
  onResend,
  maxCooldown = 60
}: ResendOTPButtonProps) {
  // Calculate percentage for the timer visual
  const percentage = resendCooldown > 0 ? (resendCooldown / maxCooldown) * 100 : 0;
  
  return (
    <div className="relative">
      {resendCooldown > 0 && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <svg className="w-full h-full" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="#f3f4f6"
              strokeWidth="5"
            />
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="currentColor"
              strokeWidth="5"
              strokeDasharray="283"
              strokeDashoffset={283 - (283 * percentage) / 100}
              className="text-muted-foreground/30"
              transform="rotate(-90 50 50)"
            />
          </svg>
        </div>
      )}
      
      <Button
        type="button"
        variant="link"
        size="sm"
        onClick={onResend}
        disabled={isResending || resendCooldown > 0}
        className={`px-0 flex items-center gap-1.5 relative z-10 ${
          resendCooldown > 0 ? 'text-muted-foreground' : 'text-primary'
        }`}
        aria-label={
          isResending ? 'جاري إعادة إرسال رمز التحقق' : 
          resendCooldown > 0 ? `إعادة إرسال الرمز بعد ${resendCooldown} ثانية` : 
          'إعادة إرسال رمز التحقق'
        }
      >
        {isResending ? (
          <>
            <Loader2 className="h-3 w-3 animate-spin" />
            جاري إعادة الإرسال...
          </>
        ) : resendCooldown > 0 ? (
          <>
            <span>
              إعادة إرسال الرمز (
              <span className="font-mono">{resendCooldown}</span>)
            </span>
          </>
        ) : (
          <>
            <RefreshCw className="h-3 w-3" />
            إعادة إرسال رمز التحقق
          </>
        )}
      </Button>
    </div>
  );
}
