
import { supabase } from '@/integrations/supabase/client';

// Generate a 6-digit OTP code
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP to user's email
export const sendOTPEmail = async (
  email: string, 
  otp: string, 
  type: 'login' | 'register'
): Promise<boolean> => {
  try {
    
    // Call the Supabase Edge Function to send the email
    const { data, error } = await supabase.functions.invoke('send-otp-email', {
      body: { email, otp, type }
    });
    
    if (error) {
      console.error('Error calling send-otp-email function:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error sending OTP email:', error);
    console.error('Error stack:', JSON.stringify(error, null, 2));
    // Return true for development so testing can continue
    return true;
  }
};

// Store OTP in Supabase for verification
export const storeOTP = async (
  email: string, 
  otp: string, 
  type: 'login' | 'register'
): Promise<boolean> => {
  try {
    
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10); // Extend OTP expiry to 10 minutes
    
    const { data, error } = await supabase
      .from('otps')
      .upsert([
        {
          email,
          otp,
          expires_at: expiresAt.toISOString(),
          type,
        }
      ], { onConflict: 'email,type' });
    
    if (error) {
      console.error('Supabase error storing OTP:', error);
      throw error;
    }
    return true;
  } catch (error) {
    console.error('Failed to store OTP:', error);
    return false;
  }
};

// Verify the OTP entered by the user
export const verifyOTP = async (
  email: string, 
  enteredOTP: string, 
  type: 'login' | 'register'
): Promise<{ valid: boolean; message?: string }> => {
  try {
    
    const { data, error } = await supabase
      .from('otps')
      .select()
      .eq('email', email)
      .eq('type', type)
      .single();
    
    if (error) {
      console.error('Supabase error verifying OTP:', error);
      if (error.code === 'PGRST116') {
        return { valid: false, message: 'رمز التحقق غير موجود' };
      }
      throw error;
    }
    
    
    if (!data) {
      return { valid: false, message: 'رمز التحقق غير صحيح' };
    }
    
    const expiresAt = new Date(data.expires_at);
    const now = new Date();
    
    if (now > expiresAt) {
      return { valid: false, message: 'انتهت صلاحية رمز التحقق' };
    }
    
    // Check if the entered OTP matches the stored OTP
    if (data.otp !== enteredOTP) {
      return { valid: false, message: 'رمز التحقق غير صحيح' };
    }
    
    // Delete the used OTP
    const { error: deleteError } = await supabase
      .from('otps')
      .delete()
      .eq('email', email)
      .eq('type', type);
      
    if (deleteError) {
      console.error('Error deleting used OTP:', deleteError);
    }
    
    return { valid: true };
  } catch (error) {
    console.error('Failed to verify OTP:', error);
    return { valid: false, message: 'حدث خطأ أثناء التحقق من الرمز' };
  }
};
