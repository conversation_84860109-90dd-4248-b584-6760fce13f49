
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Mail, ArrowLeft, Loader2 } from 'lucide-react';
import { useOTPLogin } from '@/hooks/auth/useOTPLogin';
import OTPVerification from './OTPVerification';

// Define the schema for email input
const emailSchema = z.object({
  email: z.string().email({ message: 'البريد الإلكتروني غير صالح' }),
});

type EmailFormData = z.infer<typeof emailSchema>;

const OTPLogin = ({ onBack }: { onBack: () => void }) => {
  const { 
    isLoading, 
    error, 
    email, 
    showVerification, 
    initiateOTPLogin, 
    handleSuccessfulVerification, 
    resetOTPLogin 
  } = useOTPLogin();

  const form = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: EmailFormData) => {
    await initiateOTPLogin(data.email);
  };

  // If in OTP verification mode, show the OTP verification component
  if (showVerification) {
    return (
      <OTPVerification
        email={email}
        type="login"
        onVerified={handleSuccessfulVerification}
        onBackToLogin={resetOTPLogin}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">تسجيل الدخول برمز التحقق</h1>
        <p className="text-gray-500 dark:text-gray-400">أدخل بريدك الإلكتروني لإرسال رمز التحقق</p>
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
          {error}
        </div>
      )}
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>البريد الإلكتروني</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                    <Input 
                      placeholder="<EMAIL>" 
                      {...field} 
                      type="email" 
                      autoComplete="email"
                      className="pl-10" 
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري الإرسال...
              </>
            ) : 'إرسال رمز التحقق'}
          </Button>
          
          <Button
            type="button"
            variant="ghost"
            className="w-full flex items-center justify-center gap-2"
            onClick={onBack}
          >
            <ArrowLeft className="h-4 w-4" /> العودة إلى طرق تسجيل الدخول الأخرى
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default OTPLogin;
