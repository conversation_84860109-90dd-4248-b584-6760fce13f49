
import React from 'react';
import { useVerificationStatus } from '@/hooks/useVerificationStatus';
import { Loader2 } from 'lucide-react';

export default function EmailVerificationHandler() {
  const { isVerifying } = useVerificationStatus();

  // This component will only show a loading state
  // The hook will handle redirecting based on verification results
  if (isVerifying) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg">جاري التحقق من البريد الإلكتروني...</p>
        </div>
      </div>
    );
  }

  // If not verifying, the hook will have redirected
  return null;
}
