import React from "react";
import { Link } from "react-router-dom";

export default function LoginFooter() {
  return (
    <>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">أو</span>
        </div>
      </div>

      <div className="flex flex-col space-y-2 text-center text-sm">
        <p>
          ليس لديك حساب؟{" "}
          <Link
            to="/register"
            className="font-semibold text-primary underline-offset-4 hover:underline"
          >
            إنشاء حساب
          </Link>
        </p>

        <p className="text-xs text-muted-foreground mt-2">
          باستخدامك للمنصة، فإنك توافق على{" "}
          <Link to="/privacy-policy" className="text-rfof-blue hover:underline">
            سياسة الخصوصية
          </Link>{" "}
          و{" "}
          <Link to="/terms" className="text-rfof-blue hover:underline">
            شروط الاستخدام
          </Link>
        </p>

        {/* <div className="mt-4 text-center text-xs text-muted-foreground">
          <p>بيانات التجربة:</p>
          <p className="mt-1">متجر إلكتروني: <EMAIL> / password123</p>
          <p>محل تجاري: <EMAIL> / password123</p>
          <p>مدير: <EMAIL> / admin123</p>
        </div> */}
      </div>
    </>
  );
}
