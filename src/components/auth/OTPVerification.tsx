
import React, { useState, useEffect } from 'react';
import { EmailDisplay } from './otp/EmailDisplay';
import { OTPVerificationForm, OTPFormValues } from './otp/OTPVerificationForm';
import { useOTPAuthentication } from '@/hooks/auth/useOTPAuthentication';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

interface OTPVerificationProps {
  email: string;
  type: 'register' | 'login'| 'recovery';
  onVerified: () => void;
  onBackToLogin: () => void;
  name?: string;
  maxAttempts?: number;
}

export default function OTPVerification({
  email,
  type,
  onVerified,
  onBackToLogin,
  maxAttempts = 5
}: OTPVerificationProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const { verifyOTP, sendOTP } = useOTPAuthentication();
  const navigate = useNavigate();

  // Session storage keys
  const cooldownKey = `${email}-${type}-otp-sent-time`;
  const attemptsKey = `${email}-${type}-otp-attempts`;
  
  // Set initial cooldown and attempts on first mount
  useEffect(() => {
    // Initialize resend cooldown
    const lastSentTime = sessionStorage.getItem(cooldownKey);
    if (lastSentTime) {
      const elapsedSeconds = Math.floor((Date.now() - parseInt(lastSentTime)) / 1000);
      const remainingCooldown = Math.max(60 - elapsedSeconds, 0);
      
      if (remainingCooldown > 0) {
        setResendCooldown(remainingCooldown);
        
        // Start the countdown
        const timer = setInterval(() => {
          setResendCooldown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        return () => clearInterval(timer);
      }
    }
    
    // Initialize attempts
    const savedAttempts = sessionStorage.getItem(attemptsKey);
    if (savedAttempts) {
      setAttempts(parseInt(savedAttempts));
    }
  }, [email, type, cooldownKey, attemptsKey]);

  const handleSubmit = async (values: OTPFormValues) => {
    if (isLoading) return;
    
    // Check if max attempts reached
    if (attempts >= maxAttempts) {
      setError('تم تجاوز الحد الأقصى للمحاولات. يرجى طلب رمز جديد.');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const success = await verifyOTP(email, values.otp, type as 'login' | 'register' | 'recovery');
      
      if (success) {
        // Reset attempts on success
        sessionStorage.removeItem(attemptsKey);
        toast.success('تم التحقق بنجاح');
        onVerified();
        navigate('/reset-password');

      } else {
        // Increment attempts and save
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        sessionStorage.setItem(attemptsKey, newAttempts.toString());
        
        if (newAttempts >= maxAttempts) {
          setError('تم تجاوز الحد الأقصى للمحاولات. يرجى طلب رمز جديد.');
        } else {
          setError('رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.');
        }
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      
      // Handle different error cases
      if (error.message?.includes('expire')) {
        setError('انتهت صلاحية رمز التحقق، يرجى طلب رمز جديد');
      } else if (error.message?.includes('invalid')) {
        // Increment attempts and save
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        sessionStorage.setItem(attemptsKey, newAttempts.toString());
        
        setError('رمز التحقق غير صحيح، يرجى التحقق والمحاولة مرة أخرى');
      } else {
        setError(error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0 || isResending) return;
    
    setIsResending(true);
    setError(null);
    
    try {
      await sendOTP(email, type as 'login' | 'register');
      
      // Reset attempts when sending new OTP
      setAttempts(0);
      sessionStorage.removeItem(attemptsKey);
      
      // Store the time when OTP was sent
      sessionStorage.setItem(cooldownKey, Date.now().toString());
      
      // Start cooldown timer (60 seconds)
      setResendCooldown(60);
      const timer = setInterval(() => {
        setResendCooldown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      toast.success('تم إرسال رمز تحقق جديد');
    } catch (error) {
      console.error('OTP resend error:', error);
      setError(error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى');
      
      toast.error('فشل في إرسال رمز جديد', {
        description: error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى'
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* <EmailDisplay email={email} type={type} /> */}
      
      <OTPVerificationForm
        onSubmit={handleSubmit}
        onResend={handleResendOTP}
        onBack={onBackToLogin}
        isLoading={isLoading}
        isResending={isResending}
        resendCooldown={resendCooldown}
        error={error}
        maxAttempts={maxAttempts}
        currentAttempts={attempts}
      />
    </div>
  );
}
