import React, { useState } from "react";
import { toast } from "sonner";
import { usePasswordLogin } from "@/hooks/auth/usePasswordLogin";
import { useOTPLogin } from "@/hooks/auth/useOTPLogin";
import OTPVerification from "./OTPVerification";
import LoginMethodSelector from "./LoginMethodSelector";
import PasswordLoginForm, { PasswordFormData } from "./PasswordLoginForm";

interface LoginFormProps {
  switchToRegister: () => void;
  switchToResetPassword: () => void;
}

// Define the login method type explicitly
type LoginMethod = "password" | "otp";

const LoginForm = ({
  switchToRegister,
  switchToResetPassword,
}: LoginFormProps) => {
  const [loginMethod, setLoginMethod] = useState<LoginMethod>("password");

  // Password login hook
  const {
    login,
    isLoading: isPasswordLoading,
    error: loginError,
    clearError,
  } = usePasswordLogin();

  // OTP login hook
  const {
    initiateOTPLogin,
    isLoading: isOTPLoading,
    error: otpError,
    email,
    showVerification,
    clearError: clearOTPError,
  } = useOTPLogin();

  const handleLoginMethodChange = (method: LoginMethod) => {
    setLoginMethod(method);
    // Clear errors when switching methods
    clearError();
    clearOTPError();
  };

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    const success = await login({
      email: data.email,
      password: data.password,
    });

    if (success) {
      toast.success("تم تسجيل الدخول بنجاح");
    }
  };

  // If showing OTP verification form
  if (loginMethod === "otp" && showVerification) {
    return (
      <p className="flex items-center justify-center text-black font-[900]">
        please check your email
      </p>
    );
  }

  // if (loginMethod === "otp") {
  //   return (
  //     <div className="space-y-6">
  //       <div className="space-y-2 text-center">
  //         <h1 className="text-3xl font-bold">تسجيل الدخول</h1>
  //         <p className="text-gray-500 dark:text-gray-400">
  //           مرحباً بعودتك! يرجى إدخال بريدك الإلكتروني
  //         </p>
  //       </div>

  //       {otpError && (
  //         <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
  //           {otpError}
  //         </div>
  //       )}

  //       <LoginMethodSelector
  //         currentMethod={loginMethod}
  //         onMethodChange={handleLoginMethodChange}
  //       />

  //       <form
  //         onSubmit={(e) => {
  //           e.preventDefault();
  //           const formData = new FormData(e.currentTarget);
  //           const email = formData.get("email") as string;
  //           initiateOTPLogin(email);
  //         }}
  //         className="space-y-4"
  //       >
  //         <div className="space-y-2">
  //           <label
  //             htmlFor="email"
  //             className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
  //           >
  //             البريد الإلكتروني
  //           </label>
  //           <input
  //             id="email"
  //             name="email"
  //             type="email"
  //             autoCapitalize="none"
  //             autoComplete="email"
  //             autoCorrect="off"
  //             required
  //             className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
  //           />
  //         </div>

  //         <button
  //           type="submit"
  //           disabled={isOTPLoading}
  //           className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
  //         >
  //           {isOTPLoading ? "جاري الإرسال..." : "إرسال لينك التحقق"}
  //         </button>

  //         <div className="text-center">
  //           <button
  //             type="button"
  //             onClick={() => handleLoginMethodChange("password")}
  //             className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
  //           >
  //             العودة إلى تسجيل الدخول بكلمة المرور
  //           </button>
  //         </div>
  //       </form>
  //     </div>
  //   );
  // }

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">تسجيل الدخول</h1>
        <p className="text-gray-500 dark:text-gray-400">
          مرحباً بعودتك! يرجى إدخال بيانات الدخول
        </p>
      </div>

      {loginError && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
          {loginError}
        </div>
      )}

      {/* <LoginMethodSelector
        currentMethod={loginMethod}
        onMethodChange={handleLoginMethodChange}
      /> */}

      <PasswordLoginForm
        onSubmit={handlePasswordSubmit}
        isLoading={isPasswordLoading}
        switchToRegister={switchToRegister}
        switchToResetPassword={switchToResetPassword}
      />
    </div>
  );
};

export default LoginForm;
