
import React from 'react';
import { FormItem, FormMessage } from '../ui/form';
import { ToggleGroup, ToggleGroupItem } from '../ui/toggle-group';
import { ShoppingCart, Store } from 'lucide-react';
import { UserRole } from '@/contexts/auth/types';

interface UserTypeSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
}

export function UserTypeSelector({ value, onValueChange }: UserTypeSelectorProps) {
  return (
    <FormItem className="space-y-4">
      <div className="text-center font-medium text-lg">أنا أملك</div>
      
      <ToggleGroup
        type="single"
        className="justify-center"
        value={value}
        onValueChange={(value) => {
          if (value) onValueChange(value);
        }}
      >
        <UserTypeCard 
          icon={<ShoppingCart className="h-10 w-10" />} 
          value="ecommerce" 
          title="متجر إلكتروني" 
          description="أبيع المنتجات عبر الإنترنت"
          isSelected={value === "ecommerce"}
        />
        
        <UserTypeCard 
          icon={<Store className="h-10 w-10" />} 
          value="store" 
          title="محل تجاري" 
          description="لدي محل تجاري فعلي"
          isSelected={value === "store"}
        />
      </ToggleGroup>
      
      <FormMessage />
    </FormItem>
  );
}

interface UserTypeCardProps {
  icon: React.ReactNode;
  value: string;
  title: string;
  description: string;
  isSelected: boolean;
}

function UserTypeCard({ icon, value, title, description, isSelected }: UserTypeCardProps) {
  return (
    <ToggleGroupItem 
      value={value}
      className={`
        flex-col p-4 m-2 h-40 w-40 border-2 transition-all duration-200
        ${isSelected 
          ? 'border-primary bg-primary/5 shadow-lg scale-105' 
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
      `}
      aria-label={title}
    >
      <div className={`
        rounded-full p-3 mb-2 transition-colors
        ${isSelected ? 'text-primary bg-primary/10' : 'text-gray-500 bg-gray-100'}
      `}>
        {icon}
      </div>
      <div className="font-medium text-lg">{title}</div>
      <div className="text-xs text-gray-500 mt-1">{description}</div>
    </ToggleGroupItem>
  );
}
