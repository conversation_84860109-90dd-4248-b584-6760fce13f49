import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { EyeIcon, EyeOffIcon, Lock } from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import AdminInitializer from "./AdminInitializer";

const formSchema = z.object({
  email: z.string().email({ message: "البريد الإلكتروني غير صالح" }),
  password: z
    .string()
    .min(6, { message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" }),
});

type FormData = z.infer<typeof formSchema>;

// معلومات تسجيل الدخول الافتراضية للمدير
const DEFAULT_ADMIN_EMAIL = "<EMAIL>";
const DEFAULT_ADMIN_PASSWORD = "Admin123!";

const AdminLoginForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // عرض رسالة للمستخدم لتوضيح أن عملية تسجيل الدخول جارية
      toast.info("جاري تسجيل الدخول...");

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        console.error("Login error:", error);

        setErrorMessage("البريد الإلكتروني أو كلمة المرور غير صحيحة");
        toast.error("فشل تسجيل الدخول");
        setIsLoading(false);
        return;
      }

      if (authData && authData.user) {
        // التحقق مباشرة من البيانات التعريفية للمستخدم
        const userRole = authData.user.user_metadata?.role;
        let isAdmin = userRole === "admin" || userRole === "sub-admin";
        const isActive = authData.user.user_metadata?.active !== false; // Default to true if undefined

        if (!isActive) {
          console.error("User account is deactivated");
          setErrorMessage("تم تعطيل هذا الحساب، يرجى التواصل مع المسؤول");
          await supabase.auth.signOut();
          setIsLoading(false);
          return;
        }

        // إذا لم تكن البيانات التعريفية متوفرة، نتحقق من جدول الملفات الشخصية
        if (!isAdmin) {
          try {
            const { data: profileData, error: profileError } = await supabase
              .from("profiles")
              .select("role, active")
              .eq("id", authData.user.id)
              .maybeSingle();

            if (profileError) {
              console.error("Error fetching profile:", profileError);
              setErrorMessage("حدث خطأ أثناء التحقق من معلومات المستخدم");
              setIsLoading(false);
              return;
            }

            if (profileData) {
              const profileRole = profileData?.role || "";
              const isAdminFromProfile =
                profileRole === "admin" || profileRole === "sub-admin";
              const isActiveFromProfile = profileData?.active !== false;

              if (!isActiveFromProfile) {
                console.error("User account is deactivated (from profile)");
                setErrorMessage("تم تعطيل هذا الحساب، يرجى التواصل مع المسؤول");
                await supabase.auth.signOut();
                setIsLoading(false);
                return;
              }

              if (isAdminFromProfile) {
                isAdmin = true;
                // تحديث البيانات التعريفية للمستخدم لتسهيل الوصول في المستقبل
                try {
                  await supabase.auth.updateUser({
                    data: {
                      role: profileRole,
                      permissions: [
                        "manage_users",
                        "approve_payments",
                        "manage_transactions",
                        "manage_admins",
                      ],
                      active: isActiveFromProfile,
                    },
                  });
                } catch (updateError) {
                  console.error("Error updating user metadata:", updateError);
                }
              }
            }
          } catch (err) {
            console.error("Error checking user profile:", err);
            setErrorMessage("حدث خطأ أثناء التحقق من معلومات المستخدم");
            setIsLoading(false);
            return;
          }
        }

        if (isAdmin) {
          toast.success("تم تسجيل الدخول بنجاح");
          navigate("/dashboard/admin");
          return;
        }

        console.error("User does not have admin role");
        setErrorMessage("ليس لديك صلاحية الوصول للوحة التحكم");

        await supabase.auth.signOut();
      } else {
        setErrorMessage("حدث خطأ ما، يرجى المحاولة مرة أخرى");
      }
    } catch (error) {
      console.error("Login error:", error);
      setErrorMessage(error.message || "حدث خطأ ما، يرجى المحاولة مرة أخرى");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // الدالة المساعدة لتسجيل دخول المشرف التلقائي (للاختبار فقط)
  const loginAsAdmin = () => {
    form.setValue("email", DEFAULT_ADMIN_EMAIL);
    form.setValue("password", DEFAULT_ADMIN_PASSWORD);
  };

  return (
    <div className="space-y-6 max-w-md mx-auto">
      {/* إضافة مكون تهيئة حساب المدير */}
      <AdminInitializer
        adminEmail={DEFAULT_ADMIN_EMAIL}
        adminPassword={DEFAULT_ADMIN_PASSWORD}
        adminName="مدير النظام"
      />

      <div className="space-y-2 text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-primary/10 p-3 rounded-full">
            <Lock className="h-10 w-10 text-primary" />
          </div>
        </div>
        <h1 className="text-3xl font-bold">تسجيل دخول الإدارة</h1>
        <p className="text-gray-500 dark:text-gray-400">
          منطقة محمية، يرجى تسجيل الدخول للوصول لوحة التحكم
        </p>
      </div>
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
          {errorMessage}
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>البريد الإلكتروني</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    {...field}
                    type="email"
                    autoComplete="email"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>كلمة المرور</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="********"
                      autoComplete="current-password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={toggleShowPassword}
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-transparent"
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-4 w-4" />
                      ) : (
                        <EyeIcon className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "جاري التحميل..." : "تسجيل الدخول"}
          </Button>

          {/* زر للتسجيل التلقائي كمشرف (للاختبار فقط) */}
          {/* <Button 
            type="button" 
            variant="outline" 
            className="w-full mt-2" 
            onClick={loginAsAdmin}
          >
            الدخول كمشرف (بيانات تجريبية)
          </Button> */}
        </form>
      </Form>
      <div className="mt-4 text-center text-sm">
        <Button variant="link" className="p-0" onClick={() => navigate("/")}>
          العودة للصفحة الرئيسية
        </Button>
      </div>
    </div>
  );
};

export default AdminLoginForm;
