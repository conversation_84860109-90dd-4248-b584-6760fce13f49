import { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, Mail, ArrowLeft } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "@/hooks/toast";

const resetPasswordSchema = z.object({
  email: z.string().email({
    message: "يرجى إدخال بريد إلكتروني صحيح",
  }),
});

interface PasswordResetFormProps {
  onBackToLogin: () => void;
}

export default function PasswordResetForm({
  onBackToLogin,
}: PasswordResetFormProps) {
  const [isResetting, setIsResetting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { resetPassword, authError } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const resetForm = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onResetPassword = async (
    values: z.infer<typeof resetPasswordSchema>
  ) => {
    setIsResetting(true);
    setError(null);

    try {
      const success = await resetPassword(values.email);
      sessionStorage.setItem("email", values.email);
      if (success) {
        console.log("Password reset email sent successfully");
        setIsSuccess(true);
        toast({
          title: "تم إرسال رابط إعادة تعيين كلمة المرور",
          description:
            "يرجى التحقق من بريدك الإلكتروني لإعادة تعيين كلمة المرور الخاصة بك",
        });
      } else {
        // Display the specific error from auth context
        console.error("Password reset failed:", authError);
        setError(authError || "فشل إرسال لينك التحقق");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      setError(
        error.message ||
          "حدث خطأ أثناء محاولة إرسال لينك التحقق لاعادة تعيين كلمة المرور"
      );
    } finally {
      setIsResetting(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="space-y-4 text-center">
        <div className="rounded-full bg-green-50 p-3 w-20 h-20 mx-auto flex items-center justify-center">
          <Mail className="h-10 w-10 text-green-500" />
        </div>
        <h2 className="text-xl font-semibold">تم إرسال البريد الإلكتروني</h2>
        <p className="text-muted-foreground">
          لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى
          التحقق من صندوق الوارد الخاص بك.
        </p>
        <Button
          variant="outline"
          className="w-full mt-4"
          disabled={isResetting}
          onClick={() => {
            onResetPassword({
              email: sessionStorage.getItem("email") || "",
            });
          }}
        >
          {isResetting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              جاري المعالجة...
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              اعادة راسال رابط تعيين كلمة المرور
            </>
          )}
        </Button>

        <Button
          variant="outline"
          className="w-full mt-4"
          onClick={onBackToLogin}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          العودة إلى تسجيل الدخول
        </Button>
      </div>
    );
  }

  return (
    <>
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...resetForm}>
        <form
          onSubmit={resetForm.handleSubmit(onResetPassword)}
          className="space-y-4"
        >
          <FormField
            control={resetForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>البريد الإلكتروني</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    {...field}
                    autoComplete="email"
                    disabled={isResetting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col space-y-2">
            <Button type="submit" className="w-full" disabled={isResetting}>
              {isResetting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> جاري
                  المعالجة...
                </>
              ) : (
                "إرسال رابط إعادة التعيين"
              )}
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="w-full"
              onClick={onBackToLogin}
              disabled={isResetting}
            >
              العودة إلى تسجيل الدخول
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
