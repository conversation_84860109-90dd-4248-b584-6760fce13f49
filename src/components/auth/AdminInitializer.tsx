
import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

type AdminInitializerProps = {
  adminEmail: string;
  adminPassword: string;
  adminName?: string;
}

const AdminInitializer: React.FC<AdminInitializerProps> = ({ 
  adminEmail, 
  adminPassword, 
  adminName = "مدير النظام" 
}) => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeAdmin = async () => {
    setIsInitializing(true);
    setError(null);
    
    try {
      const { data, error } = await supabase.functions.invoke("create-admin", {
        body: {
          email: adminEmail,
          password: adminPassword,
          name: adminName
        }
      });
      
      if (error) {
        console.error("Error initializing admin:", error);
        setError("فشل إنشاء حساب المدير");
        toast.error("فشل إنشاء حساب المدير", { description: error.message });
        return;
      }
      
      if (data.success) {
        setIsInitialized(true);
        toast.success(data.isNewUser ? "تم إنشاء حساب المدير بنجاح" : "تم تحديث حساب المدير بنجاح");
      } else {
        setError(data.error || "حدث خطأ أثناء إنشاء حساب المدير");
      }

    } catch (err) {

      console.error("Error initializing admin:", err);
      setError(err.message || "حدث خطأ غير متوقع");
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div className="rounded-md bg-amber-50 border border-amber-200 p-4 shadow-sm">
      <h3 className="font-medium text-amber-800">إعداد حساب مدير النظام</h3>
      <p className="text-sm text-amber-700 mb-4 mt-1">
        {!isInitialized 
          ? "لم يتم إنشاء حساب مدير النظام بعد، أو تحتاج لتحديث بياناته." 
          : "تم إنشاء حساب مدير النظام بنجاح."
        }
      </p>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm mb-3">
          {error}
        </div>
      )}
      
      <Button
        type="button"
        variant="default"
        size="sm"
        onClick={initializeAdmin}
        disabled={isInitializing}
      >
        {isInitializing 
          ? "جاري الإنشاء..." 
          : isInitialized 
            ? "إعادة تهيئة حساب المدير" 
            : "إنشاء حساب مدير النظام"
        }
      </Button>
    </div>
  );
};

export default AdminInitializer;
