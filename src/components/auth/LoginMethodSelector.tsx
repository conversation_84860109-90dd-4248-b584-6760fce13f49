import { Button } from '@/components/ui/button';
import { KeyRound, Smartphone } from 'lucide-react';
type LoginMethod = 'password' | 'otp';
interface LoginMethodSelectorProps {
  currentMethod: LoginMethod;
  onMethodChange: (method: LoginMethod) => void;
}
const LoginMethodSelector = ({
  currentMethod,
  onMethodChange
}: LoginMethodSelectorProps) => {
  return <div className="flex gap-2 p-1 bg-muted rounded-lg mb-4">
      <Button type="button" variant={currentMethod === 'password' ? 'secondary' : 'ghost'} className="flex-1 flex items-center justify-center gap-2" onClick={() => onMethodChange('password')}>
        <KeyRound className="h-4 w-4" />
        كلمة المرور
      </Button>
      <Button type="button" variant={currentMethod === 'otp' ? 'secondary' : 'ghost'} onClick={() => onMethodChange('otp')} className="flex-1 flex items-center justify-center gap-2 font-normal text-base">
        <Smartphone className="h-4 w-4" />
        لينك التحقق
      </Button>
    </div>;
};
export default LoginMethodSelector;