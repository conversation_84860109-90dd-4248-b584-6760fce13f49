
import React from 'react';
import { Transaction, TransactionStatus } from '@/types';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import {
  ArrowUpRight,
  ArrowDownLeft,
  CreditCard,
  Clock,
  CircleCheck,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import EmptyState from '@/components/EmptyState';
import { useUserName } from '@/hooks/useUserName';

interface TransactionsListProps {
  transactions: Transaction[];
  onAddTransaction?: () => void;
}

// Helper to render a user name quickly using the hook
function RenderUserName({ userId }: { userId?: string }) {
  const { data: userName, isLoading } = useUserName(userId);
  if (!userId) return <span>-</span>;
  if (isLoading) return <span className="text-gray-400">•••</span>;
  return <span>{userName || "-"}</span>;
}

export const TransactionsList: React.FC<TransactionsListProps> = ({ 
  transactions,
  onAddTransaction
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: ar });
    } catch (error) {
      return dateString;
    }
  };
  
  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">تحت المعالجة</Badge>;
      case 'approved':
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">مكتملة</Badge>;
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">فشلت</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">مرفوضة</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">ملغية</Badge>;
      default:
        return <Badge variant="outline">غير معروفة</Badge>;
    }
  };

  const getTransactionIcon = (type: string, status: string) => {
    switch (type) {
      case 'deposit':
        return status === 'approved' || status === 'completed' ? 
          <CircleCheck className="h-6 w-6 text-green-500" /> : 
          <Clock className="h-6 w-6 text-yellow-500" />;
      case 'withdrawal':
      case 'withdraw':
        return status === 'approved' || status === 'completed' ? 
          <ArrowUpRight className="h-6 w-6 text-blue-500" /> : 
          <Clock className="h-6 w-6 text-yellow-500" />;
      case 'fee':
        return <CreditCard className="h-6 w-6 text-purple-500" />;
      case 'commission':
        return <CreditCard className="h-6 w-6 text-orange-500" />;
      default:
        return <CreditCard className="h-6 w-6 text-gray-500" />;
    }
  };

  const getTransactionTypeText = (type: string) => {
    switch (type) {
      case 'deposit':
        return 'إيداع';
      case 'withdrawal':
      case 'withdraw':
        return 'سحب';
      case 'fee':
        return 'رسوم';
      case 'commission':
        return 'عمولة';
      case 'payment':
        return 'دفعة';
      case 'transfer':
        return 'تحويل';
      case 'refund':
        return 'استرداد';
      case 'hosting_fee':
        return 'رسوم استضافة';
      default:
        return type;
    }
  };
  
  if (transactions.length === 0) {
    return (
      <EmptyState
        title="مافي عمليات مالية"
        description="ما سويت أي عمليات مالية للحين"
        icon={CreditCard}
        action={onAddTransaction ? {
          label: "إضافة أول إيداع",
          onClick: onAddTransaction,
        } : undefined}
      />
    );
  }

  return (
    <div className="space-y-4">
      {transactions.map((transaction) => (
        <Card key={transaction.id} className="overflow-hidden">
          <div className="flex items-start p-6">
            <div className="flex-shrink-0 ml-4">
              {getTransactionIcon(transaction.type, transaction.status)}
            </div>
            <div className="flex-grow">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium">
                    {getTransactionTypeText(transaction.type)}
                  </h3>
                  <div className="flex text-xs gap-2 mb-1">
                    <span className="text-muted-foreground font-medium">من:</span>
                    <RenderUserName userId={transaction.from_user_id} />
                    <span className="text-muted-foreground font-medium">إلى:</span>
                    <RenderUserName userId={transaction.to_user_id} />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {transaction.description}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatDate(transaction.created_at)}
                  </p>
                </div>
                <div className="text-right">
                  <div className={`font-bold ${
                    transaction.type === 'deposit' && (transaction.status === 'approved' || transaction.status === 'completed')
                      ? 'text-green-600' 
                      : (transaction.type === 'withdrawal' || transaction.type === 'withdraw' || transaction.type === 'fee')
                      ? 'text-red-600' 
                      : ''
                  }`}>
                    {transaction.type === 'deposit' ? '+' : (transaction.type === 'withdrawal' || transaction.type === 'withdraw' || transaction.type === 'fee') ? '-' : ''}
                    {transaction.amount} ريال
                  </div>
                  <div className="mt-1">
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default TransactionsList;
