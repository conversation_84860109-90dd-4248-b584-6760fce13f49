
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import NavItem, { NavItemProps } from './NavItem';
import { NavItem as NavItemType } from './types';

interface MobileMenuProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  navItems: NavItemType[];
  onItemClick: () => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onOpenChange,
  navItems,
  onItemClick,
}) => {
  // Safely convert React nodes to string
  const getLabelString = (label: React.ReactNode): string => {
    if (typeof label === 'string') return label;
    if (typeof label === 'number') return String(label);
    if (typeof label === 'boolean') return String(label);
    // For complex React elements or objects, return a fallback string
    return 'قائمة';
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">القائمة</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="pr-0 z-[100] w-[75%] sm:max-w-sm">
        <SheetHeader className="pr-6">
          <SheetTitle>القائمة</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col gap-4 py-4 pr-6">
          {navItems.map((item, index) => {
            const navItemProps: NavItemProps = {
              href: item.href,
              label: getLabelString(item.label),
              icon: item.icon,
              onClick: onItemClick,
              roles: item.roles,
              permissions: item.permissions
            };
            
            return (
              <NavItem
                key={index}
                {...navItemProps}
              />
            );
          })}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileMenu;
