
import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { UserRole, AdminPermission } from '@/types';

// تحديث واجهة NavItemProps لتتضمن roles و permissions
export interface NavItemProps {
  href: string;
  label: string;
  icon: React.ReactNode | null;
  onClick?: () => void;
  roles?: UserRole[];
  permissions?: AdminPermission[];
}

const NavItem: React.FC<NavItemProps> = ({ 
  href, 
  label, 
  icon, 
  onClick 
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn("w-full justify-start")}
      asChild
      onClick={onClick}
    >
      <Link to={href}>
        {icon && <span>{icon}</span>}
        {label}
      </Link>
    </Button>
  );
};

export default NavItem;
