import React from "react";
import { <PERSON> } from "react-router-dom";
import { LogOut, Package, Settings, User, Wallet } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { UserProfile } from "@/contexts/auth/types";
import { AvatarFallback, AvatarImage, Avatar } from "../ui/avatar";

interface UserMenuProps {
  user: UserProfile | null;
  logout: () => void;
}

export const UserMenu: React.FC<UserMenuProps> = ({ user, logout }) => {
  // Define menu items
  const userNavItems = [
    {
      href: '/dashboard',
      label: 'لوحة التحكم',
      icon: <Package className="h-4 w-4" />,
    },
    {
      href: "/dashboard/profile",
      label: "الملف الشخصي",
      icon: <User className="h-4 w-4" />,
    },
    {
      href: "/dashboard/wallet",
      label: "المحفظة",
      icon: <Wallet className="h-4 w-4" />,
    },
    {
      href: "/dashboard/settings",
      label: "الإعدادات",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      label: "تسجيل الخروج",
      icon: <LogOut className="h-4 w-4" />,
      onClick: logout,
    },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full border h-8 w-8"
        >
          <span className="sr-only">قائمة المستخدم</span>
          <Avatar className="h-9 w-9">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user?.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {userNavItems.map((item, index) => (
          <DropdownMenuItem key={index} asChild={!item.onClick}>
            {item.onClick ? (
              <button
                className="flex w-full cursor-pointer items-center"
                onClick={item.onClick}
              >
                {item.icon}
                <span className="mr-2">{item.label}</span>
              </button>
            ) : (
              <Link
                to={item.href!}
                className="flex cursor-pointer items-center gap-2"
              >
                {item.icon}
                <span className="mr-2">{item.label}</span>
              </Link>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenu;
