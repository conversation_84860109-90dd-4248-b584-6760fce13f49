// imports
import {
  Home,
  ShoppingBag,
  Store,
  ScrollText,
  ShoppingCart,
  Wallet,
  Bell,
  Settings,
  Users,
  Package,
  LayoutDashboard,
  Clipboard,
  BarChart,
  UserCog,
  FileCheck,
  CreditCard,
} from "lucide-react";

import { UserRole, AdminPermission } from "@/types";
import { NavItem } from "./types";
import { LucideIcon } from "lucide-react";
import { useAuth } from "@/contexts/auth";

export type SupportedIcon =
  | "Home"
  | "ShoppingBag"
  | "Store"
  | "ScrollText"
  | "ShoppingCart"
  | "Wallet"
  | "Bell"
  | "Settings"
  | "Users"
  | "Package"
  | "LayoutDashboard"
  | "Clipboard"
  | "BarChart"
  | "UserCog"
  | "FileCheck"
  | "CreditCard";

// Map string name to actual Lucide icon
export const getIconComponent = (
  iconName: SupportedIcon | string
): LucideIcon | null => {
  const iconMap: Record<SupportedIcon, LucideIcon> = {
    Home,
    ShoppingBag,
    Store,
    ScrollText,
    ShoppingCart,
    Wallet,
    Bell,
    Settings,
    Users,
    Package,
    LayoutDashboard,
    Clipboard,
    BarChart,
    UserCog,
    FileCheck,
    CreditCard,
  };

  return iconMap[iconName as SupportedIcon] || null;
};

export const useNavItems = () => {
  // ✅ Common (shared) navigation

  const { user } = useAuth();
  const commonItems: NavItem[] = [
    {
      href: "/dashboard",
      label: "لوحة التحكم",
      icon: "LayoutDashboard",
      roles: ["store", "ecommerce", "admin", "sub-admin"] as UserRole[],
    },
  ];

  // ✅ Admin dashboard
  const adminItems: NavItem[] = [
    {
      href: "/dashboard/admin",
      label: "لوحة الإحصائيات",
      icon: "BarChart",
      roles: ["admin", "sub-admin"],
    },
    {
      href: "/dashboard/admin/users",
      label: "إدارة المستخدمين",
      icon: "Users",
      roles: ["admin", "sub-admin"],
      permissions: ["manage_users", "read:users"] as AdminPermission[],
    },
    {
      href: "/dashboard/admin/admins",
      label: "إدارة المسؤولين",
      icon: "UserCog",
      roles: ["admin"],
      permissions: ["manage_admins"] as AdminPermission[],
    },
    {
      href: "/dashboard/admin/stores",
      label: "إدارة المتاجر",
      icon: "Store",
      roles: ["admin", "sub-admin"],
      permissions: ["manage_stores"] as AdminPermission[],
    },
    {
      href: "/dashboard/admin/products",
      label: "إدارة المنتجات",
      icon: "Package",
      roles: ["admin", "sub-admin"],
      permissions: ["manage_products", "read:products"] as AdminPermission[],
    },
    {
      href: "/dashboard/admin/hosting-requests",
      label: "طلبات العرض",
      icon: "FileCheck",
      roles: ["admin", "sub-admin"],
      permissions: ["manage_transactions"] as AdminPermission[],
    },
    {
      href: "/dashboard/admin/transactions",
      label: "المعاملات المالية",
      icon: "CreditCard",
      roles: ["admin", "sub-admin"],
      permissions: ["manage_transactions"] as AdminPermission[],
    },
  ];

  // ✅ Ecommerce role
  const ecommerceItems: NavItem[] = [
    {
      href: "/dashboard/products",
      label: "المنتجات",
      icon: "Package",
      roles: ["ecommerce", "admin"],
    },
    {
      href: "/dashboard/stores",
      label: "قائمة المحلات التجارية",
      icon: "Store",
      roles: ["ecommerce"],
    },
    {
      href: "/dashboard/hosting-requests",
      label: "طلبات العرض المرسلة",
      icon: "ScrollText",
      roles: ["ecommerce"],
    },
    {
      href: "/dashboard/wallet",
      label: "المحفظة",
      icon: "Wallet",
      roles: ["ecommerce"],
    },

    {
      href: "/dashboard/legal-details",
      label: "البيانات القانونية",
      icon: "FileCheck",
      roles: ["ecommerce"],
    },
    {
      href: "/dashboard/notifications",
      label: "الإشعارات",
      icon: "Bell",
      roles: ["ecommerce"],
    },
  ];

  // ✅ Store role
  const storeItems: NavItem[] = [
    {
      href: "/dashboard/store-inventory",
      label: "مخزون المنتجات",
      icon: "Clipboard",
      roles: ["store"],
    },
    {
      href: "/dashboard/hosting-requests",
      label: "طلبات عرض المنتجات",
      icon: "ShoppingCart",
      roles: ["store"],
    },
    {
      href: "/dashboard/myspaces",
      label: "مساحتي",
      icon: "ShoppingBag",
      roles: ["store"],
    },
    {
      href: "/dashboard/store-data",
      label: "بيانات المتجر",
      icon: "FileCheck",
      roles: ["store"],
    },

    // {
    //   href: "/dashboard/branches",
    //   label: "الفروع",
    //   icon: "Store",
    //   roles: ["store"],
    // },
    {
      href: "/dashboard/wallet",
      label: "المحفظة",
      icon: "Wallet",
      roles: ["store"],
    },
    // {
    //   href: "/dashboard/employees",
    //   label: "الموظفين",
    //   icon: "Users",
    //   roles: ["store"],
    // },
    {
      href: "/dashboard/legal-details",
      label: "البيانات القانونية",
      icon: "FileCheck",
      roles: ["store"],
    },
    {
      href: "/dashboard/notifications",
      label: "الإشعارات",
      icon: "Bell",
      roles: ["store"],
    },
    {
      href: "/dashboard/invoices",
      label: "الفواتير",
      icon: "Clipboard",
      roles: ["ecommerce"],
    },
  ];

  // ✅ Settings (available to all)
  const settingsItem: NavItem = {
    href: "/dashboard/settings",
    label: "الإعدادات",
    icon: "Settings",
  };

  return {
    commonItems,
    adminItems,
    ecommerceItems,
    storeItems,
    settingsItem,
  };
};

export default useNavItems;
