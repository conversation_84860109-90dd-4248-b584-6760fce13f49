
import React, { useState } from 'react';
import { User, UserRole, AdminPermission } from '@/types';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Edit, Trash2, Shield, MoreH<PERSON>zontal, Ban, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { translateRoleToArabic, getRoleColor } from '@/utils/roleUtils';
import { toast } from 'sonner';

interface UserManagementTableProps {
  users: User[];
  isLoading: boolean;
  onUpdateUser: (id: string, userData: Partial<User>) => Promise<boolean>;
  onDeleteUser: (id: string) => Promise<boolean>;
  onChangeRole: (id: string, role: UserRole) => Promise<boolean>;
  onToggleActive: (id: string, active: boolean) => Promise<boolean>;
  currentUserRole?: UserRole;
}

const UserManagementTable: React.FC<UserManagementTableProps> = ({
  users,
  isLoading,
  onUpdateUser,
  onDeleteUser,
  onChangeRole,
  onToggleActive,
  currentUserRole
}) => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [editUserData, setEditUserData] = useState<Partial<User>>({});
  const [selectedRole, setSelectedRole] = useState<UserRole>('ecommerce');
  const { user: currentUser } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleEditUser = async () => {
    if (!selectedUser) return;
    setIsProcessing(true);
    
    try {
      const success = await onUpdateUser(selectedUser.id, editUserData);
      if (success) {
        toast.success('تم تحديث بيانات المستخدم بنجاح');
        setIsEditDialogOpen(false);
      } else {
        toast.error('فشل تحديث بيانات المستخدم');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('حدث خطأ أثناء تحديث بيانات المستخدم');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    setIsProcessing(true);
    
    try {
      const success = await onDeleteUser(selectedUser.id);
      if (success) {
        toast.success('تم حذف المستخدم بنجاح');
        setIsDeleteDialogOpen(false);
      } else {
        toast.error('فشل حذف المستخدم');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('حدث خطأ أثناء حذف المستخدم');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleChangeRole = async () => {
    if (!selectedUser) return;
    setIsProcessing(true);
    
    try {
      const success = await onChangeRole(selectedUser.id, selectedRole);
      if (success) {
        toast.success(`تم تغيير دور المستخدم إلى ${translateRoleToArabic(selectedRole)} بنجاح`);
        setIsRoleDialogOpen(false);
      } else {
        toast.error('فشل تغيير دور المستخدم');
      }
    } catch (error) {
      console.error('Error changing user role:', error);
      toast.error('حدث خطأ أثناء تغيير دور المستخدم');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleToggleActive = async (userId: string, currentActive: boolean) => {
    setIsProcessing(true);
    
    try {
      const success = await onToggleActive(userId, !currentActive);
      if (success) {
        toast.success(currentActive ? 'تم تعطيل المستخدم بنجاح' : 'تم تفعيل المستخدم بنجاح');
      } else {
        toast.error('فشل تغيير حالة المستخدم');
      }
    } catch (error) {
      console.error('Error toggling user active state:', error);
      toast.error('حدث خطأ أثناء تغيير حالة المستخدم');
    } finally {
      setIsProcessing(false);
    }
  };

  const openEditDialog = (user: User) => {
    setSelectedUser(user);
    setEditUserData({
      name: user.name,
      phone_number: user.phone_number || '',
      address: user.address || '',
      legal_activity: user.legal_activity || '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (user: User) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const openRoleDialog = (user: User) => {
    setSelectedUser(user);
    setSelectedRole(user.role as UserRole || 'ecommerce');
    setIsRoleDialogOpen(true);
  };

  // تحديد ما إذا كان بإمكان المستخدم الحالي تعديل مستخدم آخر
  const canManageUser = (userToManage: User): boolean => {
    // لا يمكن للمستخدم تعديل نفسه من هذه الصفحة
    if (currentUser?.id === userToManage.id) return false;
    
    // المشرف الرئيسي يمكنه تعديل أي شخص
    if (currentUserRole === 'admin') return true;
    
    // المشرف الفرعي يمكنه تعديل المتاجر والمتاجر الإلكترونية فقط
    if (currentUserRole === 'sub-admin') {
      return userToManage.role !== 'admin' && userToManage.role !== 'sub-admin';
    }
    
    // أي دور آخر لا يمكنه التعديل
    return false;
  };

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>المستخدم</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>الدور</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>رقم الهاتف</TableHead>
            <TableHead>تاريخ الإنشاء</TableHead>
            <TableHead>الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                جاري تحميل البيانات...
              </TableCell>
            </TableRow>
          ) : users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                لا يوجد مستخدمين
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id} className={user.active === false ? "opacity-70" : ""}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback>{user.name?.slice(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div className="font-medium">{user.name}</div>
                  </div>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Badge className={getRoleColor(user.role as UserRole)}>
                    {translateRoleToArabic(user.role as UserRole)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.active !== false ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      نشط
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      معطل
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{user.phone_number || "-"}</TableCell>
                <TableCell>{new Date(user.created_at).toLocaleDateString('ar-SA')}</TableCell>
                <TableCell>
                  {canManageUser(user) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">إجراءات</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(user)}>
                          <Edit className="h-4 w-4 ml-2" />
                          تعديل البيانات
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openRoleDialog(user)}>
                          <Shield className="h-4 w-4 ml-2" />
                          تغيير الدور
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleActive(user.id, user.active !== false)}>
                          {user.active !== false ? (
                            <>
                              <Ban className="h-4 w-4 ml-2" />
                              تعطيل المستخدم
                            </>
                          ) : (
                            <>
                              <CheckCircle className="h-4 w-4 ml-2" />
                              تفعيل المستخدم
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => openDeleteDialog(user)} 
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="h-4 w-4 ml-2" />
                          حذف المستخدم
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* حوار تعديل المستخدم */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription>
              تعديل معلومات {selectedUser?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                value={editUserData.name || ''}
                onChange={(e) => setEditUserData({ ...editUserData, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone_number" className="text-right">
                رقم الهاتف
              </Label>
              <Input
                id="phone_number"
                value={editUserData.phone_number || ''}
                onChange={(e) => setEditUserData({ ...editUserData, phone_number: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="address" className="text-right">
                العنوان
              </Label>
              <Input
                id="address"
                value={editUserData.address || ''}
                onChange={(e) => setEditUserData({ ...editUserData, address: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="legal_activity" className="text-right">
                النشاط التجاري
              </Label>
              <Input
                id="legal_activity"
                value={editUserData.legal_activity || ''}
                onChange={(e) => setEditUserData({ ...editUserData, legal_activity: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              إلغاء
            </Button>
            <Button type="submit" onClick={handleEditUser} disabled={isProcessing}>
              {isProcessing ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار حذف المستخدم */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>حذف المستخدم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف المستخدم {selectedUser?.name}؟
              لا يمكن التراجع عن هذه العملية.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleDeleteUser} disabled={isProcessing}>
              {isProcessing ? 'جاري الحذف...' : 'حذف المستخدم'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار تغيير الدور */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>تغيير دور المستخدم</DialogTitle>
            <DialogDescription>
              تغيير دور المستخدم {selectedUser?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                الدور
              </Label>
              <Select
                value={selectedRole}
                onValueChange={(value: UserRole) => setSelectedRole(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر الدور" />
                </SelectTrigger>
                <SelectContent>
                  {/* يظهر خيار مشرف النظام فقط للمشرفين الرئيسيين */}
                  {currentUserRole === 'admin' && (
                    <>
                      <SelectItem value="admin">مشرف النظام</SelectItem>
                      <SelectItem value="sub-admin">مشرف فرعي</SelectItem>
                    </>
                  )}
                  <SelectItem value="store">متجر تقليدي</SelectItem>
                  <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
              إلغاء
            </Button>
            <Button type="submit" onClick={handleChangeRole} disabled={isProcessing}>
              {isProcessing ? 'جاري التغيير...' : 'تغيير الدور'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UserManagementTable;
