
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { createOrUpdateAdmin } from '@/services/user/updateUserMetadata';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const formSchema = z.object({
  email: z.string().email({ message: 'البريد الإلكتروني غير صالح' }),
  name: z.string().min(2, { message: 'الاسم يجب أن يكون حرفين على الأقل' }),
});

type FormData = z.infer<typeof formSchema>;

const AdminRoleAssignForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      name: 'مدير النظام',
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      const result = await createOrUpdateAdmin(data.email, data.name);
      
      if (result.error) {
        setErrorMessage(result.error);
        toast.error('فشل تعيين صلاحية المدير', {
          description: result.error,
        });
      } else {
        toast.success('تم تعيين صلاحية المدير بنجاح', {
          description: `تم تعيين ${data.email} كمدير للنظام`,
        });
        form.reset();
      }
    } catch (error: any) {
      console.error("Error assigning admin role:", error);
      setErrorMessage(error.message || 'حدث خطأ ما، يرجى المحاولة مرة أخرى');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>تعيين صلاحية مدير</CardTitle>
        <CardDescription>قم بتعيين صلاحية مدير للمستخدم باستخدام البريد الإلكتروني</CardDescription>
      </CardHeader>
      <CardContent>
        {errorMessage && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-3 mb-4 rounded-md text-sm">
            {errorMessage}
          </div>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>البريد الإلكتروني</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} type="email" autoComplete="email" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الاسم</FormLabel>
                  <FormControl>
                    <Input placeholder="مدير النظام" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'جاري التحميل...' : 'تعيين صلاحية مدير'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AdminRoleAssignForm;
