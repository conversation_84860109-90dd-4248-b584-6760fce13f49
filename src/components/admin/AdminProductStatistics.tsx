
import React, { useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Product } from '@/types';

interface ProductStatisticsProps {
  products: Product[];
  isLoading?: boolean;
}

export default function AdminProductStatistics({ products, isLoading = false }: ProductStatisticsProps) {
  const productStats = useMemo(() => {
    if (!products?.length) return [];

    const statusCounts = {
      pending: 0,
      approved: 0,
      rejected: 0,
      other: 0
    };

    products.forEach(product => {
      if (product.status === 'approved') statusCounts.approved++;
      else if (product.status === 'rejected') statusCounts.rejected++;
      else if (product.status === 'pending') statusCounts.pending++;
      else statusCounts.other++;
    });

    return [
      { name: 'معلق', value: statusCounts.pending, color: '#f59e0b' },
      { name: 'مقبول', value: statusCounts.approved, color: '#10b981' },
      { name: 'مرفوض', value: statusCounts.rejected, color: '#ef4444' },
      { name: 'أخرى', value: statusCounts.other, color: '#6b7280' }
    ].filter(item => item.value > 0); // Only include statuses with values greater than 0
  }, [products]);

  const config = {
    pending: {
      theme: { light: '#f59e0b', dark: '#f59e0b' },
      label: 'معلق'
    },
    approved: {
      theme: { light: '#10b981', dark: '#10b981' },
      label: 'مقبول'
    },
    rejected: {
      theme: { light: '#ef4444', dark: '#ef4444' },
      label: 'مرفوض'
    },
    other: {
      theme: { light: '#6b7280', dark: '#6b7280' },
      label: 'أخرى'
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>إحصائيات المنتجات</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">جاري تحميل البيانات...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!productStats.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>إحصائيات حالة المنتجات</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">لا توجد بيانات للعرض</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>إحصائيات حالة المنتجات</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ChartContainer config={config}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={productStats}>
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="value" label={{ position: 'top' }}>
                  {productStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
}
