
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { User } from "@/types/user";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth";
import { Loader2, Check, ChevronsUpDown } from "lucide-react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

interface AdminFundsTransferProps {
  onSuccess?: () => void;
}

export const AdminFundsTransfer: React.FC<AdminFundsTransferProps> = ({
  onSuccess,
}) => {
  const { user } = useAuth();
  const [fromUserId, setFromUserId] = useState<string>("");
  const [toUserId, setToUserId] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [users, setUsers] = useState<any[]>([]); // Fixed role error by using any[]
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isTransferring, setIsTransferring] = useState<boolean>(false);
  const [fromUserOpen, setFromUserOpen] = useState(false);
  const [toUserOpen, setToUserOpen] = useState(false);

  React.useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from("profiles")
          .select("id, name, email, role, balance")
          .order("name");

        if (error) {
          throw error;
        }
        setUsers((data as any[]) || []);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("حدث خطأ أثناء جلب بيانات المستخدمين");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleTransfer = async () => {
    if (!fromUserId || !toUserId || !amount || !description) {
      toast.error("يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    if (fromUserId === toUserId) {
      toast.error("لا يمكن التحويل إلى نفس الحساب");
      return;
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      toast.error("يرجى إدخال مبلغ صالح أكبر من صفر");
      return;
    }

    const fromUserBalance = getUserBalance(fromUserId);
    if (amountValue > fromUserBalance) {
      toast.error("الرصيد غير كافٍ في الحساب المصدر");
      return;
    }

    setIsTransferring(true);
    console.log("Transferring funds:", {
      fromUserId,
      toUserId,
      amount: amountValue,
      description,
      adminId: user?.id,
    });

    try {
      const { error } = await supabase.functions.invoke("admin-transfer-funds", {
        body: {
          fromUserId,
          toUserId,
          amount: amountValue,
          description,
          adminId: user?.id,
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      toast.success("تم تحويل المبلغ بنجاح");

      setFromUserId("");
      setToUserId("");
      setAmount("");
      setDescription("");

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error transferring funds:", error);
      toast.error(error.message || "حدث خطأ أثناء تحويل المبلغ");
    } finally {
      setIsTransferring(false);
    }
  };

  const getUserBalance = (userId: string): number => {
    const user = users.find((u) => u.id === userId);
    return user?.balance || 0;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>تحويل الأموال بين الحسابات</CardTitle>
        <CardDescription>
          قم بتحويل الأموال من حساب إلى آخر بصفتك مسؤول النظام
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fromUser">تحويل من (المُرسِل)</Label>
            {isLoading ? (
              <div className="flex items-center gap-2 h-10 px-3 border rounded-md">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>جاري تحميل المستخدمين...</span>
              </div>
            ) : (
              <Drawer open={fromUserOpen} onOpenChange={setFromUserOpen}>
                <DrawerTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={fromUserOpen}
                    className="w-full justify-between"
                    disabled={isLoading}
                  >
                    {fromUserId
                      ? users.find((u) => u.id === fromUserId)?.name
                      : "اختر المستخدم المصدر"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent>
                  <DrawerHeader>
                    <DrawerTitle>اختر المستخدم المصدر</DrawerTitle>
                    <DrawerDescription>
                      حدد المستخدم الذي سيتم التحويل من حسابه (من)
                    </DrawerDescription>
                  </DrawerHeader>
                  <div className="px-4">
                    <Command>
                      <CommandInput placeholder="ابحث عن مستخدم..." />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty>لا يوجد مستخدمين مطابقين</CommandEmpty>
                        <CommandGroup>
                          {users.map((user) => (
                            <CommandItem
                              key={user.id}
                              value={`${user.name} ${user.role} ${user.balance} ${user.email}`}
                              onSelect={() => {
                                setFromUserId(user.id);
                                setFromUserOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  fromUserId === user.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {user.name} ({user.role}) - الرصيد: {user.balance} ريال
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </div>
                  <DrawerFooter>
                    <DrawerClose asChild>
                      <Button variant="outline">إغلاق</Button>
                    </DrawerClose>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            )}
            {fromUserId && (
              <p className="text-sm text-muted-foreground">
                الرصيد المتاح: {getUserBalance(fromUserId)} ريال
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="toUser">تحويل إلى (المُستلِم)</Label>
            {isLoading ? (
              <div className="flex items-center gap-2 h-10 px-3 border rounded-md">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>جاري تحميل المستخدمين...</span>
              </div>
            ) : (
              <Drawer open={toUserOpen} onOpenChange={setToUserOpen}>
                <DrawerTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={toUserOpen}
                    className="w-full justify-between"
                    disabled={isLoading}
                  >
                    {toUserId
                      ? users.find((u) => u.id === toUserId)?.name
                      : "اختر المستخدم الهدف"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent>
                  <DrawerHeader>
                    <DrawerTitle>اختر المستخدم الهدف</DrawerTitle>
                    <DrawerDescription>
                      حدد المستخدم الذي سيتم التحويل إلى حسابه (إلى)
                    </DrawerDescription>
                  </DrawerHeader>
                  <div className="px-4">
                    <Command>
                      <CommandInput placeholder="ابحث عن مستخدم..." />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty>لا يوجد مستخدمين مطابقين</CommandEmpty>
                        <CommandGroup>
                          {users.map((user) => (
                            <CommandItem
                              key={user.id}
                              value={`${user.name} ${user.role} ${user.balance} ${user.email}`}
                              onSelect={() => {
                                setToUserId(user.id);
                                setToUserOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  toUserId === user.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {user.name} ({user.role}) - الرصيد: {user.balance} ريال
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </div>
                  <DrawerFooter>
                    <DrawerClose asChild>
                      <Button variant="outline">إغلاق</Button>
                    </DrawerClose>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            )}
            {toUserId && (
              <p className="text-sm text-muted-foreground">
                الرصيد الحالي: {getUserBalance(toUserId)} ريال
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">المبلغ (ريال)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="أدخل المبلغ"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">الوصف / السبب</Label>
            <Textarea
              id="description"
              placeholder="أدخل سبب التحويل"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          <Button
            className="w-full"
            onClick={handleTransfer}
            disabled={
              !fromUserId ||
              !toUserId ||
              !amount ||
              !description ||
              isTransferring ||
              parseFloat(amount) > getUserBalance(fromUserId)
            }
          >
            {isTransferring ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                جاري التحويل...
              </>
            ) : (
              "تحويل الأموال"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminFundsTransfer;
