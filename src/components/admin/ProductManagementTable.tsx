import React, { useState } from "react";
import { Product, UserRole } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  Eye,
} from "lucide-react";
import { toast } from "sonner";
import { formatPrice } from "@/utils/formatters";

interface ProductManagementTableProps {
  products: Product[];
  isLoading: boolean;
  onUpdateProduct: (
    id: string,
    productData: Partial<Product>
  ) => Promise<boolean>;
  onDeleteProduct: (id: string) => Promise<boolean>;
  onApproveProduct: (id: string) => Promise<boolean>;
  onRejectProduct: (id: string, reason?: string) => Promise<boolean>;
  currentUserRole?: UserRole;
}

const ProductManagementTable: React.FC<ProductManagementTableProps> = ({
  products,
  isLoading,
  onUpdateProduct,
  onDeleteProduct,
  onApproveProduct,
  onRejectProduct,
  currentUserRole,
}) => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [editProductData, setEditProductData] = useState<Partial<Product>>({});
  const [rejectReason, setRejectReason] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const handleEditProduct = async () => {
    if (!selectedProduct) return;
    setIsProcessing(true);

    try {
      const success = await onUpdateProduct(
        selectedProduct.id,
        editProductData
      );
      if (success) {
        toast.success("تم تحديث بيانات المنتج بنجاح");
        setIsEditDialogOpen(false);
      } else {
        toast.error("فشل تحديث بيانات المنتج");
      }
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error("حدث خطأ أثناء تحديث بيانات المنتج");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteProduct = async () => {
    if (!selectedProduct) return;
    setIsProcessing(true);

    try {
      const success = await onDeleteProduct(selectedProduct.id);
      if (success) {
        toast.success("تم حذف المنتج بنجاح");
        setIsDeleteDialogOpen(false);
      } else {
        toast.error("فشل حذف المنتج");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("حدث خطأ أثناء حذف المنتج");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApproveProduct = async (productId: string) => {
    setIsProcessing(true);

    try {
      const success = await onApproveProduct(productId);
      if (success) {
        toast.success("تم الموافقة على المنتج بنجاح");
      } else {
        toast.error("فشل في الموافقة على المنتج");
      }
    } catch (error) {
      console.error("Error approving product:", error);
      toast.error("حدث خطأ أثناء الموافقة على المنتج");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectProduct = async () => {
    if (!selectedProduct) return;
    setIsProcessing(true);

    try {
      const success = await onRejectProduct(selectedProduct.id, rejectReason);
      if (success) {
        toast.success("تم رفض المنتج بنجاح");
        setIsRejectDialogOpen(false);
      } else {
        toast.error("فشل في رفض المنتج");
      }
    } catch (error) {
      console.error("Error rejecting product:", error);
      toast.error("حدث خطأ أثناء رفض المنتج");
    } finally {
      setIsProcessing(false);
      setRejectReason("");
    }
  };

  const openEditDialog = (product: Product) => {
    setSelectedProduct(product);
    setEditProductData({
      name: product.name,
      description: product.description || "",
      price: product.price,
      quantity: product.quantity || 0,
      active: product.active !== false,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (product: Product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  const openViewDialog = (product: Product) => {
    setSelectedProduct(product);
    setIsViewDialogOpen(true);
  };

  const openRejectDialog = (product: Product) => {
    setSelectedProduct(product);
    setRejectReason("");
    setIsRejectDialogOpen(true);
  };

  const getStatusBadge = (status: string | boolean) => {
    if (typeof status === "string") {
      switch (status) {
        case "approved":
          return (
            <Badge
              variant="outline"
              className="bg-green-50 text-green-700 border-green-200"
            >
              معتمد
            </Badge>
          );
        case "pending":
          return (
            <Badge
              variant="outline"
              className="bg-yellow-50 text-yellow-700 border-yellow-200"
            >
              قيد المراجعة
            </Badge>
          );
        case "rejected":
          return (
            <Badge
              variant="outline"
              className="bg-red-50 text-red-700 border-red-200"
            >
              مرفوض
            </Badge>
          );
        default:
          return <Badge variant="outline">{status}</Badge>;
      }
    } else {
      return status !== false ? (
        <Badge
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200"
        >
          نشط
        </Badge>
      ) : (
        <Badge
          variant="outline"
          className="bg-red-50 text-red-700 border-red-200"
        >
          غير نشط
        </Badge>
      );
    }
  };

  // تحديد ما إذا كان المنتج يمكن إدارته
  const canManageProduct = (): boolean => {
    return currentUserRole === "admin" || currentUserRole === "sub-admin";
  };

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>المنتج</TableHead>
            <TableHead>السعر</TableHead>
            <TableHead>الكمية</TableHead>
            <TableHead>المتجر</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>تاريخ الإضافة</TableHead>
            <TableHead>الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                جاري تحميل البيانات...
              </TableCell>
            </TableRow>
          ) : products.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                لا يوجد منتجات
              </TableCell>
            </TableRow>
          ) : (
            products.map((product) => (
              <TableRow
                key={product.id}
                className={product.active === false ? "opacity-70" : ""}
              >
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded border overflow-hidden">
                      <a
                        href={product.images?.[0] || "/placeholder.svg"}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          src={product.images?.[0] || "/placeholder.svg"}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      </a>
                    </div>
                    <div
                      className="font-medium truncate max-w-[200px]"
                      title={product.name}
                    >
                      {product.name}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{formatPrice(product.price || 0)} ريال</TableCell>
                <TableCell>
                  {product.quantity !== undefined
                    ? product.quantity
                    : "غير محدد"}
                </TableCell>
                <TableCell>
                  <div
                    className="truncate max-w-[150px]"
                    title={product.store_name || "غير محدد"}
                  >
                    {product.store_name || "غير محدد"}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(product.status || product.active)}
                </TableCell>
                <TableCell>
                  {product.created_at
                    ? new Date(product.created_at).toLocaleDateString("ar-SA")
                    : "-"}
                </TableCell>
                <TableCell>
                  {canManageProduct() && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">إجراءات</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => openViewDialog(product)}
                        >
                          <Eye className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openEditDialog(product)}
                        >
                          <Edit className="h-4 w-4 ml-2" />
                          تعديل المنتج
                        </DropdownMenuItem>

                        {product.status === "pending" && (
                          <>
                            <DropdownMenuItem
                              onClick={() => handleApproveProduct(product.id)}
                            >
                              <CheckCircle className="h-4 w-4 ml-2" />
                              الموافقة على المنتج
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => openRejectDialog(product)}
                            >
                              <XCircle className="h-4 w-4 ml-2" />
                              رفض المنتج
                            </DropdownMenuItem>
                          </>
                        )}

                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(product)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="h-4 w-4 ml-2" />
                          حذف المنتج
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* حوار عرض تفاصيل المنتج */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>تفاصيل المنتج</DialogTitle>
          </DialogHeader>
          {selectedProduct && (
            <div className="grid gap-4 py-4">
              <div className="flex justify-center mb-4">
                <div className="w-32 h-32 rounded overflow-hidden border">
                  <a
                    href={selectedProduct.images?.[0] || "/placeholder.svg"}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <img
                      src={selectedProduct.images?.[0] || "/placeholder.svg"}
                      alt={selectedProduct.name}
                      className="w-full h-full object-cover"
                    />
                  </a>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">الاسم</Label>
                <div className="col-span-3">{selectedProduct.name}</div>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right font-bold">الوصف</Label>
                <div className="col-span-3 whitespace-pre-wrap">
                  {selectedProduct.description || "لا يوجد وصف"}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">السعر</Label>
                <div className="col-span-3">
                  {formatPrice(selectedProduct.price || 0)} ريال
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">الكمية</Label>
                <div className="col-span-3">
                  {selectedProduct.quantity !== undefined
                    ? selectedProduct.quantity
                    : "غير محدد"}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">المتجر</Label>
                <div className="col-span-3">
                  {selectedProduct.store_name || "غير محدد"}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">الحالة</Label>
                <div className="col-span-3">
                  {getStatusBadge(
                    selectedProduct.status || selectedProduct.active
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-bold">تاريخ الإضافة</Label>
                <div className="col-span-3">
                  {selectedProduct.created_at
                    ? new Date(selectedProduct.created_at).toLocaleDateString(
                        "ar-SA"
                      )
                    : "-"}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewDialogOpen(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار تعديل المنتج */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المنتج</DialogTitle>
            <DialogDescription>
              تعديل معلومات {selectedProduct?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                value={editProductData.name || ""}
                onChange={(e) =>
                  setEditProductData({
                    ...editProductData,
                    name: e.target.value,
                  })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                الوصف
              </Label>
              <Textarea
                id="description"
                value={editProductData.description || ""}
                onChange={(e) =>
                  setEditProductData({
                    ...editProductData,
                    description: e.target.value,
                  })
                }
                className="col-span-3"
                rows={4}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price" className="text-right">
                السعر
              </Label>
              <Input
                id="price"
                type="number"
                value={editProductData.price || 0}
                onChange={(e) =>
                  setEditProductData({
                    ...editProductData,
                    price: parseFloat(e.target.value),
                  })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                الكمية
              </Label>
              <Input
                id="quantity"
                type="number"
                value={editProductData.quantity || 0}
                onChange={(e) =>
                  setEditProductData({
                    ...editProductData,
                    quantity: parseInt(e.target.value),
                  })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="active" className="text-right">
                الحالة
              </Label>
              <Select
                value={editProductData.active === false ? "false" : "true"}
                onValueChange={(value) =>
                  setEditProductData({
                    ...editProductData,
                    active: value === "true",
                  })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">نشط</SelectItem>
                  <SelectItem value="false">غير نشط</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              onClick={handleEditProduct}
              disabled={isProcessing}
            >
              {isProcessing ? "جاري الحفظ..." : "حفظ التغييرات"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار حذف المنتج */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>حذف المنتج</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف المنتج {selectedProduct?.name}؟ لا
              يمكن التراجع عن هذه العملية.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteProduct}
              disabled={isProcessing}
            >
              {isProcessing ? "جاري الحذف..." : "حذف المنتج"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار رفض المنتج */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>رفض المنتج</DialogTitle>
            <DialogDescription>
              الرجاء إدخال سبب رفض المنتج {selectedProduct?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="rejectReason" className="text-right">
                سبب الرفض
              </Label>
              <Textarea
                id="rejectReason"
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                className="col-span-3"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectProduct}
              disabled={isProcessing}
            >
              {isProcessing ? "جاري الرفض..." : "رفض المنتج"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProductManagementTable;
