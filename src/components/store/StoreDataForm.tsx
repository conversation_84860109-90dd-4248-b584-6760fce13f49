
import React, { useEffect, useState } from "react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useStoreDataQuery } from "@/hooks/query/useStoreDataQuery";
import ImageUploadPreview from "@/components/ui/image-upload-preview";
import { useAuth } from "@/contexts/auth";

// Define the form schema with zod
const formSchema = z.object({
  name: z.string().min(2, "الاسم يجب أن يحتوي على الأقل حرفين"),
  description: z.string().optional(),
  address: z.string().min(5, "العنوان يجب أن يحتوي على الأقل 5 أحرف"),
  city: z.string().min(2, "المدينة يجب أن تحتوي على الأقل حرفين"),
  phone: z.string().min(10, "رقم الهاتف يجب أن يحتوي على الأقل 10 أرقام"),
  email: z.string().email("البريد الإلكتروني غير صالح"),
  hours: z.string().min(5, "ساعات العمل يجب أن تحتوي على الأقل 5 أحرف"),
  legal_activity: z.string().min(2, "النشاط القانوني يجب أن يحتوي على الأقل حرفين"),
  shelf_space: z.string().min(2, "مساحة الرف يجب أن تحتوي على الأقل حرفين"),
  capacity: z.coerce.number().min(0, "السعة يجب أن تكون أكبر من أو تساوي 0"),
  subscription_type: z.enum(["percentage", "fixed"]).default("fixed"),
});

export default function StoreDataForm() {
  const { submitData, storeData, isLoading } = useStoreDataQuery();
  const { user } = useAuth();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [galleryFiles, setGalleryFiles] = useState<File[]>([]);
  
  // Create form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      city: "",
      phone: "",
      email: "",
      hours: "",
      legal_activity: "",
      shelf_space: "",
      capacity: 0,
      subscription_type: "fixed", // Default to fixed fee
    },
  });

  // Populate form with existing store data if available
  useEffect(() => {
    if (storeData) {
      form.reset({
        name: storeData.name || "",
        description: storeData.description || "",
        address: storeData.address || "",
        city: storeData.city || "",
        phone: storeData.phone || "",
        email: storeData.email || "",
        hours: storeData.hours || "",
        legal_activity: storeData.legal_activity || "",
        shelf_space: storeData.shelf_space || "",
        capacity: storeData.capacity || 0,
        subscription_type: storeData.subscription_type || "fixed",
      });
    }
  }, [storeData, form]);

  // Check if user is admin to show subscription type field
  const isAdmin = user?.role === 'admin' || user?.role === 'sub-admin';

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await submitData({
        data: {
          ...values,
          gallery: storeData?.gallery || [],
        },
        imageFile: imageFile,
        galleryFiles: galleryFiles,
      });
    } catch (error) {
      console.error("Error submitting store data:", error);
      toast.error("حدث خطأ أثناء حفظ بيانات المتجر");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>اسم المتجر</FormLabel>
                  <FormControl>
                    <Input placeholder="أدخل اسم المتجر" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>وصف المتجر</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="أدخل وصفاً للمتجر"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>المدينة</FormLabel>
                    <FormControl>
                      <Input placeholder="المدينة" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>العنوان</FormLabel>
                    <FormControl>
                      <Input placeholder="العنوان" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رقم الهاتف</FormLabel>
                    <FormControl>
                      <Input placeholder="رقم الهاتف" type="tel" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="البريد الإلكتروني"
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ساعات العمل</FormLabel>
                    <FormControl>
                      <Input placeholder="9 صباحاً - 10 مساءً" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="legal_activity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>النشاط القانوني</FormLabel>
                    <FormControl>
                      <Input placeholder="النشاط القانوني" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shelf_space"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>مساحة الرف</FormLabel>
                    <FormControl>
                      <Input placeholder="مساحة الرف" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="capacity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>السعة</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="السعة"
                        type="number"
                        min={0}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Only show subscription type selection to admin users */}
            {isAdmin && (
              <FormField
                control={form.control}
                name="subscription_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>نوع الاشتراك</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر نوع الاشتراك" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="fixed">رسوم ثابتة</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      نظام الاشتراك هو رسوم ثابتة فقط، يتم تحديدها من قبل الإدارة
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>

        <FormItem>
          <FormLabel>صورة المتجر</FormLabel>
          <ImageUploadPreview
            onChange={setImageFile}
            value={storeData?.image}
            maxSizeMB={5}
          />
        </FormItem>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "جاري الحفظ..." : "حفظ بيانات المتجر"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
