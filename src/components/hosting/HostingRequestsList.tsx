import React, { useState } from "react";
import { HostingRequest } from "@/types";
import HostingRequestCard from "./HostingRequestCard";
import { ShoppingCart, Plus, Filter } from "lucide-react";
import EmptyState from "@/components/EmptyState";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface HostingRequestsListProps {
  requests: HostingRequest[];
  isLoading: boolean;
  onCreateRequest: () => void;
  userRole?: string;
}

export const HostingRequestsList: React.FC<HostingRequestsListProps> = ({
  requests,
  isLoading,
  onCreateRequest,
  userRole,
}) => {
  const [activeTab, setActiveTab] = useState<string>("all");

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <div key={index} className="border rounded-lg p-4 shadow-sm">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-4" />
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
              <Skeleton className="h-10 w-full mt-4" />
            </div>
          ))}
      </div>
    );
  }

  // Group requests by status for tabs
  const pendingRequests = requests.filter((req) => req.status === "pending");
  const acceptedRequests = requests.filter((req) =>
    ["accepted", "ready", "processing", "in_progress"].includes(
      req.status as string
    )
  );
  const completedRequests = requests.filter((req) =>
    ["completed", "delivered"].includes(req.status as string)
  );
  const rejectedRequests = requests.filter((req) =>
    ["rejected", "cancelled"].includes(req.status as string)
  );

  const getFilteredRequests = () => {
    switch (activeTab) {
      case "pending":
        return pendingRequests;
      case "accepted":
        return acceptedRequests;
      case "completed":
        return completedRequests;
      case "rejected":
        return rejectedRequests;
      default:
        return requests;
    }
  };

  const filteredRequests = getFilteredRequests();

  if (requests.length === 0) {
    return (
      <EmptyState
        title={
          userRole === "ecommerce"
            ? "لا توجد طلبات عرض منتجات"
            : "لا توجد طلبات عرض منتجات"
        }
        description={
          userRole === "ecommerce"
            ? "لم يتم إنشاء أي طلبات عرض منتجات بعد. يمكنك إنشاء طلب جديد لعرض منتجاتك في المحلات التجارية."
            : "لا توجد طلبات عرض منتجات موجهة إليك حالياً."
        }
        icon={ShoppingCart}
        action={
          userRole === "ecommerce"
            ? {
                label: "إنشاء طلب جديد",
                onClick: onCreateRequest,
              }
            : undefined
        }
      />
    );
  }

  return (
    <Card className="space-y-5">
      <CardHeader className="pb-3">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <CardTitle>طلبات عرض المنتجات</CardTitle>
        </div>
      </CardHeader>

      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <div className="px-6">
          <TabsList className="w-full justify-start overflow-auto">
            <TabsTrigger value="all" className="flex items-center gap-2">
              الكل
              <Badge variant="secondary" className="ml-1">
                {requests.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex items-center gap-2">
              قيد الانتظار
              <Badge variant="secondary" className="ml-1">
                {pendingRequests.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="accepted" className="flex items-center gap-2">
              مقبولة
              <Badge variant="secondary" className="ml-1">
                {acceptedRequests.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-2">
              مكتملة
              <Badge variant="secondary" className="ml-1">
                {completedRequests.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex items-center gap-2">
              مرفوضة
              <Badge variant="secondary" className="ml-1">
                {rejectedRequests.length}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <CardContent className="pt-6">
          <TabsContent value="all" className="m-0">
            {filteredRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد طلبات عرض منتجات
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredRequests.map((request) => (
                  <HostingRequestCard key={request.id} request={request} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="pending" className="m-0">
            {filteredRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد طلبات قيد الانتظار
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredRequests.map((request) => (
                  <HostingRequestCard key={request.id} request={request} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="accepted" className="m-0">
            {filteredRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد طلبات مقبولة
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredRequests.map((request) => (
                  <HostingRequestCard key={request.id} request={request} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="completed" className="m-0">
            {filteredRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد طلبات مكتملة
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredRequests.map((request) => (
                  <HostingRequestCard key={request.id} request={request} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected" className="m-0">
            {filteredRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد طلبات مرفوضة
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredRequests.map((request) => (
                  <HostingRequestCard key={request.id} request={request} />
                ))}
              </div>
            )}
          </TabsContent>
        </CardContent>
      </Tabs>

      <div className="px-6 pb-4 text-center text-sm text-muted-foreground border-t mt-6 pt-4">
        إجمالي الطلبات: {requests.length}
      </div>
    </Card>
  );
};

export default HostingRequestsList;
