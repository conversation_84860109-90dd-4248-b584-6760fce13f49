
import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, Calendar, CheckCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { Link } from 'react-router-dom';

interface ExpiryNoticeProps {
  onRenew?: () => void;
}

export const SubscriptionExpiryNotice: React.FC<ExpiryNoticeProps> = ({ onRenew }) => {
  const { user } = useAuth();
  const [daysLeft, setDaysLeft] = useState<number | null>(null);

  // Fetch subscription data using React Query
  const { data: subscriptionData, isLoading } = useQuery({
    queryKey: ['subscription-expiry', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      
      try {
        // For e-commerce users, check hosting requests they've sent
        if (user.role === 'ecommerce') {
          const { data, error } = await supabase
            .from('hosting_requests')
            .select('expires_at, status')
            .eq('ecommerce_id', user.id)
            .in('status', ['accepted', 'ready'])
            .order('created_at', { ascending: false })
            .limit(1);
            
          if (error) throw error;
          return data?.[0] || null;
        } 
        // For store users, check hosting requests they've received
        else if (user.role === 'store') {
          const { data, error } = await supabase
            .from('hosting_requests')
            .select('expires_at, status')
            .eq('store_id', user.id)
            .in('status', ['accepted', 'ready'])
            .order('created_at', { ascending: false })
            .limit(1);
            
          if (error) throw error;
          return data?.[0] || null;
        }
        
        return null;
      } catch (error) {
        console.error('Error fetching subscription data:', error);
        return null;
      }
    },
    enabled: !!user?.id,
  });

  useEffect(() => {
    if (subscriptionData?.expires_at) {
      const expiryDate = new Date(subscriptionData.expires_at);
      const today = new Date();
      const timeDiff = expiryDate.getTime() - today.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
      
      setDaysLeft(daysDiff);
    } else {
      setDaysLeft(null);
    }
  }, [subscriptionData]);

  if (isLoading) return null;
  
  // Don't show anything if there's no active subscription or expiry date
  if (!subscriptionData || !subscriptionData.expires_at || !daysLeft) return null;

  // Subscription is active and not near expiry
  if (daysLeft > 30) {
    return (
      <Card className="mb-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardContent className="flex items-center pt-6">
          <CheckCircle className="h-6 w-6 text-green-500 mr-4" />
          <div>
            <p className="text-green-800 font-medium">الاشتراك نشط وساري المفعول</p>
            <p className="text-green-600 text-sm">
              ينتهي في {new Date(subscriptionData.expires_at).toLocaleDateString('ar-SA')}
              {daysLeft > 0 && ` (متبقي ${daysLeft} يوم)`}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Subscription has expired
  if (daysLeft <= 0) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-5 w-5" />
        <AlertTitle>انتهى الاشتراك</AlertTitle>
        <AlertDescription>
          <p>
            لقد انتهت صلاحية اشتراكك، يرجى تجديد الاشتراك للاستمرار في استخدام الخدمة.
          </p>
          <div className="mt-4">
            {onRenew ? (
              <Button onClick={onRenew} className="mt-2">
                تجديد الاشتراك الآن
              </Button>
            ) : (
              <Button asChild className="mt-2">
                <Link to="/dashboard/wallet">تجديد الاشتراك</Link>
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Subscription is expiring soon (30 days or less)
  return (
    <Alert className="mb-6 border-yellow-300 bg-yellow-50">
      <Calendar className="h-5 w-5 text-yellow-600" />
      <AlertTitle className="text-yellow-800">اشتراكك سينتهي قريباً</AlertTitle>
      <AlertDescription className="text-yellow-700">
        <p>
          سينتهي اشتراكك في {new Date(subscriptionData.expires_at).toLocaleDateString('ar-SA')} 
          {daysLeft > 0 && ` (متبقي ${daysLeft} يوم)`}. يرجى تجديد الاشتراك قبل انتهاء المدة لتجنب انقطاع الخدمة.
        </p>
        <div className="mt-4">
          {onRenew ? (
            <Button onClick={onRenew} variant="outline" className="mt-2">
              تجديد الاشتراك الآن
            </Button>
          ) : (
            <Button variant="outline" asChild className="mt-2">
              <Link to="/dashboard/wallet">تجديد الاشتراك</Link>
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default SubscriptionExpiryNotice;
