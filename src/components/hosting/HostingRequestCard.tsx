import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { HostingRequest } from "@/types";
import { formatDate } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import { Store, Calendar, Clock, ArrowRight } from "lucide-react";

interface HostingRequestCardProps {
  request: HostingRequest;
}

const HostingRequestCard: React.FC<HostingRequestCardProps> = ({ request }) => {
  const navigate = useNavigate();
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
      case "rejected":
      case "expired":
        return "bg-red-100 text-red-800";
      case "processing":
      case "accepted":
      case "awaiting_shipping":
      case "on_sale":
        return "bg-blue-100 text-blue-800";
      case "ready":
      case "delivered":
        return "bg-green-100 text-green-800";
      default:
        return "bg-amber-100 text-amber-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "مكتمل";
      case "cancelled":
        return "ملغي";
      case "pending":
        return "قيد الانتظار";
      case "processing":
        return "قيد المعالجة";
      case "accepted":
        return "مقبول";
      case "rejected":
        return "مرفوض";
      case "ready":
        return "جاهز";
      case "delivered":
        return "تم التسليم";
      case "awaiting_shipping":
        return "بانتظار الشحن";
      case "on_sale":
        return " معروض للبيع ";
      case "expired":
        return "منتهي الصلاحية";
      default:
        return status;
    }
  };

  const viewDetails = () => {
    navigate(`/dashboard/hosting-requests/${request.id}`);
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{request.store_name}</CardTitle>
          <Badge className={getStatusBadgeClass(request.status || "")}>
            {getStatusLabel(request.status || "")}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          <div className="flex items-center text-sm text-muted-foreground gap-2">
            <Store className="h-4 w-4" />
            <span>المتجر الإلكتروني: {request.ecommerce_name}</span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground gap-2">
            <Calendar className="h-4 w-4" />
            <span>تاريخ الطلب: {formatDate(request.created_at || "")}</span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground gap-2">
            <Clock className="h-4 w-4" />
            <span>آخر تحديث: {formatDate(request.updated_at || "")}</span>
          </div>

          {request.notes && (
            <div className="mt-2 text-sm line-clamp-2">{request.notes}</div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        <Button
          variant="ghost"
          className="w-full flex items-center justify-center gap-1 hover:bg-slate-100"
          onClick={viewDetails}
        >
          عرض التفاصيل
          <ArrowRight className="h-4 w-4 mr-1" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default HostingRequestCard;
