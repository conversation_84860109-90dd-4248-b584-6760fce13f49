import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatDate } from "@/lib/utils";
import { HostingRequest, OrderStatus, Product } from "@/types";
import ProductTable, { ProductData } from "@/components/products/ProductTable";
import { useAuth } from "@/contexts/auth";

interface HostingRequestDetailCardProps {
  request: HostingRequest;
  onStatusUpdate?: (status: OrderStatus) => void;
  canApprove: boolean;
  canCancel: boolean;
  isMutating?: boolean;
}

export const HostingRequestDetailCard: React.FC<
  HostingRequestDetailCardProps
> = ({
  request,
  onStatusUpdate,
  canApprove,
  canCancel,
  isMutating = false,
}) => {
  const { user } = useAuth();
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
      case "rejected":
      case "expired":
        return "bg-red-100 text-red-800";
      case "awaiting_shipping":
        return "bg-amber-100 text-amber-800";
      case "on_sale":
        return "bg-green-100 text-green-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "awaiting_shipping":
        return "بأنتظار شحن المنتجات";
      case "on_sale":
        return "منتجات معروضة للبيع";
      case "expired":
        return "اشتراك منتهي";
      case "completed":
        return "مكتمل";
      case "cancelled":
        return "ملغي";
      case "pending":
        return "قيد الانتظار";
      case "processing":
        return "قيد المعالجة";
      default:
        return status;
    }
  };

  const getSubscriptionTypeLabel = (type?: string) => {
    if (!type) return "غير محدد";

    switch (type) {
      case "percentage":
        return "عمولة على كل عملية بيع";
      case "fixed":
        return "رسوم شهرية ثابتة";
      default:
        return type;
    }
  };

  const getDurationLabel = (duration?: number) => {
    if (duration === undefined || duration === null) return "غير محدد";

    if (duration === 1) return "شهر واحد";
    if (duration === 12) return "سنة";
    return `${duration} أشهر`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>معلومات طلب عرض المنتجات</CardTitle>
          <Badge className={getStatusBadgeClass(request.status)}>
            {getStatusLabel(request.status)}
          </Badge>
        </div>
        <CardDescription>طلب رقم {request.id?.substring(0, 8)}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">
              المتجر الإلكتروني
            </h3>
            <p>{request.ecommerce_name}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">
              المتجر المضيف
            </h3>
            <p>{request.store_name}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">
              تاريخ الطلب
            </h3>
            <p>{formatDate(request.created_at || "")}</p>
          </div>
          <div>
            <h3 className="font-medium text-sm text-muted-foreground mb-1">
              آخر تحديث
            </h3>
            <p>{formatDate(request.updated_at || "")}</p>
          </div>

          {/* Subscription Info */}
          {request.subscription_type && (
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-1">
                نوع الاشتراك
              </h3>
              <p>{getSubscriptionTypeLabel(request.subscription_type)}</p>
            </div>
          )}

          {request.duration !== undefined && (
            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-1">
                مدة الاشتراك
              </h3>
              <p>{getDurationLabel(request.duration)}</p>
            </div>
          )}
        </div>

        <Separator />

        {request.notes && (
          <div>
            <h3 className="font-medium mb-2">وصف الطلب</h3>
            <p className="text-muted-foreground">{request.notes}</p>
          </div>
        )}

        {request.notes && (
          <div>
            <h3 className="font-medium mb-2">ملاحظات إضافية</h3>
            <p className="text-muted-foreground">{request.notes}</p>
          </div>
        )}
        {request.products && request.products.length > 0 && (
          <div>
            <h3 className="font-medium mb-4">المنتجات المطلوبة</h3>
            <ProductTable
              products={request.products as ProductData[]}
              onView={(product) => {
                console.log("Viewing product:", product);
              }}
              showActions={false}
            />
          </div>
        )}
      </CardContent>
      {(canApprove || canCancel) && user.role === "admin" && (
        <CardFooter className="flex flex-wrap justify-end gap-2">
          {isMutating ? (
            <Badge className="bg-gray-100 text-gray-800 py-2 px-4 cursor-not-allowed opacity-50">
              جاري المعالجة...
            </Badge>
          ) : (
            <>
              <Badge
                className="cursor-pointer bg-amber-100 hover:bg-amber-200 text-amber-800 py-2 px-4"
                onClick={() =>
                  onStatusUpdate && onStatusUpdate("awaiting_shipping")
                }
              >
                بأنتظار شحن المنتجات
              </Badge>
              <Badge
                className="cursor-pointer bg-green-100 hover:bg-green-200 text-green-800 py-2 px-4"
                onClick={() => onStatusUpdate && onStatusUpdate("on_sale")}
              >
                منتجات معروضة للبيع
              </Badge>
              <Badge
                className="cursor-pointer bg-red-100 hover:bg-red-200 text-red-800 py-2 px-4"
                onClick={() => onStatusUpdate && onStatusUpdate("expired")}
              >
                اشتراك منتهي
              </Badge>
            </>
          )}
        </CardFooter>
      )}
      {(canApprove || canCancel) && user.role === "store" && (
        <CardFooter className="flex flex-wrap justify-end gap-2">
          {isMutating ? (
            <Badge className="bg-gray-100 text-gray-800 py-2 px-4 cursor-not-allowed opacity-50">
              جاري المعالجة...
            </Badge>
          ) : (
            <>
              {canApprove && (
                <Badge
                  className="cursor-pointer bg-green-100 hover:bg-green-200 text-green-800 py-2 px-4"
                  onClick={() => onStatusUpdate && onStatusUpdate("accepted")}
                >
                  قبول الطلب
                </Badge>
              )}

              {canCancel && (
                <Badge
                  className="cursor-pointer bg-red-100 hover:bg-red-200 text-red-800 py-2 px-4"
                  onClick={() => onStatusUpdate && onStatusUpdate("rejected")}
                >
                  إلغاء الطلب
                </Badge>
              )}
            </>
          )}
        </CardFooter>
      )}
    </Card>
  );
};

export default HostingRequestDetailCard;
