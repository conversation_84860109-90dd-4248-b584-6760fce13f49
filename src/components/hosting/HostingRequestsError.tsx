
import React from 'react';
import { XCircle } from 'lucide-react';
import EmptyState from '@/components/EmptyState';

interface HostingRequestsErrorProps {
  error: string;
  onRetry: () => void;
  title?: string;
}

export const HostingRequestsError: React.FC<HostingRequestsErrorProps> = ({ 
  error, 
  onRetry,
  title = "حدث خطأ"
}) => {
  return (
    <div className="max-w-7xl mx-auto py-10">
      <EmptyState
        title={title}
        description={error}
        icon={XCircle}
        action={{
          label: "إعادة المحاولة",
          onClick: onRetry
        }}
      />
    </div>
  );
};

export default HostingRequestsError;
