import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { HostingProduct, Product } from "@/types";
import { useProductOperations } from "@/hooks/useProductOperations";
import { Loader2, Minus, Plus } from "lucide-react";
import { toast } from "sonner";

export interface ProductSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProducts: HostingProduct[];
  setSelectedProducts: React.Dispatch<React.SetStateAction<HostingProduct[]>>;
  onSave: (selectedProducts: HostingProduct[], notes: string) => void;
  storeName: string;
}

const ProductSelectionModal: React.FC<ProductSelectionModalProps> = ({
  isOpen,
  onClose,
  selectedProducts,
  setSelectedProducts,
  onSave,
  storeName,
}) => {
  const [notes, setNotes] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const { userProducts, isLoading } = useProductOperations();

  // Convert user products to hosting products format
  const availableProducts = userProducts.map((product) => ({
    id: product.id,
    hosting_request_id: "",
    product_id: product.id,
    product_name: product.name,
    price: product.price || 0,
    quantity: 1, // Default quantity
    image:
      product.images && product.images.length > 0
        ? product.images[0]
        : "/placeholder.svg",
  }));

  const filteredProducts = availableProducts.filter((product) =>
    product.product_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProductSelect = (product: HostingProduct) => {
    if (selectedProducts.some((p) => p.product_id === product.product_id)) {
      setSelectedProducts(
        selectedProducts.filter((p) => p.product_id !== product.product_id)
      );
    } else {
      setSelectedProducts([...selectedProducts, { ...product, quantity: 1 }]);
    }
  };

  const updateProductQuantity = (productId: string, quantity: number) => {
    setSelectedProducts(
      selectedProducts.map((product) =>
        product.product_id === productId
          ? { ...product, quantity: Math.max(1, quantity) }
          : product
      )
    );
  };

  const handleSave = () => {
    if (!selectedProducts.length) return;

    onSave(selectedProducts, notes);
  };

  function checkQuantity(productId: string | number, quantity: number) {
    const selectedProduct = selectedProducts.find(
      (product) => product.product_id === productId
    );
    const product = userProducts.find((product) => product.id === productId);
    if (selectedProduct && product) {
      if (quantity <= product.quantity) {
        return true;
      } else {
        toast.error(
          `الكمية المطلوبة (${quantity}) أكبر من المتاحة (${product.quantity})`
        );
        return false;
      }
    }
    return false;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>اختيار المنتجات لعرضها في {storeName}</DialogTitle>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <Input
            placeholder="البحث عن منتج..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mb-4"
          />

          <div className="border rounded-md p-2 max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredProducts.length === 0 ? (
              <p className="text-center p-4 text-muted-foreground">
                {searchTerm
                  ? "لا توجد منتجات مطابقة للبحث"
                  : "لا توجد منتجات متاحة"}
              </p>
            ) : (
              <div className="space-y-2">
                {filteredProducts.map((product) => (
                  <div
                    key={product.product_id}
                    className="flex items-center space-x-4 space-x-reverse p-2 border-b last:border-0"
                  >
                    <Checkbox
                      id={`product-${product.product_id}`}
                      checked={selectedProducts.some(
                        (p) => p.product_id === product.product_id
                      )}
                      onCheckedChange={() => handleProductSelect(product)}
                    />
                    <Label
                      htmlFor={`product-${product.product_id}`}
                      className="flex flex-1 items-center cursor-pointer"
                    >
                      <div className="flex-shrink-0 h-12 w-12 overflow-hidden rounded-md border">
                        <img
                          src={product.image}
                          alt={product.product_name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="mr-4 flex-1">
                        <div className="font-medium">
                          {product.product_name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {product.price} ر.س
                        </div>
                      </div>
                    </Label>
                    {selectedProducts.some(
                      (p) => p.product_id === product.product_id
                    ) && (
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation();

                            const selectedProduct = selectedProducts.find(
                              (p) => p.product_id === product.product_id
                            );
                            if (selectedProduct) {
                              if (selectedProduct.quantity === 1) return;
                              updateProductQuantity(
                                product.product_id,
                                selectedProduct.quantity - 1
                              );
                            }
                          }}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          className="w-12 h-7 text-center px-1"
                          value={
                            selectedProducts.find(
                              (p) => p.product_id === product.product_id
                            )?.quantity || 1
                          }
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 1;
                            if (checkQuantity(product.product_id, value)) {
                              updateProductQuantity(product.product_id, value);
                            }
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            const selectedProduct = selectedProducts.find(
                              (p) => p.product_id === product.product_id
                            );
                            if (selectedProduct) {
                              if (
                                checkQuantity(
                                  product.product_id,
                                  selectedProduct.quantity + 1
                                )
                              ) {
                                updateProductQuantity(
                                  product.product_id,
                                  selectedProduct.quantity + 1
                                );
                              }
                            }
                          }}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات إضافية</Label>
            <Textarea
              id="notes"
              placeholder="إضافة أي ملاحظات أو تفاصيل إضافية حول المنتجات المختارة..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            إلغاء
          </Button>
          <Button onClick={handleSave} disabled={selectedProducts.length === 0}>
            تأكيد اختيار المنتجات ({selectedProducts.length})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProductSelectionModal;
