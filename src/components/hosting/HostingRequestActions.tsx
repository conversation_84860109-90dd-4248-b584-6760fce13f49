import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { HostingRequest, OrderStatus } from '@/types';
import { Badge } from '@/components/ui/badge';
import { FileText, Package, CheckCircle, XCircle } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { useAuth } from '@/contexts/auth';

interface HostingRequestActionsProps {
  request: HostingRequest;
  onSelectProducts: () => void;
  onCreateInvoice: () => void;
}

export const HostingRequestActions: React.FC<HostingRequestActionsProps> = ({
  request,
  onSelectProducts,
  onCreateInvoice
}) => {
  const { user } = useAuth();
  const isStoreOwner = user?.role === 'store';
  const isEcommerceOwner = user?.role === 'ecommerce';
  
  const getStatusInfo = (status: OrderStatus) => {
    const statusMap: Record<string, { label: string; color: string }> = {
      'pending': { 
        label: 'قيد الانتظار', 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200' 
      },
      'processing': { 
        label: 'قيد المعالجة', 
        color: 'bg-blue-100 text-blue-800 border-blue-200' 
      },
      'completed': { 
        label: 'مكتمل', 
        color: 'bg-green-100 text-green-800 border-green-200' 
      },
      'cancelled': { 
        label: 'ملغي', 
        color: 'bg-red-100 text-red-800 border-red-200' 
      },
      'accepted': {
        label: 'مقبول',
        color: 'bg-green-100 text-green-800 border-green-200'
      },
      'rejected': {
        label: 'مرفوض',
        color: 'bg-red-100 text-red-800 border-red-200'
      },
      'delivered': {
        label: 'تم التوصيل',
        color: 'bg-green-100 text-green-800 border-green-200'
      },
      'ready': {
        label: 'جاهز',
        color: 'bg-blue-100 text-blue-800 border-blue-200'
      },
      'in_progress': {
        label: 'قيد التنفيذ',
        color: 'bg-blue-100 text-blue-800 border-blue-200'
      }
    };
    
    return statusMap[status] || { label: status, color: 'bg-gray-100 text-gray-800 border-gray-200' };
  };
  
  const { label, color } = getStatusInfo(request.status);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>إجراءات الطلب</CardTitle>

        <CardDescription>
          يمكنك إدارة طلب عرض المنتجات من هنا
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium">حالة الطلب</h3>
          <div className="flex items-center gap-2">
            <Badge className={color}>{label}</Badge>
            <span className="text-xs text-muted-foreground">
              آخر تحديث: {formatDate(request.updated_at || '')}
            </span>
          </div>
        </div>
        
        <div className="space-y-4">
          {isStoreOwner && request.status === 'processing' && (
            <Button 
              onClick={onSelectProducts} 
              className="w-full flex items-center justify-center gap-2"
            >
              <Package className="h-4 w-4" />
              اختيار المنتجات للعرض
            </Button>
          )}
          
          {(request.status === 'accepted' || request.status === 'processing') && (
            <Button
              onClick={onCreateInvoice}
              variant="outline"
              className="w-full flex items-center justify-center gap-2"
            >
              <FileText className="h-4 w-4" />
              إنشاء فاتورة
            </Button>
          )}
          
          {isStoreOwner && request.status === 'pending' && (
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                onClick={() => {/* سيتم تنفيذ وظيفة قبول الطلب */}}
              >
                <CheckCircle className="h-4 w-4" />
                قبول الطلب
              </Button>
              
              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                onClick={() => {/* سيتم تنفيذ وظيفة رفض الطلب */}}
              >
                <XCircle className="h-4 w-4" />
                رفض الطلب
              </Button>
            </div>
          )}
          
          {isEcommerceOwner && request.status === 'pending' && (
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-2 bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
              onClick={() => {/* سيتم تنفيذ وظيفة إلغاء الطلب */}}
            >
              <XCircle className="h-4 w-4" />
              إلغاء الطلب
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default HostingRequestActions;
