import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Camera,
  Briefcase,
  Package,
} from "lucide-react";
import { Store } from "@/types/store";
import { HostingProduct } from "@/types";
import { useAuth } from "@/contexts/auth";
import { useCreateHostingRequest } from "@/hooks/hosting/useCreateHostingRequest";
import ProductSelectionModal from "../hosting/ProductSelectionModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useUpdateStoreAdmin } from "@/hooks/store/useUpdateStoreAdmin";
import { Input } from "../ui/input";

interface StoreDetailsProps {
  selectedStore: Store | null;
  onClose: () => void;
  onSendRequest: (
    storeId: string,
    storeName: string,
    legalActivity: string
  ) => void;
}

const subscriptionFormSchema = z.object({
  duration: z.string().min(1, { message: "يرجى اختيار المدة" }),
  subscriptionType: z.string().min(1, { message: "يرجى اختيار نوع الاشتراك" }),
});

type SubscriptionFormValues = z.infer<typeof subscriptionFormSchema>;

export const StoreDetails: React.FC<StoreDetailsProps> = ({
  selectedStore,
  onClose,
}) => {
  const { user } = useAuth();
  const { createHostingRequest, isSubmitting } = useCreateHostingRequest();
  const [isProductSelectionOpen, setIsProductSelectionOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<HostingProduct[]>(
    []
  );
  const [subscription, setSupscripton] = useState("");

  const [rating, setRating] = useState<number>(selectedStore?.rating || 0);
  const [featured, setFeatured] = useState(selectedStore?.featured);
  const [isSubscriptionDialogOpen, setIsSubscriptionDialogOpen] =
    useState(false);

  const { mutateAsync, isPending: isUpdating } = useUpdateStoreAdmin();

  useEffect(() => {
    if (selectedStore) {
      setRating(selectedStore.rating || 0);
      setFeatured(selectedStore.featured || false);
    }
  }, [selectedStore]);

  const form = useForm<SubscriptionFormValues>({
    resolver: zodResolver(subscriptionFormSchema),
    defaultValues: {
      duration: "1",
      subscriptionType: "fixed",
    },
  });

  if (!selectedStore) return null;

  const handleSendRequest = () => {
    if (!user) {
      toast.error("يجب تسجيل الدخول لإرسال طلب استضافة");
      return;
    }
    if (!user.legalDetails) {
      toast.error("يرجى إكمال معلوماتك القانونية قبل إرسال الطلب.");
      return;
    }

    // Check if store activity matches user activity
    const userLegalActivity = user.legal_activity || "";
    // if (
    //   userLegalActivity &&
    //   selectedStore.legalActivity !== userLegalActivity
    // ) {
    //   toast.error(
    //     "المتجر لا يتطابق مع نشاطك التجاري. للمتابعة، قم بإضافة النشاط إلى سجلك التجاري أو اتصل بالدعم."
    //   );
    //   return;
    // }

    setIsSubscriptionDialogOpen(true);
  };

  const handleSubscriptionSubmit = (values: SubscriptionFormValues) => {
    setIsSubscriptionDialogOpen(false);
    setIsProductSelectionOpen(true);
  };

  const handleSubmitRequest = async (
    products: HostingProduct[],
    notes: string
  ) => {
    if (!user || !selectedStore) return;

    const { duration, subscriptionType } = form.getValues();
    const totalQuantity = products.reduce(
      (acc, product) => acc + product.quantity,
      0
    );
    if (totalQuantity > selectedStore.capacity) {
      toast.error("عدد المنتجات المختارة يتجاوز سعة المتجر.");
      return;
    }
    await createHostingRequest(
      selectedStore,
      products,
      notes,
      user.id || "",
      user.name || "",
      user.legal_activity,
      duration,
      subscriptionType
    ).then(() => {
      setIsProductSelectionOpen(false);
      setSelectedProducts([]);
    });
  };

  function handleUpdateStore() {
    if (user?.role !== "admin") return;
    toast.promise(
      mutateAsync({
        storeId: selectedStore.id,
        featured,
        rating,
        subscription,
      }),
      {
        loading: "جاري تحديث المتجر...",
        success: () => {
          return "تم تحديث المتجر بنجاح";
        },
        error: () => {
          return "خطأ في تحديث المتجر";
        },
      }
    );
  }

  return (
    <>
      <Sheet open={!!selectedStore} onOpenChange={(open) => !open && onClose()}>
        <SheetContent className="w-full sm:max-w-md overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex justify-between items-center">
              {selectedStore.name}
              {featured && (
                <Badge className="bg-rfof-green text-white">متميز</Badge>
              )}
            </SheetTitle>
            <SheetDescription>معلومات تفصيلية عن المتجر</SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-6">
            {/* Gallery */}
            {selectedStore.gallery && selectedStore.gallery.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center">
                  <Camera className="h-4 w-4 ml-2" />
                  معرض الصور
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {selectedStore.gallery.map((image, index) => (
                    <div
                      key={index}
                      className="aspect-video overflow-hidden rounded-md"
                    >
                      <a href={image} target="_blank" rel="noopener noreferrer">
                        <img
                          src={image}
                          alt={`${selectedStore.name} - ${index + 1}`}
                          className="object-cover w-full h-full"
                        />
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Description */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">الوصف</h4>
              <p className="text-sm text-muted-foreground">
                {selectedStore.description || "لا يوجد وصف متاح"}
              </p>
            </div>

            {/* Details */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">معلومات الاتصال والموقع</h4>

              <div className="space-y-2 text-sm">
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mt-1 ml-2 text-muted-foreground" />
                  <span>{selectedStore.address}</span>
                </div>

                <div className="flex items-center">
                  <Clock className="h-4 w-4 ml-2 text-muted-foreground" />
                  <span>{selectedStore.hours}</span>
                </div>

                {user?.role === "admin" && (
                  <>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 ml-2 text-muted-foreground" />
                      <span>{selectedStore.phone || "لا يوجد رقم هاتف"}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 ml-2 text-muted-foreground" />
                      <span>
                        {selectedStore.email || "لا يوجد بريد إلكتروني"}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Capacity */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <h4 className="text-sm font-medium flex items-center">
                  <Package className="h-4 w-4 ml-2" />
                  سعة المخزون
                </h4>
                <Badge variant="outline" dir="ltr">
                  {selectedStore.shelf_space}
                </Badge>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-rfof-green h-2.5 rounded-full"
                  style={{
                    width: `${((selectedStore.capacity || 0) / 100) * 100}%`,
                  }}
                ></div>
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedStore.capacity} منتج
              </p>
            </div>

            {/* Legal Activity */}
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Briefcase className="h-4 w-4 ml-2 text-muted-foreground" />
                <Badge variant="outline">{selectedStore.legal_activity}</Badge>
              </div>
              <div className="mr-auto">
                <Badge variant="outline" className="flex gap-2">
                  <span>{selectedStore.subscription}</span>
                  <span>ر.س</span>
                </Badge>
              </div>
            </div>
            {user?.role === "admin" && (
              <>
                <div className="flex items-center justify-between gap-5 space-x-reverse mt-2">
                  <div className="gap-5 flex flex-col flex-1">
                    <Label htmlFor="rating">التقييم</Label>
                    <Input
                      type="number"
                      id="rating"
                      name="rating"
                      defaultValue={rating}
                      onChange={(e) => setRating(parseFloat(e.target.value))}
                      placeholder="أدخل تقييم المتجر"
                      min="0"
                      max="5"
                      step="0.5"
                    />
                  </div>
                  <div className="flex flex-col gap-5">
                    <Label htmlFor="featured">متجر مميز</Label>
                    <Switch
                      id="featured"
                      checked={featured}
                      onCheckedChange={(e) => setFeatured(e)}
                    />
                  </div>
                </div>
                <div className="flex flex-col gap-5">
                  <Label htmlFor="subscription">رسمي الاشتراك الثابت</Label>
                  <Input
                    type="number"
                    id="subscription"
                    name="subscription"
                    defaultValue={subscription}
                    onChange={(e) => setSupscripton(e.target.value)}
                    placeholder="أدخل قيمة الاشتراك الثابت"
                    min="0"
                    step="0.5"
                    required
                  />
                </div>
                <div className="pt-4">
                  <Button
                    className="w-full"
                    onClick={handleUpdateStore}
                    disabled={isUpdating}
                  >
                    {isUpdating ? "جاري المعالجة..." : "تحديث المتجر"}
                  </Button>
                </div>
              </>
            )}
            {user.role !== "admin" && (
              <div className="pt-4">
                <Button
                  className="w-full"
                  onClick={handleSendRequest}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "جاري المعالجة..." : "إرسال طلب استضافة"}
                </Button>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Subscription Options Dialog */}
      <Dialog
        open={isSubscriptionDialogOpen}
        onOpenChange={setIsSubscriptionDialogOpen}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>اختر خيارات الاستضافة</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubscriptionSubmit)}
              className="space-y-6"
            >
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>مدة الاستضافة</FormLabel>
                    <Select
                      disabled={true}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر المدة" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">شهر</SelectItem>
                        {/* <SelectItem value="3">3 أشهر</SelectItem>
                        <SelectItem value="6">6 أشهر</SelectItem>
                        <SelectItem value="12">سنة</SelectItem> */}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="subscriptionType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>نوع الاشتراك</FormLabel>
                    <Select
                      disabled={true}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر نوع الاشتراك" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="fixed">رسوم شهرية ثابتة</SelectItem>
                        {/* <SelectItem value="percentage">
                          عمولة على كل عملية بيع
                        </SelectItem> */}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsSubscriptionDialogOpen(false)}
                >
                  إلغاء
                </Button>
                <Button type="submit">متابعة</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <ProductSelectionModal
        isOpen={isProductSelectionOpen}
        onClose={() => setIsProductSelectionOpen(false)}
        selectedProducts={selectedProducts}
        setSelectedProducts={setSelectedProducts}
        onSave={handleSubmitRequest}
        storeName={selectedStore?.name || ""}
      />
    </>
  );
};
