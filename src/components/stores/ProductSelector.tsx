
import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Check, Plus } from 'lucide-react';
import { useProductOperations } from '@/hooks/useProductOperations';

interface ProductSelectorProps {
  selectedProducts: string[];
  setSelectedProducts: React.Dispatch<React.SetStateAction<string[]>>;
}

export const ProductSelector: React.FC<ProductSelectorProps> = ({
  selectedProducts,
  setSelectedProducts
}) => {
  const { userProducts } = useProductOperations();
  
  const toggleProductSelection = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };
  
  return (
    <div className="grid grid-cols-1 gap-2">
      <Label className="mb-2">اختيار المنتجات</Label>
      {userProducts.length === 0 ? (
        <div className="text-center p-4 border rounded-md">
          <p className="text-muted-foreground">لا توجد منتجات متاحة حالياً</p>
          <Button 
            variant="outline" 
            className="mt-2"
            onClick={() => window.location.href = '/dashboard/products/add'}
          >
            <Plus className="h-4 w-4 ml-1" />
            إضافة منتج جديد
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-40 overflow-y-auto p-2 border rounded-md">
          {userProducts.map((product) => (
            <div 
              key={product.id}
              className={`flex items-center p-2 rounded cursor-pointer ${
                selectedProducts.includes(product.id) ? 'bg-primary/10 border border-primary' : 'border'
              }`}
              onClick={() => toggleProductSelection(product.id)}
            >
              {product.images && product.images.length > 0 ? (
                <img 
                  src={product.images[0]} 
                  alt={product.name}
                  className="w-10 h-10 object-cover rounded ml-2"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-200 rounded ml-2 flex items-center justify-center">
                  <span className="text-xs">لا توجد صورة</span>
                </div>
              )}
              <div className="flex-1">
                <p className="text-sm font-medium line-clamp-1">{product.name}</p>
                <p className="text-xs text-muted-foreground">{product.price} ريال</p>
              </div>
              {selectedProducts.includes(product.id) && (
                <Check className="h-4 w-4 text-primary ml-2" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
