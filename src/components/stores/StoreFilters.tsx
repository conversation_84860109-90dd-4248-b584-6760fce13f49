import React from "react";
import { Filter, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StoreFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedCity: string;
  setSelectedCity: (city: string) => void;
  selectedActivity: string;
  setSelectedActivity: (activity: string) => void;
  shelfSize: string;
  setShelfSize: (size: string) => void;
  isFilterSheetOpen: boolean;
  setIsFilterSheetOpen: (isOpen: boolean) => void;
  resetFilters: () => void;
  cities: string[];
  activities: string[];
  shelfSizes: string[];
}

export const StoreFilters: React.FC<StoreFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  selectedCity,
  setSelectedCity,
  selectedActivity,
  setSelectedActivity,
  shelfSize,
  setShelfSize,
  isFilterSheetOpen,
  setIsFilterSheetOpen,
  resetFilters,
  cities,
  activities,
  shelfSizes,
}) => {
  return (
    <>
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="دوّر عن متجر..."
            className="pr-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        {/* <Button 
          variant="outline" 
          className="flex gap-2"
          onClick={() => setIsFilterSheetOpen(true)}
        >
          <Filter className="h-4 w-4" />
          <span>فلترة</span>
        </Button> */}
      </div>

      {/* Show active filters */}
      {(selectedCity || selectedActivity || shelfSize) && (
        <div className="flex flex-wrap gap-2 mb-4">
          {selectedCity && (
            <Badge variant="outline" className="flex items-center gap-1">
              المدينة: {selectedCity === "all" ? "الكل" : selectedCity}
              <button
                className="ml-1 text-xs"
                onClick={() => setSelectedCity("")}
              >
                ×
              </button>
            </Badge>
          )}
          {selectedActivity && (
            <Badge variant="outline" className="flex items-center gap-1">
              النشاط: {selectedActivity === "all" ? "الكل" : selectedActivity}
              <button
                className="ml-1 text-xs"
                onClick={() => setSelectedActivity("")}
              >
                ×
              </button>
            </Badge>
          )}
          {shelfSize && (
            <Badge variant="outline" className="flex items-center gap-1">
              حجم الرف: {shelfSize === "all" ? "الكل" : shelfSize}
              <button className="ml-1 text-xs" onClick={() => setShelfSize("")}>
                ×
              </button>
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="text-xs"
            onClick={resetFilters}
          >
            مسح الفلاتر
          </Button>
        </div>
      )}

      {/* Filters Sheet */}
      <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
        <SheetContent className="w-[300px] sm:w-[400px]">
          <SheetHeader>
            <SheetTitle>فلترة المحلات</SheetTitle>
            <SheetDescription>
              حدد خيارات الفلترة للبحث عن المحلات
            </SheetDescription>
          </SheetHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">المدينة</label>
              <Select value={selectedCity} onValueChange={setSelectedCity}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر المدينة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  {cities.map((city) => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">النشاط التجاري</label>
              <Select
                value={selectedActivity}
                onValueChange={setSelectedActivity}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر النشاط" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  {activities.map((activity) => (
                    <SelectItem key={activity} value={activity}>
                      {activity}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">حجم الرف</label>
              <Select value={shelfSize} onValueChange={setShelfSize}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر حجم الرف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  {shelfSizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={resetFilters}>
              إعادة ضبط
            </Button>
            <Button onClick={() => setIsFilterSheetOpen(false)}>
              تطبيق الفلاتر
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};
