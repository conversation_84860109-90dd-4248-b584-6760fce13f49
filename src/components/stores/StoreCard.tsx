import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, ChevronRight, RecycleIcon, Trash } from "lucide-react";
import { Store } from "@/types/store";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";

interface StoreCardProps {
  store: Store;
  onViewDetails: (store: Store) => void;
  onDelete?: (storeId: string) => void;
}

export const StoreCard: React.FC<StoreCardProps> = ({
  store,
  onViewDetails,
  onDelete,
}) => {
  return (
    <Dialog>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow group relative">
        {onDelete && (
          <>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                className=" absolute top-2 left-2 p-1 text-red-500 hover:bg-red-100 z-10"
              >
                <Trash className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="text-lg font-semibold">
                  تأكيد الحذف
                </DialogTitle>
                <DialogDescription className="mt-2 text-sm text-muted-foreground">
                  هل أنت متأكد أنك تريد حذف هذا المتجر؟
                </DialogDescription>
              </DialogHeader>
              <div className="mt-4 flex justify-end space-x-2">
                <Button variant="outline" onClick={() => onDelete(store.id)}>
                  حذف
                </Button>
              </div>
            </DialogContent>
          </>
        )}
        <div className="aspect-video relative">
          <img
            src={store.image}
            alt={store.name}
            className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
          />

          {store.featured && (
            <Badge className="absolute top-2 right-2 bg-rfof-green text-white">
              متميز
            </Badge>
          )}
          <div className="absolute bottom-2 right-2 flex items-center bg-black/70 text-white px-2 py-1 rounded-full">
            <Star className="h-3 w-3 fill-yellow-400 stroke-yellow-400 mr-1" />
            <span className="text-sm">{store.rating}</span>
          </div>
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2 flex justify-between">
            {store.name}
            <Badge variant="outline" className="mr-2" dir="ltr">
              {store.shelf_space}
            </Badge>
          </h3>
          <div className="flex items-center mb-2 justify-between">
            <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
              {store.description || `متجر في ${store.city}`}
            </p>
            {store.subscription && (
              <Badge variant="outline" className="mr-2" dir="rtl" size="sm">
                {store.subscription} ر.س
              </Badge>
            )}
          </div>

          <Button
            className="w-full flex items-center justify-center"
            variant="outline"
            onClick={() => onViewDetails(store)}
          >
            <span>عرض التفاصيل</span>
            <ChevronRight className="mr-2 h-4 w-4" />
          </Button>
        </CardContent>
      </Card>
    </Dialog>
  );
};
