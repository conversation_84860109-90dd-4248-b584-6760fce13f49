
import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface SubscriptionSelectorProps {
  subscriptionType: string;
  setSubscriptionType: (value: string) => void;
  duration: string;
  setDuration: (value: string) => void;
}

export const SubscriptionSelector: React.FC<SubscriptionSelectorProps> = ({
  subscriptionType,
  setSubscriptionType,
  duration,
  setDuration
}) => {
  // Get subscription types based on store settings
  const subscriptionTypes = [
    { value: "percentage", label: "عمولة على كل عملية بيع" },
    { value: "fixed", label: "رسوم شهرية ثابتة" }
  ];

  // Duration options in months
  const durations = [
    { value: "1", label: "شهر" },
    { value: "3", label: "3 أشهر" },
    { value: "6", label: "6 أشهر" },
    { value: "12", label: "سنة" }
  ];
  
  return (
    <>
      {/* Subscription Type */}
      <div className="grid grid-cols-3 gap-4 items-center">
        <Label htmlFor="subscription-type" className="text-right">
          طريقة الاشتراك
        </Label>
        <div className="col-span-2">
          <Select 
            value={subscriptionType} 
            onValueChange={setSubscriptionType}
          >
            <SelectTrigger>
              <SelectValue placeholder="اختر طريقة الاشتراك" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {subscriptionTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Duration */}
      <div className="grid grid-cols-3 gap-4 items-center">
        <Label htmlFor="duration" className="text-right">
          مدة الاشتراك
        </Label>
        <div className="col-span-2">
          <Select 
            value={duration} 
            onValueChange={setDuration}
          >
            <SelectTrigger>
              <SelectValue placeholder="اختر مدة الاشتراك" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {durations.map((duration) => (
                  <SelectItem key={duration.value} value={duration.value}>
                    {duration.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
    </>
  );
};
