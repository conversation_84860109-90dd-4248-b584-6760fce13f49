import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/auth';
import { useProductOperations } from '@/hooks/useProductOperations';
import { Store } from '@/types/store';
import { toast } from 'sonner';
import { SubscriptionSelector } from './SubscriptionSelector';
import { ProductSelector } from './ProductSelector';

interface HostingRequestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  store: Store | null;
  onSubmit: (data: any) => void;
}

export function HostingRequestDialog({ isOpen, onClose, store, onSubmit }: HostingRequestDialogProps) {
  const { user } = useA<PERSON>();
  const [duration, setDuration] = useState<string>("1");
  const [subscriptionType, setSubscriptionType] = useState<string>("percentage");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [notes, setNotes] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  if (!store) return null;

  const handleSubmit = async () => {
    if (selectedProducts.length === 0) {
      toast.error("يرجى اختيار منتج واحد على الأقل");
      return;
    }

    setIsSubmitting(true);
    try {
      const requestData = {
        store_id: store.id,
        store_name: store.name,
        ecommerce_id: user?.id,
        ecommerce_name: user?.name,
        duration: parseInt(duration),
        subscription_type: subscriptionType,
        products: selectedProducts,
        notes: notes,
        status: "pending"
      };
      
      await onSubmit(requestData);
      toast.success("تم إرسال طلب الاستضافة بنجاح");
      onClose();
    } catch (error) {
      console.error("Error submitting request:", error);
      toast.error("حدث خطأ أثناء إرسال الطلب");
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md md:max-w-xl">
        <DialogHeader>
          <DialogTitle>إنشاء طلب عرض منتجات</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-3 gap-4 items-center">
            <Label htmlFor="store-name" className="text-left">
              المحل التجاري
            </Label>
            <div className="col-span-2">
              <Input id="store-name" value={store.name} readOnly disabled />
            </div>
          </div>

          {/* Subscription Components */}
          <SubscriptionSelector 
            subscriptionType={subscriptionType}
            setSubscriptionType={setSubscriptionType}
            duration={duration}
            setDuration={setDuration}
          />

          {/* Products Selection */}
          <ProductSelector 
            selectedProducts={selectedProducts}
            setSelectedProducts={setSelectedProducts}
          />

          {/* Notes */}
          <div className="grid grid-cols-1 gap-2">
            <Label htmlFor="notes">ملاحظات</Label>
            <Textarea 
              id="notes" 
              value={notes} 
              onChange={(e) => setNotes(e.target.value)}
              placeholder="أي ملاحظات إضافية حول طلب عرض المنتجات"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            إلغاء
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting || selectedProducts.length === 0}>
            {isSubmitting ? 'جارٍ الإرسال...' : 'إرسال الطلب'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default HostingRequestDialog;
