
import React from 'react';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  withText?: boolean;
}

const RfofLogo: React.FC<LogoProps> = ({ className = "", size = 'md', withText = true }) => {
  const sizeMap = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16',
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className={`relative ${sizeMap[size]}`}>
        <img 
          src="/lovable-uploads/b9744226-b869-41bd-aab5-6e82459f913e.png" 
          alt="رفوف" 
          className={`${sizeMap[size]} w-auto`}
        />
      </div>
      {withText && (
        <span className="mr-2 text-rfof-darkBlue font-bold text-xl hidden sm:inline-block">رفوف</span>
      )}
    </div>
  );
};

export default RfofLogo;
