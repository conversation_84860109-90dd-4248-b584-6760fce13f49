import React, { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { ImagePlus, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client.ts";
import { toast } from "sonner";
import { uploadImage } from "@/utils/supabaseActions.ts";

// Form schema for branch
export const branchFormSchema = z.object({
  name: z
    .string()
    .min(3, { message: "يجب أن يكون اسم الفرع 3 أحرف على الأقل" }),
  address: z.string().min(5, { message: "يجب إدخال عنوان صحيح" }),
  city: z.string().min(2, { message: "يجب إدخال اسم المدينة" }),
  phoneNumber: z.string().min(10, { message: "يجب إدخال رقم هاتف صحيح" }),
  managerName: z.string().optional(),
  workingHours: z.string().min(3, { message: "يجب إدخال ساعات العمل" }),
  active: z.boolean().default(true),
  legalActivity: z.string().optional(),
});

export type BranchFormValues = z.infer<typeof branchFormSchema>;

interface BranchFormProps {
  defaultValues?: BranchFormValues;
  onSubmit: (values: BranchFormValues & { images: string[] }) => void;
  initialImages?: string[];
}

export const BranchForm: React.FC<BranchFormProps> = ({
  defaultValues,
  onSubmit,
  initialImages = [],
}) => {
  const [uploadedImages, setUploadedImages] = useState<string[]>(initialImages);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<BranchFormValues>({
    resolver: zodResolver(branchFormSchema),
    defaultValues: defaultValues || {
      name: "",
      address: "",
      city: "",
      phoneNumber: "",
      managerName: "",
      workingHours: "",
      active: true,
      legalActivity: "",
    },
  });

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files) return;

    const file = event.target.files[0];
    if (!file) return;

    uploadImage({
      storageName: "branches",
      file,
    })
      .then((publicUrl) => {
        console.log(publicUrl);

        setUploadedImages((prev) => [...prev, publicUrl]);
        fileInputRef.current!.value = ""; // Clear the input field
      })
      .catch((error) => console.log(error));
  };

  const removeImage = (index: number) => {
    const imageToRemove = uploadedImages[index];
    supabase.storage
      .from("branches")
      .remove([imageToRemove])
      .then(({ data, error }) => {
        if (error) {
          console.error("Error removing image:", error);
          return;
        }
        console.log("Image removed successfully:", data);
        setUploadedImages((prev) => prev.filter((_, i) => i !== index));
      });

    // Remove the image from the uploadedImages state
  };

  const handleSubmit = (values: BranchFormValues) => {
    onSubmit({ ...values, images: uploadedImages });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-4 max-h-[55vh] overflow-y-auto"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم الفرع</FormLabel>
              <FormControl>
                <Input placeholder="الفرع الرئيسي" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>المدينة</FormLabel>
                <FormControl>
                  <Input placeholder="الرياض" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>رقم الهاتف</FormLabel>
                <FormControl>
                  <Input placeholder="05xxxxxxxx" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>العنوان</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="شارع الملك فهد، حي الملز..."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="workingHours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ساعات العمل</FormLabel>
                <FormControl>
                  <Input placeholder="من 9 صباحاً إلى 11 مساءً" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="managerName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>اسم المدير (اختياري)</FormLabel>
                <FormControl>
                  <Input placeholder="اسم المدير" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="legalActivity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>النشاط التجاري (اختياري)</FormLabel>
              <FormControl>
                <Input placeholder="النشاط التجاري المسجل" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Image upload section */}
        <div className="space-y-2">
          <FormLabel>صور الفرع</FormLabel>
          <div className="flex flex-wrap gap-2 mb-2">
            {uploadedImages.map((image, index) => (
              <div
                key={index}
                className="relative h-20 w-20 rounded overflow-hidden"
              >
                <img
                  src={image}
                  alt={`صورة ${index + 1}`}
                  className="object-cover w-full h-full"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-0 right-0 h-5 w-5 rounded-full"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>

          <div className="flex items-center">
            <input
              placeholder="img"
              type="file"
              id="image-upload"
              ref={fileInputRef}
              accept="image/*"
              className="hidden"
              onChange={handleImageUpload}
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center"
            >
              <ImagePlus className="h-4 w-4 ml-2" />
              {uploadedImages.length > 0
                ? "إضافة صورة أخرى"
                : "إضافة صور للفرع"}
            </Button>
          </div>
          <FormDescription>
            يمكنك إضافة صور للفرع لعرضها للعملاء. يفضل استخدام صور بأبعاد
            متساوية.
          </FormDescription>
        </div>

        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
              <div className="space-y-0.5">
                <FormLabel>الفرع نشط</FormLabel>
                <FormDescription>
                  حدد ما إذا كان الفرع نشطاً ومفتوحاً حالياً
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit">حفظ</Button>
        </div>
      </form>
    </Form>
  );
};

export default BranchForm;
