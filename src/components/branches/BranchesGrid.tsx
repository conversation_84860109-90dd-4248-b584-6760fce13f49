
import React from 'react';
import { Branch } from '@/types/branch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import BranchCard from './BranchCard';
import { RefreshCw, Building2 } from 'lucide-react';

interface BranchesGridProps {
  branches: Branch[];
  isLoading: boolean;
  error: string | null;
  onRefresh: () => void;
  onEdit: (branch: Branch) => void;
  onDelete: (branch: Branch) => void;
  onAddNew: () => void;
}

const BranchesGrid: React.FC<BranchesGridProps> = ({
  branches,
  isLoading,
  error,
  onRefresh,
  onEdit,
  onDelete,
  onAddNew,
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>جاري تحميل الفروع...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={onRefresh} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (branches.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>قائمة الفروع</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-6">
            <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">لا توجد فروع. يمكنك إضافة فرع جديد الآن.</p>
            <Button onClick={onAddNew}>
              <Building2 className="h-4 w-4 ml-2" />
              إضافة فرع جديد
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {branches.map((branch) => (
        <BranchCard
          key={branch.id}
          branch={branch}
          onEdit={() => onEdit(branch)}
          onDelete={() => onDelete(branch)}
        />
      ))}
    </div>
  );
};

export default BranchesGrid;
