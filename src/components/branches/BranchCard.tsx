import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Branch } from "@/types/branch";
import { MapPin, Phone, Clock, User, Edit, Trash2 } from "lucide-react";

interface BranchCardProps {
  branch: Branch;
  onEdit: () => void;
  onDelete: () => void;
}

const BranchCard: React.FC<BranchCardProps> = ({
  branch,
  onEdit,
  onDelete,
}) => {
  // Default image if none provided
  const defaultImage =
    "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D";
  console.log(branch);

  return (
    <Card className={branch.active !== false ? "" : "opacity-70"}>
      <div className="relative h-48 overflow-hidden rounded-t-lg">
        <a
          href={
            branch.images && branch.images.length > 0
              ? branch.images[0]
              : defaultImage
          }
          target="_blank"
        >
          <img
            src={
              branch.images && branch.images.length > 0
                ? branch.images[0]
                : defaultImage
            }
            alt={branch.name}
            className="w-full h-full object-cover"
          />
        </a>
        <div className="absolute top-2 right-2">
          <Badge variant={branch.active !== false ? "default" : "secondary"}>
            {branch.active !== false ? "نشط" : "غير نشط"}
          </Badge>
        </div>
      </div>

      <CardHeader className="pb-2">
        <CardTitle>{branch.name}</CardTitle>
      </CardHeader>

      <CardContent className="space-y-2 pb-2">
        <div className="flex items-center text-sm">
          <MapPin className="h-4 w-4 ml-2 text-muted-foreground" />
          <span className="line-clamp-1">
            {branch.city} - {branch.address}
          </span>
        </div>

        <div className="flex items-center text-sm">
          <Phone className="h-4 w-4 ml-2 text-muted-foreground" />
          <span>{branch.phone_number}</span>
        </div>

        {branch.working_hours && (
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 ml-2 text-muted-foreground" />
            <span>{branch.working_hours}</span>
          </div>
        )}

        {branch.manager_name && (
          <div className="flex items-center text-sm">
            <User className="h-4 w-4 ml-2 text-muted-foreground" />
            <span>{branch.manager_name}</span>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-2">
        <div className="flex gap-2 w-full">
          <Button variant="outline" className="flex-1" onClick={onEdit}>
            <Edit className="h-4 w-4 ml-1" />
            تعديل
          </Button>
          <Button
            variant="outline"
            className="flex-1 text-red-600 hover:bg-red-50"
            onClick={onDelete}
          >
            <Trash2 className="h-4 w-4 ml-1" />
            حذف
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default BranchCard;
