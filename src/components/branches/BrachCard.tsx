import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Branch } from "@/types/branch";
import { Info, MapPin, Phone } from "lucide-react";

interface BranchCardProps {
  branch: Branch;
  onViewDetails: (branch: Branch) => void;
}

export function BranchCard({ branch, onViewDetails }: BranchCardProps) {
  return (
    <Card className="overflow-hidden">
      <div className="aspect-video w-full overflow-hidden">
        <a href={branch.images[0]} target="_blank" rel="noopener noreferrer">
          <img
            src={
              branch.images && branch.images.length > 0
                ? branch.images[0]
                : "https://via.placeholder.com/800x450?text=فرع"
            }
            alt={branch.name}
            className="w-full h-full object-cover"
          />
        </a>
      </div>
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold mb-2">{branch.name}</h3>
        <div className="flex items-center text-xs text-muted-foreground mb-2">
          <MapPin className="h-3 w-3 mr-1" />
          <span>
            {branch.address}, {branch.city}
          </span>
        </div>
        <div className="flex items-center text-xs text-muted-foreground">
          <Phone className="h-3 w-3 mr-1" />
          <span>{branch.phone_number}</span>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => onViewDetails(branch)}
        >
          <Info className="h-4 w-4 mr-2" />
          عرض التفاصيل
        </Button>
      </CardFooter>
    </Card>
  );
}
