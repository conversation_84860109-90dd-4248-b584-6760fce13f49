import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Branch } from '@/types/branch';
import { ImagePlus, X, ArrowRight, ArrowLeft, Building2, MapPin, Phone, Clock, User, Briefcase } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

const basicInfoSchema = z.object({
  name: z.string().min(3, { message: 'لازم اسم الفرع يكون 3 أحرف على الأقل' }),
  city: z.string().min(2, { message: 'لازم تدخل اسم المدينة' }),
  phoneNumber: z.string().min(10, { message: 'لازم تدخل رقم هاتف صحيح' }),
});

const contactInfoSchema = z.object({
  address: z.string().min(5, { message: 'لازم تدخل عنوان تفصيلي' }),
  workingHours: z.string().min(3, { message: 'لازم تدخل ساعات العمل' }),
  managerName: z.string().optional(),
});

const additionalInfoSchema = z.object({
  legalActivity: z.string().optional(),
  active: z.boolean().default(true),
});

export const branchFormSchema = z.object({
  name: z.string().min(3, { message: 'لازم اسم الفرع يكون 3 أحرف على الأقل' }),
  city: z.string().min(2, { message: 'لازم تدخل اسم المدينة' }),
  phoneNumber: z.string().min(10, { message: 'لازم تدخل رقم هاتف صحيح' }),
  address: z.string().min(5, { message: 'لازم تدخل عنوان تفصيلي' }),
  workingHours: z.string().min(3, { message: 'لازم تدخل ساعات العمل' }),
  managerName: z.string().optional(),
  legalActivity: z.string().optional(),
  active: z.boolean().default(true),
});

export type BranchFormValues = z.infer<typeof branchFormSchema>;

interface BranchWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: BranchFormValues & { images: string[] }) => Promise<boolean>;
  branch: Branch | null;
}

export const BranchWizard: React.FC<BranchWizardProps> = ({ 
  open, 
  onOpenChange, 
  onSubmit,
  branch 
}) => {
  const [step, setStep] = useState(1);
  const [uploadedImages, setUploadedImages] = useState<string[]>(branch?.images || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [progress, setProgress] = useState(33);
  
  const totalSteps = 3;

  const form = useForm<BranchFormValues>({
    resolver: zodResolver(branchFormSchema),
    defaultValues: branch ? {
      name: branch.name,
      address: branch.address,
      city: branch.city || '',
      phoneNumber: branch.phone_number,
      managerName: branch.manager_name || '',
      workingHours: branch.working_hours || '',
      active: branch.active !== undefined ? branch.active : true,
      legalActivity: branch.legal_activity || '',
    } : {
      name: '',
      address: '',
      city: '',
      phoneNumber: '',
      managerName: '',
      workingHours: '',
      active: true,
      legalActivity: '',
    }
  });
  
  const formValues = form.getValues();
  
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files) return;
    
    const file = event.target.files[0];
    if (!file) return;
    
    const imageUrl = URL.createObjectURL(file);
    setUploadedImages(prev => [...prev, imageUrl]);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };
  
  const goToNextStep = async () => {
    if (step === 1) {
      const result = await form.trigger(['name', 'city', 'phoneNumber']);
      if (!result) return;
      setStep(2);
      setProgress(66);
    } else if (step === 2) {
      const result = await form.trigger(['address', 'workingHours']);
      if (!result) return;
      setStep(3);
      setProgress(100);
    }
  };
  
  const goToPreviousStep = () => {
    if (step === 2) {
      setStep(1);
      setProgress(33);
    } else if (step === 3) {
      setStep(2);
      setProgress(66);
    }
  };

  const handleSubmit = async (values: BranchFormValues) => {
    setIsSubmitting(true);
    try {
      const success = await onSubmit({ ...values, images: uploadedImages });
      if (success) {
        onOpenChange(false);
        form.reset();
        setUploadedImages([]);
        setStep(1);
        setProgress(33);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setStep(1);
      setProgress(33);
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>{branch ? 'تعديل الفرع' : 'إضافة فرع جديد'}</DialogTitle>
          <DialogDescription>
            {branch
              ? 'تعديل معلومات الفرع'
              : 'إضافة فرع جديد للمتجر'}
          </DialogDescription>
        </DialogHeader>
        
        <Progress value={progress} className="h-2 mb-4" />
        <div className="text-center mb-4 text-sm text-muted-foreground">
          الخطوة {step} من {totalSteps}
        </div>
        
        {step === 1 && (
          <Form {...form}>
            <form className="space-y-4">
              <div className="flex items-center gap-2 mb-4 text-primary">
                <Building2 className="h-5 w-5" />
                <h3 className="text-lg font-medium">البيانات الأساسية</h3>
              </div>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>اسم الفرع</FormLabel>
                    <FormControl>
                      <Input placeholder="مثال: الفرع الرئيسي" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>المدينة</FormLabel>
                      <FormControl>
                        <Input placeholder="مثال: الرياض" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>رقم الهاتف</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input placeholder="05xxxxxxxx" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        )}
        
        {step === 2 && (
          <Form {...form}>
            <form className="space-y-4">
              <div className="flex items-center gap-2 mb-4 text-primary">
                <MapPin className="h-5 w-5" />
                <h3 className="text-lg font-medium">بيانات الاتصال</h3>
              </div>
              
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>العنوان التفصيلي</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="مثال: شارع الملك فهد، حي الملز، بجوار مستشفى الملك فهد" 
                        {...field}
                        className="min-h-[120px]" 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="workingHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ساعات العمل</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="مثال: من 9 صباحاً إلى 11 مساءً" {...field} />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="managerName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>اسم مدير الفرع (اختياري)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input placeholder="اسم المدير" {...field} />
                      </div>
                    </FormControl>
                    <FormDescription>
                      يمكنك إضافة اسم مدير الفرع لتسهيل التواصل معه
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
        
        {step === 3 && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="flex items-center gap-2 mb-4 text-primary">
                <Briefcase className="h-5 w-5" />
                <h3 className="text-lg font-medium">معلومات إضافية</h3>
              </div>
              
              <FormField
                control={form.control}
                name="legalActivity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>النشاط التجاري (اختياري)</FormLabel>
                    <FormControl>
                      <Input placeholder="النشاط التجاري المسجل" {...field} />
                    </FormControl>
                    <FormDescription>
                      أدخل النشاط التجاري كما هو مسجل في السجل التجاري
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Alert variant="default" className="bg-blue-50">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  يجب أن يتطابق النشاط التجاري مع ما هو مسجل في السجل التجاري أو وثيقة العمل الحر.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2">
                <FormLabel>صور الفرع</FormLabel>
                <div className="flex flex-wrap gap-2 mb-2">
                  {uploadedImages.map((image, index) => (
                    <div key={index} className="relative h-20 w-20 rounded overflow-hidden">
                      <img
                        src={image}
                        alt={`صورة ${index + 1}`}
                        className="object-cover w-full h-full"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-0 right-0 h-5 w-5 rounded-full"
                        onClick={() => removeImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
                
                <div className="flex items-center">
                  <input
                    type="file"
                    id="image-upload"
                    ref={fileInputRef}
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center"
                  >
                    <ImagePlus className="h-4 w-4 ml-2" />
                    {uploadedImages.length > 0 ? 'إضافة صورة أخرى' : 'إضافة صور للفرع'}
                  </Button>
                </div>
                <FormDescription>
                  يمكنك إضافة صور للفرع لعرضها للعملاء. يفضل استخدام صور بأبعاد متساوية.
                </FormDescription>
              </div>
              
              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>الفرع نشط</FormLabel>
                      <FormDescription>
                        حدد ما إذا كان الفرع نشطاً ومفتوحاً حالياً
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
        
        <DialogFooter className="flex justify-between items-center mt-4">
          {step > 1 ? (
            <Button type="button" variant="outline" onClick={goToPreviousStep}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              السابق
            </Button>
          ) : (
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              إلغاء
            </Button>
          )}
          
          {step < totalSteps ? (
            <Button type="button" onClick={goToNextStep}>
              التالي
              <ArrowRight className="h-4 w-4 mr-2" />
            </Button>
          ) : (
            <Button 
              type="button" 
              onClick={form.handleSubmit(handleSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : branch ? 'حفظ التعديلات' : 'إنشاء الفرع'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BranchWizard;
