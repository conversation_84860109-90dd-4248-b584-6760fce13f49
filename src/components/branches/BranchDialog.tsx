import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { BranchForm, BranchFormValues } from "./BranchForm";
import { Branch } from "@/types/branch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { toast, useAuth } from "@/contexts/auth";

interface BranchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (
    values: BranchFormValues & { images: string[] }
  ) => Promise<boolean>;
  branch: Branch | null;
}

export const BranchDialog: React.FC<BranchDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  branch,
}) => {
  const { user } = useAuth();
  const handleSubmit = (values: BranchFormValues & { images: string[] }) => {
    onSubmit(values);
    onOpenChange(false);
  };
  if (open && !user.legalDetails) {
    if (user.role !== "admin") {
      toast.error("يجب أن يكون لديك تفاصيل قانونية لإضافة فرع جديد");
      return null;
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>{branch ? "تعديل الفرع" : "إضافة فرع جديد"}</DialogTitle>
          <DialogDescription>
            {branch
              ? "قم بتعديل معلومات الفرع أدناه"
              : "أدخل معلومات الفرع الجديد"}
          </DialogDescription>
        </DialogHeader>

        <Alert variant="default" className="my-2 bg-blue-50">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            يجب أن يتطابق النشاط التجاري مع ما هو مسجل في السجل التجاري أو وثيقة
            العمل الحر.
          </AlertDescription>
        </Alert>

        <BranchForm
          defaultValues={
            branch
              ? {
                  name: branch.name,
                  address: branch.address,
                  city: branch.city || "",
                  phoneNumber: branch.phone_number  || "",
                  managerName: branch.manager_name || "",
                  workingHours: branch.working_hours || "",
                  active: branch.active !== undefined ? branch.active : true,
                  legalActivity: branch.legal_activity || "",
                }
              : undefined
          }
          initialImages={branch?.images || []}
          onSubmit={handleSubmit}
        />

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            إلغاء
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BranchDialog;
