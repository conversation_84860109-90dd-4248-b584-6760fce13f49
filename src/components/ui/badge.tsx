
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // إضافة المزيد من أنماط الشارات للأدوار المختلفة
        admin: 
          "border-red-200 bg-red-100 text-red-800 hover:bg-red-200",
        "sub-admin": 
          "border-orange-200 bg-orange-100 text-orange-800 hover:bg-orange-200",
        store: 
          "border-blue-200 bg-blue-100 text-blue-800 hover:bg-blue-200",
        ecommerce: 
          "border-green-200 bg-green-100 text-green-800 hover:bg-green-200",
        active: 
          "border-green-200 bg-green-100 text-green-800 hover:bg-green-200",
        inactive: 
          "border-gray-200 bg-gray-100 text-gray-800 hover:bg-gray-200",
        pending: 
          "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-1.5 py-0.25 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
