
import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorMessageProps {
  message?: string;
  description?: string;
  onRetry?: () => void;
  retry?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message = "حدث خطأ",
  description = "حدث خطأ أثناء تحميل البيانات، يرجى المحاولة مرة أخرى",
  onRetry,
  retry = true
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-96 p-8">
      <div className="text-center">
        <AlertTriangle className="h-16 w-16 text-yellow-500 mb-4 mx-auto" />
        <h2 className="text-xl font-bold text-gray-800 mb-2">{message}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        {retry && onRetry && (
          <Button onClick={onRetry} className="flex items-center">
            <RefreshCw className="h-4 w-4 ml-2" />
            إعادة المحاولة
          </Button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;
