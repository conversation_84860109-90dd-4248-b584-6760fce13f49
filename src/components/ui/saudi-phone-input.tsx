
import * as React from "react";
import { Input } from "@/components/ui/input";
import { formatSaudiPhoneNumber, isValidSaudiPhoneNumber } from "@/utils/phoneNumberUtils";

// Define our own InputProps interface based on HTML input element props
interface SaudiPhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value: string;
  onChange: (value: string) => void;
  onValidityChange?: (isValid: boolean) => void;
}

export const SaudiPhoneInput = React.forwardRef<HTMLInputElement, SaudiPhoneInputProps>(
  ({ value, onChange, onValidityChange, className, ...props }, ref) => {
    // Track if the input is valid
    const [isValid, setIsValid] = React.useState<boolean>(
      isValidSaudiPhoneNumber(value)
    );

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Get the raw input value
      const inputValue = e.target.value;
      
      // Remove any non-digit characters except + at the beginning
      const sanitizedValue = inputValue.replace(/[^\d+]/g, '');
      
      // Format the phone number
      const formattedValue = formatSaudiPhoneNumber(sanitizedValue);
      
      // Check validity
      const valid = isValidSaudiPhoneNumber(formattedValue);
      setIsValid(valid);
      
      // Call the parent's onChange
      onChange(formattedValue);
      
      // Notify parent about validity change if callback provided
      if (onValidityChange) {
        onValidityChange(valid);
      }
    };

    return (
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span className="text-gray-500 sm:text-sm">+966</span>
        </div>
        <Input
          ref={ref}
          value={value}
          onChange={handleChange}
          className={`pl-14 ${isValid ? '' : 'border-red-500'} ${className}`}
          placeholder="05XXXXXXXX"
          type="tel"
          maxLength={10}
          {...props}
        />
        {!isValid && value && (
          <p className="mt-1 text-sm text-red-500">
            الرجاء إدخال رقم هاتف سعودي صالح (05XXXXXXXX)
          </p>
        )}
      </div>
    );
  }
);

SaudiPhoneInput.displayName = "SaudiPhoneInput";
