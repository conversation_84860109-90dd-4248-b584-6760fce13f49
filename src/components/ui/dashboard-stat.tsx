
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowUp, ArrowDown, Minus, LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DashboardStatProps {
  title: string;
  value: string;
  icon: LucideIcon;
  description?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  className?: string;
  isLoading?: boolean;
}

export function DashboardStat({
  title,
  value,
  icon: Icon,
  description,
  trend = 'stable',
  trendValue,
  className,
  isLoading = false,
}: DashboardStatProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline gap-2">
              <h4 className="text-3xl font-bold">
                {isLoading ? (
                  <div className="h-8 w-16 animate-pulse bg-muted rounded"></div>
                ) : (
                  value
                )}
              </h4>
              {trend && !isLoading && (
                <div 
                  className={cn(
                    "flex items-center text-xs font-medium",
                    trend === 'up' && "text-green-500",
                    trend === 'down' && "text-red-500",
                    trend === 'stable' && "text-gray-500"
                  )}
                >
                  {trend === 'up' ? (
                    <ArrowUp className="h-3 w-3 ml-1" />
                  ) : trend === 'down' ? (
                    <ArrowDown className="h-3 w-3 ml-1" />
                  ) : (
                    <Minus className="h-3 w-3 ml-1" />
                  )}
                  {trendValue}
                </div>
              )}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          <div className={cn(
            "p-2 rounded-full",
            trend === 'up' && "bg-green-100 text-green-600",
            trend === 'down' && "bg-red-100 text-red-600",
            trend === 'stable' && "bg-blue-100 text-blue-600"
          )}>
            <Icon className="h-5 w-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
