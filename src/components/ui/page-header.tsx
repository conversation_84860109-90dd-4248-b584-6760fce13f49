
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from './button';
import { ChevronRight } from 'lucide-react';

export interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  actions?: React.ReactNode;
  backLink?: string; // link for backward navigation
  backButtonLink?: string; // alias for backLink 
}

export function PageHeader({
  title,
  description,
  icon: Icon,
  actions,
  backLink,
  backButtonLink,
}: PageHeaderProps) {
  // Use backButtonLink as alias for backLink
  const navBackLink = backLink || backButtonLink;

  return (
    <div className="flex flex-col md:flex-row md:items-start md:justify-between pb-6 mb-6 border-b">
      <div className="flex flex-col gap-1 mb-4 md:mb-0">
        {navBackLink && (
          <Button
            variant="ghost"
            className="mb-2 mr-auto -ml-3 h-8 w-fit"
            asChild
          >
            <Link to={navBackLink}>
              <ChevronRight className="mr-1 h-4 w-4" />
              العودة
            </Link>
          </Button>
        )}
        <div className="flex items-center">
          {Icon && <Icon className="ml-2 h-6 w-6" />}
          <h1 className="text-xl font-bold tracking-tight">{title}</h1>
        </div>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      {actions && <div className="flex gap-2 justify-end">{actions}</div>}
    </div>
  );
}
