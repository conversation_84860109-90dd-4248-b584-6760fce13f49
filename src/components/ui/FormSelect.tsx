
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FormSelectProps {
  options: { label: string; value: string }[];
  selected: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export const FormSelect: React.FC<FormSelectProps> = ({
  options,
  selected,
  onValueChange,
  placeholder = "Select an option",
  disabled = false,
}) => {
  return (
    <Select 
      value={selected} 
      onValueChange={onValueChange} 
      disabled={disabled}
    >
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
