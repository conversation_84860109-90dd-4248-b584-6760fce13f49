
import React, { createContext, useContext, useState, ReactNode } from 'react';

type Direction = 'rtl' | 'ltr';

interface DirectionContextProps {
  direction: Direction;
  setDirection: (direction: Direction) => void;
}

const DirectionContext = createContext<DirectionContextProps | undefined>(undefined);

interface DirectionProviderProps {
  children: ReactNode;
  defaultDirection?: Direction;
}

export function DirectionProvider({ 
  children, 
  defaultDirection = 'rtl' 
}: DirectionProviderProps) {
  const [direction, setDirection] = useState<Direction>(defaultDirection);

  return (
    <DirectionContext.Provider value={{ direction, setDirection }}>
      <div dir={direction} className={direction}>
        {children}
      </div>
    </DirectionContext.Provider>
  );
}

export function useDirection() {
  const context = useContext(DirectionContext);
  if (context === undefined) {
    throw new Error('useDirection must be used within a DirectionProvider');
  }
  return context;
}
