
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Image, Upload, X } from 'lucide-react';

interface ImageUploadPreviewProps {
  label?: string;
  onChange: (file: File | null) => void;
  value?: string;
  className?: string;
  maxSizeMB?: number;
  accept?: string;
}

const ImageUploadPreview: React.FC<ImageUploadPreviewProps> = ({
  label,
  onChange,
  value,
  className = '',
  maxSizeMB = 5,
  accept = 'image/*',
}) => {
  const [preview, setPreview] = useState<string | null>(value || null);
  const [error, setError] = useState<string | null>(null);
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const file = event.target.files?.[0];
    
    if (!file) {
      return;
    }
    
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      setError(`حجم الملف كبير جداً. يجب أن يكون أقل من ${maxSizeMB} ميجابايت`);
      return;
    }
    
    // Generate preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    
    // Call the onChange prop
    onChange(file);
  };
  
  const handleRemove = () => {
    setPreview(null);
    setError(null);
    onChange(null);
  };
  
  return (
    <div className={`space-y-2 ${className}`}>
      {label && <Label>{label}</Label>}
      
      {preview ? (
        <div className="relative">
          <img 
            src={preview} 
            alt="صورة معاينة" 
            className="w-full h-auto rounded-lg object-cover max-h-[200px]" 
          />
          <Button
            type="button"
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2 rounded-full p-1 h-8 w-8"
            onClick={handleRemove}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
          <Upload className="h-10 w-10 text-gray-400 mb-2" />
          <p className="text-sm text-gray-500 mb-2">اضغط أو اسحب لتحميل صورة</p>
          <Input
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
            id="image-upload"
          />
          <Label htmlFor="image-upload" className="cursor-pointer">
            <Button type="button" variant="outline">
              <Image className="h-4 w-4 ml-2" />
              اختر ملف
            </Button>
          </Label>
        </div>
      )}
      
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default ImageUploadPreview;
