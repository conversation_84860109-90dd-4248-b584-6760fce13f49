import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import RfofLogo from "./RfofLogo";
import { useAuth } from "@/contexts/auth";
import { UserRole } from "@/types";

// Import the components
import NavItem from "./navbar/NavItem";
import MobileMenu from "./navbar/MobileMenu";
import UserMenu from "./navbar/UserMenu";
import {
  useNavItems,
  getIconComponent,
  SupportedIcon,
} from "./navbar/NavItems";
import { useIsMobile } from "@/hooks/use-mobile";
import { NavItem as NavItemType } from "./navbar/types";

interface TopNavbarProps {
  onMenuClick?: () => void;
}

export const TopNavbar: React.FC<TopNavbarProps> = ({ onMenuClick }) => {
  const { user, logout, isAuthenticated } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();
  const navItemsUtil = useNavItems();

  // Check if we're in the dashboard area
  const isDashboard = location.pathname.startsWith("/dashboard");

  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  // Get appropriate nav items based on authentication state
  const getNavItems = () => {
    if (!isAuthenticated) {
      return [
        {
          href: "/login",
          label: "تسجيل الدخول",
          icon: null,
        },
        {
          href: "/register",
          label: "إنشاء حساب",
          icon: null,
        },
        {
          href: "/about",
          label: "من نحن",
          icon: null,
        },
        // {
        //   href: '/contact',
        //   label: 'اتصل بنا',
        //   icon: null,
        // },
      ];
    }

    // If authenticated, include user-specific items including legal-details
    const userSpecificItems = [];

    if (user && (user.role === "store" || user.role === "ecommerce")) {
      userSpecificItems.push({
        href: "/dashboard/legal-details",
        label: "البيانات القانونية",
        icon: "ScrollText",
      });
    }

    // Return all relevant navigation items
    return [
      ...navItemsUtil.commonItems.slice(0, 1), // Just dashboard
      {
        href: "/dashboard/notifications",
        label: "الإشعارات",
        icon: "Bell",
      },
      ...userSpecificItems,
      ...navItemsUtil.ecommerceItems
        .slice(0, 2)
        .filter(
          (item) =>
            !item.roles || (user && item.roles.includes(user.role as UserRole))
        ),
      navItemsUtil.settingsItem,
    ];
  };

  const navItems = getNavItems();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center gap-2">
            {isMobile && isAuthenticated && isDashboard && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onMenuClick}
                className="md:hidden"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">القائمة</span>
              </Button>
            )}

            {isMobile && !isDashboard && (
              <MobileMenu
                isOpen={isMobileMenuOpen}
                onOpenChange={setIsMobileMenuOpen}
                navItems={navItems as NavItemType[]}
                onItemClick={closeMobileMenu}
              />
            )}

            <Link to="/" className="flex items-center">
              <RfofLogo />
            </Link>
          </div>

          <nav className="hidden md:flex md:gap-4">
            {navItems.map((item, index) => {
              const IconComponent = item.icon
                ? getIconComponent(item.icon as SupportedIcon)
                : null;

              return (
                <NavItem
                  key={index}
                  href={item.href}
                  label={typeof item.label === "string" ? item.label : "قائمة"}
                  icon={
                    IconComponent ? (
                      <IconComponent className="h-4 w-4 ml-1" />
                    ) : null
                  }
                />
              );
            })}
          </nav>

          <div className="flex items-center gap-2">
            {isAuthenticated ? (
              <UserMenu user={user} logout={logout} />
            ) : (
              <Link to="/login">
                <Button variant="rfof" size="sm" className="px-5">
                  تسجيل الدخول
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopNavbar;
