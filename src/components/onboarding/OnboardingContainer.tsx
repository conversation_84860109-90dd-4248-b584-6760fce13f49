import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/components/ui/use-toast";
import { onboardingService } from "@/services/onboardingService";
import {
  OnboardingStep,
  OnboardingProgress,
  OnboardingConfig,
} from "@/types/onboarding";
import WelcomeScreen from "./WelcomeScreen";
import OnboardingStepContainer from "./OnboardingStepContainer";
import ProgressComponent from "./OnboardingProgress";

interface OnboardingContainerProps {
  userType: "store" | "ecommerce";
}

const OnboardingContainer: React.FC<OnboardingContainerProps> = ({
  userType,
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [currentView, setCurrentView] = useState<"welcome" | "onboarding">(
    "welcome"
  );
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [config, setConfig] = useState<OnboardingConfig | null>(null);
  const [progress, setProgress] = useState<OnboardingProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeOnboarding();
  }, [user, userType]);

  const initializeOnboarding = async () => {
    if (!user) return;

    try {
      // Get onboarding configuration
      const onboardingConfig = onboardingService.getOnboardingConfig(userType);
      setConfig(onboardingConfig);

      // Get user's progress
      const { data: userProgress, error } =
        await onboardingService.getUserOnboardingProgress(user.id);

      if (error && error.code !== "PGRST116") {
        // PGRST116 is "not found" error
        console.error("Error fetching onboarding progress:", error);
      }

      if (userProgress) {
        setProgress(userProgress);
        setCurrentStepIndex(userProgress.current_step);

        // If user has already seen welcome screen, go directly to onboarding
        if (userProgress.completed_steps.includes("welcome")) {
          setCurrentView("onboarding");
        }
      } else {
        // Create initial progress
        const initialProgress: OnboardingProgress = {
          user_id: user.id,
          current_step: 0,
          completed_steps: [],
          total_steps: onboardingConfig.steps.length,
          is_completed: false,
        };

        const { data: newProgress, error: createError } =
          await onboardingService.saveOnboardingProgress(initialProgress);

        if (createError) {
          console.error("Error creating onboarding progress:", createError);
        } else {
          setProgress(newProgress?.[0] || initialProgress);
        }
      }
    } catch (error) {
      console.error("Error initializing onboarding:", error);
      toast({
        title: "خطأ في تحميل الإعداد",
        description:
          "حدث خطأ أثناء تحميل معلومات الإعداد. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOnboarding = async () => {
    if (!user || !config) return;

    try {
      // Mark welcome step as completed
      await onboardingService.completeStep(user.id, "welcome");

      // Update local progress
      setProgress((prev) =>
        prev
          ? {
              ...prev,
              completed_steps: [...prev.completed_steps, "welcome"],
            }
          : null
      );

      setCurrentView("onboarding");
    } catch (error) {
      console.error("Error starting onboarding:", error);
      toast({
        title: "خطأ في بدء الإعداد",
        description: "حدث خطأ أثناء بدء عملية الإعداد. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const handleSkipWelcome = () => {
    navigate("/dashboard");
  };

  const handleStepComplete = async (stepId: string, stepData?: any) => {
    if (!user || !config || !progress) return;

    try {
      // Mark step as completed
      await onboardingService.completeStep(user.id, stepId);

      // Save step data if provided
      if (stepData) {
        await onboardingService.saveProfileData(user.id, stepData);
      }

      // Update local progress
      const updatedCompletedSteps = [...progress.completed_steps];
      if (!updatedCompletedSteps.includes(stepId)) {
        updatedCompletedSteps.push(stepId);
      }

      const nextStepIndex = currentStepIndex + 1;
      const isCompleted = nextStepIndex >= config.steps.length;

      const updatedProgress: OnboardingProgress = {
        ...progress,
        current_step: isCompleted ? config.steps.length : nextStepIndex,
        completed_steps: updatedCompletedSteps,
        is_completed: isCompleted,
      };

      // Save updated progress
      await onboardingService.saveOnboardingProgress(updatedProgress);
      setProgress(updatedProgress);

      if (isCompleted) {
        // Onboarding completed
        toast({
          title: "تم إكمال الإعداد بنجاح!",
          description: "تهانينا! لقد أكملت إعداد حسابك بنجاح.",
        });
        navigate("/dashboard");
      } else {
        // Move to next step
        setCurrentStepIndex(nextStepIndex);
      }
    } catch (error) {
      console.error("Error completing step:", error);
      toast({
        title: "خطأ في حفظ البيانات",
        description: "حدث خطأ أثناء حفظ بياناتك. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  const handleStepBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    } else {
      setCurrentView("welcome");
    }
  };

  const handleSkipStep = async (stepId: string) => {
    if (!user || !config || !progress) return;

    try {
      const nextStepIndex = currentStepIndex + 1;
      const isCompleted = nextStepIndex >= config.steps.length;

      const updatedProgress: OnboardingProgress = {
        ...progress,
        current_step: isCompleted ? config.steps.length : nextStepIndex,
        is_completed: isCompleted,
      };

      await onboardingService.saveOnboardingProgress(updatedProgress);
      setProgress(updatedProgress);

      if (isCompleted) {
        navigate("/dashboard");
      } else {
        setCurrentStepIndex(nextStepIndex);
      }
    } catch (error) {
      console.error("Error skipping step:", error);
      toast({
        title: "خطأ في تخطي الخطوة",
        description: "حدث خطأ أثناء تخطي الخطوة. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  if (isLoading || !config) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الإعداد...</p>
        </div>
      </div>
    );
  }

  const currentStep = config.steps[currentStepIndex];
  const progressPercentage = progress
    ? (progress.completed_steps.length / config.steps.length) * 100
    : 0;

  console.log(currentView);

  return (
    <div className="min-h-screen bg-gray-50">
      <AnimatePresence mode="wait">
        {currentView === "welcome" ? (
          <motion.div
            key="welcome"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.5 }}
          >
            <WelcomeScreen
              data={config.welcome_screen}
              onStartOnboarding={handleStartOnboarding}
              onSkip={handleSkipWelcome}
            />
          </motion.div>
        ) : (
          <motion.div
            key="onboarding"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.5 }}
            className="min-h-screen"
          >
            {/* Progress Header */}
            <div className="bg-white border-b border-gray-200 px-4 py-4">
              <div className="max-w-4xl mx-auto">
                <ProgressComponent
                  currentStep={currentStepIndex + 1}
                  totalSteps={config.steps.length}
                  completedSteps={progress?.completed_steps || []}
                  steps={config.steps}
                  progressPercentage={progressPercentage}
                />
              </div>
            </div>

            {/* Step Content */}
            <div className="max-w-4xl mx-auto p-4">
              {currentStep && (
                <OnboardingStepContainer
                  step={currentStep}
                  userType={userType}
                  onComplete={handleStepComplete}
                  onBack={handleStepBack}
                  onSkip={handleSkipStep}
                  isFirstStep={currentStepIndex === 0}
                  isLastStep={currentStepIndex === config.steps.length - 1}
                />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OnboardingContainer;
