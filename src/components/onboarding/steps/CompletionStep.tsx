import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Gift, Star, ArrowLeft, Sparkles } from 'lucide-react';

interface CompletionStepProps {
  userType: 'store' | 'ecommerce';
  onComplete: (data: any) => void;
  onBack: () => void;
  onSkip: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const CompletionStep: React.FC<CompletionStepProps> = ({
  userType,
  onComplete,
  onBack,
  onSkip,
  isFirstStep,
  isLastStep
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleComplete = async () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onComplete({
        completion: {
          completed: true,
          timestamp: new Date().toISOString(),
          onboarding_completed: true
        }
      });
      setIsLoading(false);
    }, 1000);
  };

  const rewards = [
    {
      icon: <Star className="w-6 h-6" />,
      title: '100 نقطة مكافآت',
      description: 'تم إضافتها إلى حسابك',
      color: 'bg-yellow-100 text-yellow-700'
    },
    {
      icon: <Gift className="w-6 h-6" />,
      title: 'شهر مجاني من الخدمات المتقدمة',
      description: 'استمتع بجميع الميزات المتقدمة',
      color: 'bg-purple-100 text-purple-700'
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: 'دعم أولوية',
      description: 'احصل على دعم سريع ومخصص',
      color: 'bg-green-100 text-green-700'
    }
  ];

  const nextSteps = userType === 'store' ? [
    'إضافة منتجاتك الأولى',
    'إعداد نظام نقاط البيع',
    'دعوة فريق العمل',
    'استكشاف التقارير والتحليلات'
  ] : [
    'إضافة منتجاتك الأولى',
    'تخصيص متجرك الإلكتروني',
    'إعداد وسائل الدفع',
    'تفعيل خدمات الشحن'
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Celebration Header */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="text-center"
      >
        <div className="relative">
          <div className="w-24 h-24 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="absolute -top-2 -right-2 left-0 right-0 mx-auto w-fit"
          >
            <Sparkles className="w-8 h-8 text-yellow-500" />
          </motion.div>
        </div>
        
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-3xl font-bold text-gray-900 mb-2"
        >
          🎉 تهانينا!
        </motion.h2>
        
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-xl text-gray-600"
        >
          لقد أكملت إعداد حسابك بنجاح
        </motion.p>
      </motion.div>

      {/* Rewards Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 text-center">مكافآت إكمال الإعداد</h3>
            <div className="space-y-3">
              {rewards.map((reward, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                  className="flex items-center gap-4 p-3 bg-white rounded-lg shadow-sm"
                >
                  <div className={`p-2 rounded-full ${reward.color}`}>
                    {reward.icon}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{reward.title}</p>
                    <p className="text-sm text-gray-600">{reward.description}</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    تم التفعيل
                  </Badge>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Next Steps */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">الخطوات التالية المقترحة</h3>
            <div className="grid md:grid-cols-2 gap-3">
              {nextSteps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                  className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-blue-600 text-sm font-bold">{index + 1}</span>
                  </div>
                  <p className="text-sm font-medium text-gray-700">{step}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Action Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1.2 }}
        className="text-center"
      >
        <Button
          onClick={handleComplete}
          disabled={isLoading}
          size="lg"
          className="px-12 py-3 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              جاري الانتقال...
            </div>
          ) : (
            <>
              ابدأ استخدام المنصة
              <ArrowLeft className="mr-2 h-5 w-5" />
            </>
          )}
        </Button>
        
        <p className="text-sm text-gray-500 mt-3">
          سيتم توجيهك إلى لوحة التحكم الرئيسية
        </p>
      </motion.div>

      {/* Support Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 1.4 }}
        className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center"
      >
        <p className="text-sm text-blue-800">
          <span className="font-medium">هل تحتاج مساعدة؟</span>
          {' '}فريق الدعم متاح على مدار الساعة لمساعدتك في البدء.
        </p>
        <Button variant="link" className="text-blue-600 p-0 h-auto mt-1">
          تواصل مع الدعم
        </Button>
      </motion.div>
    </motion.div>
  );
};

export default CompletionStep;
