import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Settings, Bell, Globe, Palette } from 'lucide-react';

interface PreferencesStepProps {
  userType: 'store' | 'ecommerce';
  onComplete: (data: any) => void;
  onBack: () => void;
  onSkip: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const PreferencesStep: React.FC<PreferencesStepProps> = ({
  onComplete,
  onBack,
  onSkip,
  isFirstStep,
  isLastStep
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleComplete = async () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onComplete({
        preferences: {
          completed: true,
          timestamp: new Date().toISOString()
        }
      });
      setIsLoading(false);
    }, 1000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <Card className="border-dashed border-2 border-gray-300">
        <CardContent className="p-8 text-center">
          <Settings className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-semibold mb-2">التفضيلات والإعدادات</h3>
          <p className="text-gray-600 mb-6">
            هذه الخطوة قيد التطوير. سيتم إضافة نموذج مفصل لإعداد التفضيلات.
          </p>
          
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
              <Globe className="w-6 h-6 text-blue-500" />
              <div className="text-left">
                <p className="font-medium">اللغة والمنطقة</p>
                <p className="text-sm text-gray-600">سيتم إضافته قريباً</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
              <Bell className="w-6 h-6 text-green-500" />
              <div className="text-left">
                <p className="font-medium">إعدادات الإشعارات</p>
                <p className="text-sm text-gray-600">سيتم إضافته قريباً</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
              <Palette className="w-6 h-6 text-purple-500" />
              <div className="text-left">
                <p className="font-medium">المظهر والثيم</p>
                <p className="text-sm text-gray-600">سيتم إضافته قريباً</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
              <Settings className="w-6 h-6 text-orange-500" />
              <div className="text-left">
                <p className="font-medium">إعدادات عامة</p>
                <p className="text-sm text-gray-600">سيتم إضافته قريباً</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="flex justify-end gap-4"
      >
        <Button
          onClick={handleComplete}
          disabled={isLoading}
          size="lg"
          className="px-8"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              جاري المتابعة...
            </div>
          ) : (
            'متابعة'
          )}
        </Button>
      </motion.div>
    </motion.div>
  );
};

export default PreferencesStep;
