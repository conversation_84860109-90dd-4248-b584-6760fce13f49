import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { User, Phone, Mail, Camera } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { onboardingService } from "@/services/onboardingService";
import { useToast } from "@/components/ui/use-toast";

const personalInfoSchema = z.object({
  name: z.string().min(2, "الاسم يجب أن يكون حرفين على الأقل"),
  phone: z.string().min(10, "رقم الهاتف يجب أن يكون 10 أرقام على الأقل"),
  email: z.string().email("البريد الإلكتروني غير صحيح"),
  avatar: z.string().optional(),
});

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;

interface PersonalInfoStepProps {
  userType: "store" | "ecommerce";
  onComplete: (data: any) => void;
  onBack: () => void;
  onSkip: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const PersonalInfoStep: React.FC<PersonalInfoStepProps> = ({
  onComplete,
  onBack,
  onSkip,
  isFirstStep,
  isLastStep,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string>("");

  const form = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      name: user?.name || "",
      phone: user?.phone_number || "",
      email: user?.email || "",
      avatar: user?.avatar || "",
    },
  });

  useEffect(() => {
    // Load existing profile data
    loadExistingData();
  }, [user]);

  const loadExistingData = async () => {
    if (!user) return;

    try {
      const { data: profileData } = await onboardingService.getProfileData(
        user.id
      );

      if (profileData?.personal_info) {
        const personalInfo = profileData.personal_info;
        form.reset({
          name: personalInfo.name || user.name || "",
          phone: personalInfo.phone || user.phone_number || "",
          email: personalInfo.email || user.email || "",
          avatar: personalInfo.avatar || user.avatar || "",
        });

        if (personalInfo.avatar) {
          setAvatarPreview(personalInfo.avatar);
        }
      }
    } catch (error) {
      console.error("Error loading profile data:", error);
    }
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "حجم الملف كبير جداً",
          description: "يرجى اختيار صورة أصغر من 5 ميجابايت",
          variant: "destructive",
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "نوع الملف غير مدعوم",
          description: "يرجى اختيار ملف صورة صحيح",
          variant: "destructive",
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setAvatarPreview(result);
        form.setValue("avatar", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: PersonalInfoFormData) => {
    setIsLoading(true);

    try {
      const profileData = {
        personal_info: {
          name: data.name,
          phone: data.phone,
          email: data.email,
          avatar: data.avatar || avatarPreview,
        },
      };

      await onboardingService.saveProfileData(user.id, profileData);

      toast({
        title: "تم حفظ المعلومات الشخصية",
        description: "تم حفظ معلوماتك الشخصية بنجاح",
      });
    } catch (error) {
      console.error("Error saving personal info:", error);
      toast({
        title: "خطأ في حفظ البيانات",
        description: "حدث خطأ أثناء حفظ معلوماتك. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Avatar Upload Section */}
        <Card className="border-dashed border-2 border-gray-300 hover:border-primary transition-colors">
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <Avatar className="w-24 h-24">
                  <AvatarImage src={avatarPreview || form.watch("avatar")} />
                  <AvatarFallback className="text-2xl">
                    {form.watch("name")?.charAt(0)?.toUpperCase() || (
                      <User className="w-8 h-8" />
                    )}
                  </AvatarFallback>
                </Avatar>
                <label
                  htmlFor="avatar-upload"
                  className="absolute -bottom-2 -right-2 bg-primary text-white rounded-full p-2 cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  <Camera className="w-4 h-4" />
                </label>
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-gray-900">
                  صورة الملف الشخصي
                </p>
                <p className="text-xs text-gray-500">
                  اختياري - يمكنك إضافة صورتك الشخصية
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Fields */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Name Field */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-2"
          >
            <Label
              htmlFor="name"
              className="text-sm font-medium flex items-center gap-2"
            >
              <User className="w-4 h-4" />
              الاسم الكامل *
            </Label>
            <Input
              id="name"
              {...form.register("name")}
              placeholder="أدخل اسمك الكامل"
              className="h-12"
            />
            {form.formState.errors.name && (
              <p className="text-sm text-red-600">
                {form.formState.errors.name.message}
              </p>
            )}
          </motion.div>

          {/* Phone Field */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-2"
          >
            <Label
              htmlFor="phone"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Phone className="w-4 h-4" />
              رقم الهاتف *
            </Label>
            <Input
              id="phone"
              {...form.register("phone")}
              placeholder="05xxxxxxxx"
              className="h-12"
              dir="ltr"
            />
            {form.formState.errors.phone && (
              <p className="text-sm text-red-600">
                {form.formState.errors.phone.message}
              </p>
            )}
          </motion.div>

          {/* Email Field */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-2 md:col-span-2"
          >
            <Label
              htmlFor="email"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Mail className="w-4 h-4" />
              البريد الإلكتروني *
            </Label>
            <Input
              id="email"
              type="email"
              {...form.register("email")}
              placeholder="<EMAIL>"
              className="h-12"
              dir="ltr"
            />
            {form.formState.errors.email && (
              <p className="text-sm text-red-600">
                {form.formState.errors.email.message}
              </p>
            )}
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex justify-end gap-4 pt-6"
        >
          <Button type="submit" disabled={isLoading} size="lg" className="px-8">
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </div>
            ) : (
              "حفظ والمتابعة"
            )}
          </Button>
        </motion.div>
      </form>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="bg-blue-50 border border-blue-200 rounded-lg p-4"
      >
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-blue-600 text-sm">💡</span>
          </div>
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">نصائح مهمة:</p>
            <ul className="space-y-1 text-blue-700">
              <li>• تأكد من صحة رقم الهاتف للتواصل معك</li>
              <li>• البريد الإلكتروني سيُستخدم لإرسال التحديثات المهمة</li>
              <li>• يمكنك تغيير هذه المعلومات لاحقاً من الإعدادات</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PersonalInfoStep;
