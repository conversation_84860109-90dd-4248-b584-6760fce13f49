import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Building, FileText, Globe, Calendar, Tag } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { onboardingService } from "@/services/onboardingService";
import { useToast } from "@/components/ui/use-toast";

const businessInfoSchema = z.object({
  business_name: z.string().min(2, "اسم النشاط التجاري مطلوب"),
  business_type: z.string().min(1, "نوع النشاط التجاري مطلوب"),
  industry: z.string().min(1, "القطاع مطلوب"),
  description: z.string().min(10, "وصف النشاط يجب أن يكون 10 أحرف على الأقل"),
  website: z.string().url("رابط الموقع غير صحيح").optional().or(z.literal("")),
  established_year: z
    .number()
    .min(1900, "سنة التأسيس غير صحيحة")
    .max(new Date().getFullYear(), "سنة التأسيس لا يمكن أن تكون في المستقبل")
    .optional(),
});

type BusinessInfoFormData = z.infer<typeof businessInfoSchema>;

interface BusinessInfoStepProps {
  userType: "store" | "ecommerce";
  onComplete: (data: any) => void;
  onBack: () => void;
  onSkip: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const BusinessInfoStep: React.FC<BusinessInfoStepProps> = ({
  userType,
  onComplete,
  onBack,
  onSkip,
  isFirstStep,
  isLastStep,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const businessTypes =
    userType === "store"
      ? [
          { value: "retail", label: "تجارة تجزئة" },
          { value: "wholesale", label: "تجارة جملة" },
          { value: "restaurant", label: "مطعم" },
          { value: "cafe", label: "مقهى" },
          { value: "pharmacy", label: "صيدلية" },
          { value: "supermarket", label: "سوبر ماركت" },
          { value: "clothing", label: "ملابس" },
          { value: "electronics", label: "إلكترونيات" },
          { value: "beauty", label: "تجميل" },
          { value: "automotive", label: "قطع غيار سيارات" },
          { value: "other", label: "أخرى" },
        ]
      : [
          { value: "ecommerce_general", label: "متجر إلكتروني عام" },
          { value: "fashion", label: "أزياء وموضة" },
          { value: "electronics", label: "إلكترونيات" },
          { value: "home_garden", label: "منزل وحديقة" },
          { value: "health_beauty", label: "صحة وجمال" },
          { value: "sports", label: "رياضة" },
          { value: "books", label: "كتب" },
          { value: "toys", label: "ألعاب" },
          { value: "food", label: "طعام" },
          { value: "handmade", label: "منتجات يدوية" },
          { value: "digital", label: "منتجات رقمية" },
          { value: "other", label: "أخرى" },
        ];

  const industries = [
    { value: "retail", label: "التجارة" },
    { value: "food_beverage", label: "الأطعمة والمشروبات" },
    { value: "fashion", label: "الأزياء والموضة" },
    { value: "technology", label: "التكنولوجيا" },
    { value: "health", label: "الصحة" },
    { value: "beauty", label: "التجميل" },
    { value: "automotive", label: "السيارات" },
    { value: "home", label: "المنزل والديكور" },
    { value: "sports", label: "الرياضة" },
    { value: "education", label: "التعليم" },
    { value: "entertainment", label: "الترفيه" },
    { value: "services", label: "الخدمات" },
    { value: "other", label: "أخرى" },
  ];

  const form = useForm<BusinessInfoFormData>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      business_name: "",
      business_type: "",
      industry: "",
      description: "",
      website: "",
      established_year: undefined,
    },
  });

  useEffect(() => {
    loadExistingData();
  }, [user]);

  const loadExistingData = async () => {
    if (!user) return;

    try {
      // Load existing profile data from profiles table
      const { data: profileData } = await onboardingService.getUserProfile(
        user.id
      );

      if (profileData) {
        form.reset({
          business_name: profileData.business_name || "",
          business_type: profileData.business_type || "",
          industry: profileData.industry || "",
          description: profileData.description || "",
          website: profileData.website || "",
          established_year: profileData.established_year || undefined,
        });
      }
    } catch (error) {
      console.error("Error loading business data:", error);
    }
  };

  const onSubmit = async (data: BusinessInfoFormData) => {
    setIsLoading(true);

    try {
      // Save business info directly to profiles table
      const businessInfo = {
        legal_activity: data.business_type,
        // Note: You may need to add more fields to profiles table for business info
        // or create a separate business_info table if needed
      };

      await onboardingService.saveBusinessInfo(user.id, businessInfo);

      // Mark step as completed and move to next step
      onComplete({
        step_completed: "business_info",
        timestamp: new Date().toISOString(),
      });

      toast({
        title: "تم حفظ معلومات النشاط التجاري",
        description: "تم حفظ معلومات نشاطك التجاري بنجاح",
      });
    } catch (error) {
      console.error("Error saving business info:", error);
      toast({
        title: "خطأ في حفظ البيانات",
        description:
          "حدث خطأ أثناء حفظ معلومات النشاط. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Business Name */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-2"
          >
            <Label
              htmlFor="business_name"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Building className="w-4 h-4" />
              اسم النشاط التجاري *
            </Label>
            <Input
              id="business_name"
              {...form.register("business_name")}
              placeholder="أدخل اسم نشاطك التجاري"
              className="h-12"
            />
            {form.formState.errors.business_name && (
              <p className="text-sm text-red-600">
                {form.formState.errors.business_name.message}
              </p>
            )}
          </motion.div>

          {/* Business Type */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-2"
          >
            <Label
              htmlFor="business_type"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Tag className="w-4 h-4" />
              نوع النشاط *
            </Label>
            <Select
              onValueChange={(value) => form.setValue("business_type", value)}
            >
              <SelectTrigger className="h-12">
                <SelectValue placeholder="اختر نوع النشاط" />
              </SelectTrigger>
              <SelectContent>
                {businessTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.business_type && (
              <p className="text-sm text-red-600">
                {form.formState.errors.business_type.message}
              </p>
            )}
          </motion.div>

          {/* Industry */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-2"
          >
            <Label
              htmlFor="industry"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Tag className="w-4 h-4" />
              القطاع *
            </Label>
            <Select onValueChange={(value) => form.setValue("industry", value)}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="اختر القطاع" />
              </SelectTrigger>
              <SelectContent>
                {industries.map((industry) => (
                  <SelectItem key={industry.value} value={industry.value}>
                    {industry.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.industry && (
              <p className="text-sm text-red-600">
                {form.formState.errors.industry.message}
              </p>
            )}
          </motion.div>

          {/* Established Year */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-2"
          >
            <Label
              htmlFor="established_year"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              سنة التأسيس (اختياري)
            </Label>
            <Input
              id="established_year"
              type="number"
              min="1900"
              max={new Date().getFullYear()}
              {...form.register("established_year", { valueAsNumber: true })}
              placeholder="مثال: 2020"
              className="h-12"
            />
            {form.formState.errors.established_year && (
              <p className="text-sm text-red-600">
                {form.formState.errors.established_year.message}
              </p>
            )}
          </motion.div>

          {/* Website */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="space-y-2 md:col-span-2"
          >
            <Label
              htmlFor="website"
              className="text-sm font-medium flex items-center gap-2"
            >
              <Globe className="w-4 h-4" />
              الموقع الإلكتروني (اختياري)
            </Label>
            <Input
              id="website"
              type="url"
              {...form.register("website")}
              placeholder="https://www.example.com"
              className="h-12"
              dir="ltr"
            />
            {form.formState.errors.website && (
              <p className="text-sm text-red-600">
                {form.formState.errors.website.message}
              </p>
            )}
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="space-y-2 md:col-span-2"
          >
            <Label
              htmlFor="description"
              className="text-sm font-medium flex items-center gap-2"
            >
              <FileText className="w-4 h-4" />
              وصف النشاط التجاري *
            </Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="اكتب وصفاً مختصراً عن نشاطك التجاري ونوع المنتجات أو الخدمات التي تقدمها"
              className="min-h-[100px]"
              rows={4}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">
                {form.formState.errors.description.message}
              </p>
            )}
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="flex justify-end gap-4 pt-6"
        >
          <Button type="submit" disabled={isLoading} size="lg" className="px-8">
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </div>
            ) : (
              "حفظ والمتابعة"
            )}
          </Button>
        </motion.div>
      </form>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="bg-green-50 border border-green-200 rounded-lg p-4"
      >
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-green-600 text-sm">💼</span>
          </div>
          <div className="text-sm text-green-800">
            <p className="font-medium mb-1">نصائح لوصف نشاطك التجاري:</p>
            <ul className="space-y-1 text-green-700">
              <li>• اذكر نوع المنتجات أو الخدمات التي تقدمها</li>
              <li>• أضف ما يميزك عن المنافسين</li>
              <li>• استخدم كلمات واضحة ومفهومة</li>
              <li>• يمكنك تعديل هذه المعلومات لاحقاً</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default BusinessInfoStep;
