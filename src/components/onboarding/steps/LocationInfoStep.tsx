import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Globe, Building2, Hash } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { onboardingService } from '@/services/onboardingService';
import { useToast } from '@/components/ui/use-toast';

const locationInfoSchema = z.object({
  country: z.string().min(1, 'الدولة مطلوبة'),
  city: z.string().min(1, 'المدينة مطلوبة'),
  address: z.string().min(5, 'العنوان يجب أن يكون 5 أحرف على الأقل'),
  postal_code: z.string().optional(),
});

type LocationInfoFormData = z.infer<typeof locationInfoSchema>;

interface LocationInfoStepProps {
  userType: 'store' | 'ecommerce';
  onComplete: (data: any) => void;
  onBack: () => void;
  onSkip: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

const LocationInfoStep: React.FC<LocationInfoStepProps> = ({
  userType,
  onComplete,
  onBack,
  onSkip,
  isFirstStep,
  isLastStep
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const countries = [
    { value: 'SA', label: 'المملكة العربية السعودية' },
    { value: 'AE', label: 'الإمارات العربية المتحدة' },
    { value: 'KW', label: 'الكويت' },
    { value: 'QA', label: 'قطر' },
    { value: 'BH', label: 'البحرين' },
    { value: 'OM', label: 'عمان' },
    { value: 'JO', label: 'الأردن' },
    { value: 'LB', label: 'لبنان' },
    { value: 'EG', label: 'مصر' },
    { value: 'MA', label: 'المغرب' },
    { value: 'TN', label: 'تونس' },
    { value: 'DZ', label: 'الجزائر' },
  ];

  const saudiCities = [
    'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'الظهران',
    'تبوك', 'بريدة', 'خميس مشيط', 'حائل', 'نجران', 'الطائف', 'الجبيل', 'ينبع',
    'أبها', 'عرعر', 'سكاكا', 'جازان', 'القطيف', 'الأحساء', 'الباحة', 'القريات'
  ];

  const form = useForm<LocationInfoFormData>({
    resolver: zodResolver(locationInfoSchema),
    defaultValues: {
      country: 'SA', // Default to Saudi Arabia
      city: '',
      address: '',
      postal_code: '',
    },
  });

  const selectedCountry = form.watch('country');

  useEffect(() => {
    loadExistingData();
  }, [user]);

  const loadExistingData = async () => {
    if (!user) return;

    try {
      const { data: profileData } = await onboardingService.getProfileData(user.id);
      
      if (profileData?.location_info) {
        const locationInfo = profileData.location_info;
        form.reset({
          country: locationInfo.country || 'SA',
          city: locationInfo.city || '',
          address: locationInfo.address || '',
          postal_code: locationInfo.postal_code || '',
        });
      }
    } catch (error) {
      console.error('Error loading location data:', error);
    }
  };

  const onSubmit = async (data: LocationInfoFormData) => {
    setIsLoading(true);
    
    try {
      const profileData = {
        location_info: {
          country: data.country,
          city: data.city,
          address: data.address,
          postal_code: data.postal_code || undefined,
        }
      };

      onComplete(profileData);
      
      toast({
        title: 'تم حفظ معلومات الموقع',
        description: 'تم حفظ معلومات موقعك بنجاح',
      });
    } catch (error) {
      console.error('Error saving location info:', error);
      toast({
        title: 'خطأ في حفظ البيانات',
        description: 'حدث خطأ أثناء حفظ معلومات الموقع. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Country */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-2"
          >
            <Label htmlFor="country" className="text-sm font-medium flex items-center gap-2">
              <Globe className="w-4 h-4" />
              الدولة *
            </Label>
            <Select 
              onValueChange={(value) => form.setValue('country', value)}
              defaultValue={form.getValues('country')}
            >
              <SelectTrigger className="h-12">
                <SelectValue placeholder="اختر الدولة" />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country) => (
                  <SelectItem key={country.value} value={country.value}>
                    {country.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.country && (
              <p className="text-sm text-red-600">{form.formState.errors.country.message}</p>
            )}
          </motion.div>

          {/* City */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-2"
          >
            <Label htmlFor="city" className="text-sm font-medium flex items-center gap-2">
              <Building2 className="w-4 h-4" />
              المدينة *
            </Label>
            {selectedCountry === 'SA' ? (
              <Select onValueChange={(value) => form.setValue('city', value)}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="اختر المدينة" />
                </SelectTrigger>
                <SelectContent>
                  {saudiCities.map((city) => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                id="city"
                {...form.register('city')}
                placeholder="أدخل اسم المدينة"
                className="h-12"
              />
            )}
            {form.formState.errors.city && (
              <p className="text-sm text-red-600">{form.formState.errors.city.message}</p>
            )}
          </motion.div>

          {/* Address */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-2 md:col-span-2"
          >
            <Label htmlFor="address" className="text-sm font-medium flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              العنوان التفصيلي *
            </Label>
            <Input
              id="address"
              {...form.register('address')}
              placeholder={userType === 'store' 
                ? "مثال: شارع الملك فهد، حي العليا، مجمع الرياض التجاري" 
                : "عنوان المكتب أو المستودع (إن وجد)"
              }
              className="h-12"
            />
            {form.formState.errors.address && (
              <p className="text-sm text-red-600">{form.formState.errors.address.message}</p>
            )}
          </motion.div>

          {/* Postal Code */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-2"
          >
            <Label htmlFor="postal_code" className="text-sm font-medium flex items-center gap-2">
              <Hash className="w-4 h-4" />
              الرمز البريدي (اختياري)
            </Label>
            <Input
              id="postal_code"
              {...form.register('postal_code')}
              placeholder="مثال: 12345"
              className="h-12"
              dir="ltr"
            />
            {form.formState.errors.postal_code && (
              <p className="text-sm text-red-600">{form.formState.errors.postal_code.message}</p>
            )}
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="flex justify-end gap-4 pt-6"
        >
          <Button
            type="submit"
            disabled={isLoading}
            size="lg"
            className="px-8"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </div>
            ) : (
              'حفظ والمتابعة'
            )}
          </Button>
        </motion.div>
      </form>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="bg-purple-50 border border-purple-200 rounded-lg p-4"
      >
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-purple-600 text-sm">📍</span>
          </div>
          <div className="text-sm text-purple-800">
            <p className="font-medium mb-1">
              {userType === 'store' 
                ? 'أهمية معلومات الموقع للمحلات التجارية:' 
                : 'أهمية معلومات الموقع للمتاجر الإلكترونية:'
              }
            </p>
            <ul className="space-y-1 text-purple-700">
              {userType === 'store' ? (
                <>
                  <li>• يساعد العملاء في العثور على محلك</li>
                  <li>• يُستخدم في خدمات التوصيل والشحن</li>
                  <li>• مطلوب للتراخيص والوثائق الرسمية</li>
                </>
              ) : (
                <>
                  <li>• يُستخدم في حساب تكاليف الشحن</li>
                  <li>• مطلوب للفواتير والوثائق الرسمية</li>
                  <li>• يساعد في تحديد المناطق المخدومة</li>
                </>
              )}
              <li>• يمكنك تعديل هذه المعلومات لاحقاً</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default LocationInfoStep;
