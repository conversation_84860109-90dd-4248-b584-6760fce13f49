
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/auth';

// Define shipping details schema
const shippingSchema = z.object({
  recipient_name: z.string().min(3, { message: "يرجى إدخال اسم المستلم كاملاً" }),
  phone_number: z.string().min(10, { message: "يرجى إدخال رقم هاتف صحيح" }),
  address: z.string().min(10, { message: "يرجى إدخال عنوان تفصيلي" }),
  city: z.string().min(2, { message: "يرجى اختيار المدينة" }),
  postal_code: z.string().min(5, { message: "يرجى إدخال الرمز البريدي" }),
  shipping_method: z.string({ required_error: "يرجى اختيار طريقة التوصيل" }),
  notes: z.string().optional(),
});

type ShippingDetailsFormValues = z.infer<typeof shippingSchema>;

interface ShippingDetailsFormProps {
  onSubmit?: (data: ShippingDetailsFormValues) => void;
}

const ShippingDetailsForm: React.FC<ShippingDetailsFormProps> = ({ onSubmit: onSubmitCallback }) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Set up form with react-hook-form and zod validation
  const form = useForm<ShippingDetailsFormValues>({
    resolver: zodResolver(shippingSchema),
    defaultValues: {
      recipient_name: user?.name || '',
      phone_number: user?.phone_number || '',
      address: user?.address || '',
      city: '',
      postal_code: '',
      shipping_method: 'standard',
      notes: '',
    }
  });

  // Handle form submission
  const handleSubmit = async (values: ShippingDetailsFormValues) => {
    setIsSubmitting(true);
    try {
      // Check if a callback was provided
      if (onSubmitCallback) {
        onSubmitCallback(values);
      } else {
        // Default behavior - simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('Shipping details saved:', values);
        toast.success('تم حفظ بيانات الشحن بنجاح');
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ بيانات الشحن');
      console.error('Error saving shipping details:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const saudiCities = [
    "الرياض", "جدة", "مكة المكرمة", "المدينة المنورة", "الدمام", "الخبر",
    "الطائف", "تبوك", "أبها", "حائل", "جازان", "نجران", "الباحة", "سكاكا",
    "عرعر", "القصيم", "بريدة", "الهفوف"
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 py-4">
        <FormField
          control={form.control}
          name="recipient_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم المستلم</FormLabel>
              <FormControl>
                <Input placeholder="أدخل اسم المستلم كاملاً" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>رقم الجوال</FormLabel>
              <FormControl>
                <Input placeholder="05XXXXXXXX" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>المدينة</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المدينة" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {saudiCities.map(city => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>العنوان التفصيلي</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="الحي، الشارع، رقم المبنى" 
                  className="resize-none"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="postal_code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الرمز البريدي</FormLabel>
                <FormControl>
                  <Input placeholder="أدخل الرمز البريدي" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="shipping_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>طريقة الشحن</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر طريقة الشحن" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="standard">قياسي (3-5 أيام)</SelectItem>
                    <SelectItem value="express">سريع (1-2 يوم)</SelectItem>
                    <SelectItem value="same_day">توصيل في نفس اليوم</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ملاحظات إضافية (اختياري)</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="أي تفاصيل إضافية للتوصيل" 
                  className="resize-none"
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                يمكنك إضافة أي تعليمات خاصة بالتسليم مثل الوقت المفضل للتوصيل
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? 'جاري الحفظ...' : 'حفظ بيانات الشحن'}
        </Button>
      </form>
    </Form>
  );
};

export default ShippingDetailsForm;

// Also add a named export
export { ShippingDetailsForm };
