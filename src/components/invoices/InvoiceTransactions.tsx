
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

// Define the interface for the component props
export interface InvoiceTransactionsProps {
  invoiceId: string;
}

// Sample transaction data (we'd normally fetch this from an API)
const sampleTransactions = [
  {
    id: 'txn-001',
    date: new Date(2024, 3, 1),
    type: 'payment',
    amount: 1725,
    status: 'completed',
    description: 'دفع فاتورة استضافة منتجات'
  },
  {
    id: 'txn-002',
    date: new Date(2024, 3, 1),
    type: 'system',
    amount: null,
    status: 'completed',
    description: 'إنشاء فاتورة استضافة منتجات'
  }
];

export const InvoiceTransactions: React.FC<InvoiceTransactionsProps> = ({ invoiceId }) => {
  // In a real app, we would fetch transactions based on the invoiceId
  const transactions = sampleTransactions;
  const loading = false;

  return (
    <Card>
      <CardHeader>
        <CardTitle>سجل المعاملات</CardTitle>
        <CardDescription>تاريخ المعاملات المرتبطة بهذه الفاتورة</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : transactions.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>التاريخ</TableHead>
                <TableHead>الوصف</TableHead>
                <TableHead>المبلغ</TableHead>
                <TableHead>الحالة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">
                    {formatDate(transaction.date.toString())}
                  </TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>
                    {transaction.amount ? `${transaction.amount} ريال` : '-'}
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={
                        transaction.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : transaction.status === 'pending'
                          ? 'bg-amber-100 text-amber-800'
                          : 'bg-red-100 text-red-800'
                      }
                    >
                      {transaction.status === 'completed' && 'مكتملة'}
                      {transaction.status === 'pending' && 'قيد الانتظار'}
                      {transaction.status === 'failed' && 'فشلت'}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6 text-muted-foreground">
            لا توجد معاملات لهذه الفاتورة
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InvoiceTransactions;
