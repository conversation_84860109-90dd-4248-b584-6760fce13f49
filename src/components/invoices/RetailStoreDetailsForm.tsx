
import React from 'react';
import { Input } from '@/components/ui/input';
import { RetailStoreDetails } from '@/types';
import { Label } from '@/components/ui/label';

export interface RetailStoreDetailsFormProps {
  formData: RetailStoreDetails;
  onChange?: (data: RetailStoreDetails) => void;
  readOnly?: boolean; // Added readOnly property
}

export const RetailStoreDetailsForm: React.FC<RetailStoreDetailsFormProps> = ({ 
  formData, 
  onChange, 
  readOnly = false  // Default to false
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (readOnly) return; // Don't update if readOnly is true
    
    const { name, value } = e.target;
    onChange?.({
      ...formData,
      [name]: value
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="legalName">اسم المنشأة</Label>
        <Input
          id="legalName"
          name="legalName"
          value={formData.legalName || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="registrationNumber">رقم السجل التجاري</Label>
        <Input
          id="registrationNumber"
          name="registrationNumber"
          value={formData.registrationNumber || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tax_number">الرقم الضريبي</Label>
        <Input
          id="tax_number"
          name="tax_number"
          value={formData.tax_number || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="contactPhone">رقم الهاتف</Label>
        <Input
          id="contactPhone"
          name="contactPhone"
          value={formData.contact_phone || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="address">العنوان</Label>
        <Input
          id="address"
          name="address"
          value={formData.address || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">البريد الإلكتروني</Label>
        <Input
          id="email"
          name="email"
          type="email"
          value={formData.email || formData.contact_email || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bankAccount">الحساب البنكي (IBAN)</Label>
        <Input
          id="bankAccount"
          name="bankAccount"
          value={formData.bankAccount || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="legalActivity">النشاط التجاري</Label>
        <Input
          id="legalActivity"
          name="legalActivity"
          value={formData.legalActivity || ''}
          onChange={handleChange}
          readOnly={readOnly}
        />
      </div>
    </div>
  );
};

export default RetailStoreDetailsForm;
