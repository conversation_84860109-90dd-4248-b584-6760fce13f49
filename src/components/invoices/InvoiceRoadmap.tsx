
import React from 'react';
import { InvoiceStage } from '@/types';
import { Card } from '@/components/ui/card';
import { Check, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InvoiceRoadmapProps {
  stages: InvoiceStage[];
  currentStage?: number;
  onStageClick?: (stage: InvoiceStage) => void;
}

export const InvoiceRoadmap: React.FC<InvoiceRoadmapProps> = ({
  stages,
  currentStage = 0,
  onStageClick
}) => {
  if (!stages || stages.length === 0) {
    return null;
  }

  const handleStageClick = (stage: InvoiceStage) => {
    if (onStageClick) {
      onStageClick(stage);
    }
  };

  return (
    <div className="w-full">
      {/* Mobile view - vertical roadmap */}
      <div className="lg:hidden space-y-3">
        {stages.map((stage) => (
          <Card
            key={stage.id}
            className={cn(
              "p-4 relative cursor-pointer transition-all",
              stage.completed ? "border-green-500 bg-green-50" : 
                stage.current ? "border-blue-500 bg-blue-50" : 
                "opacity-60"
            )}
            onClick={() => handleStageClick(stage)}
          >
            <div className="flex items-center">
              <div 
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center mr-3", 
                  stage.completed ? "bg-green-500 text-white" : 
                  stage.current ? "bg-blue-500 text-white" : 
                  "bg-gray-200 text-gray-500"
                )}
              >
                {stage.completed ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{stage.id}</span>
                )}
              </div>
              <div>
                <h3 className="font-medium">{stage.name}</h3>
                <p className="text-sm text-gray-500">{stage.description}</p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Desktop view - horizontal roadmap */}
      <div className="hidden lg:block">
        <div className="flex justify-between items-center relative">
          {/* Progress bar */}
          <div className="absolute inset-x-0 top-1/2 h-0.5 bg-gray-200 -translate-y-1/2 z-0"></div>
          
          {/* Stages */}
          {stages.map((stage) => {
            const isCompleted = stage.completed;
            const isCurrent = stage.current;
            
            return (
              <div 
                key={stage.id} 
                className={cn(
                  "relative z-10 flex flex-col items-center space-y-2",
                  isCompleted || isCurrent ? "cursor-pointer" : "opacity-60"
                )}
                onClick={() => (isCompleted || isCurrent) && handleStageClick(stage)}
              >
                {/* Stage indicator */}
                <div 
                  className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center", 
                    isCompleted ? "bg-green-500 text-white" : 
                    isCurrent ? "bg-blue-500 text-white" : 
                    "bg-gray-200 text-gray-500"
                  )}
                >
                  {isCompleted ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    <span>{stage.id}</span>
                  )}
                </div>
                
                {/* Stage label */}
                <div className="text-center w-24">
                  <p className={cn(
                    "font-medium text-sm",
                    isCompleted ? "text-green-700" :
                    isCurrent ? "text-blue-700" :
                    "text-gray-500"
                  )}>
                    {stage.name}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Current stage details */}
        {stages.find(s => s.current) && (
          <Card className="mt-8 p-4 border-blue-200 bg-blue-50">
            <div className="flex items-start">
              <Clock className="h-5 w-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-blue-700">
                  {stages.find(s => s.current)?.name}
                </h3>
                <p className="text-sm text-blue-600">
                  {stages.find(s => s.current)?.description}
                </p>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default InvoiceRoadmap;
