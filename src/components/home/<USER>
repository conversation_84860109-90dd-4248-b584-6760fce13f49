
import React from 'react';
import { Package, Store, ShoppingCart } from 'lucide-react';

interface ShelfDisplayProps {
  className?: string;
}

export function ShelfDisplay({ className = '' }: ShelfDisplayProps) {
  return (
    <div className={`mb-16 relative max-w-4xl mx-auto ${className}`}>
      <div className="w-full bg-wood-texture h-8 rounded-t-md shadow-md"></div>
      <div className="w-full bg-gradient-to-r from-rfof-blue/20 via-rfof-blue/30 to-rfof-blue/20 h-64 flex justify-center items-center relative border border-rfof-blue/30 rounded-b-md shadow-lg">
        <div className="absolute -top-3 left-0 right-0 h-3 bg-gradient-to-b from-transparent to-black/5"></div>
        
        {/* Items on Shelf */}
        <div className="flex justify-around items-end absolute bottom-0 left-0 right-0 px-8 pb-4">
          <div className="w-16 md:w-20 h-28 md:h-32 bg-white rounded-md shadow-lg flex flex-col justify-center items-center transform hover:-translate-y-2 transition-transform">
            <Package className="h-8 w-8 md:h-10 md:w-10 text-rfof-green mb-2" />
            <div className="text-xs font-semibold text-center">منتج إلكتروني</div>
          </div>
          
          <div className="w-20 md:w-24 h-36 md:h-40 bg-white rounded-md shadow-lg flex flex-col justify-center items-center transform hover:-translate-y-2 transition-transform">
            <Store className="h-10 w-10 md:h-12 md:w-12 text-rfof-blue mb-2" />
            <div className="text-xs font-semibold text-center">محل تجاري</div>
          </div>
          
          <div className="w-16 md:w-20 h-28 md:h-32 bg-white rounded-md shadow-lg flex flex-col justify-center items-center transform hover:-translate-y-2 transition-transform">
            <ShoppingCart className="h-8 w-8 md:h-10 md:w-10 text-indigo-500 mb-2" />
            <div className="text-xs font-semibold text-center">تسوق سهل</div>
          </div>
        </div>
        
        {/* Shelf Support */}
        <div className="absolute -bottom-4 left-1/4 w-2 h-12 bg-gradient-to-b from-rfof-blue/70 to-rfof-blue/50 rounded"></div>
        <div className="absolute -bottom-4 right-1/4 w-2 h-12 bg-gradient-to-b from-rfof-blue/70 to-rfof-blue/50 rounded"></div>
      </div>
    </div>
  );
}
