import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Package } from "lucide-react";
import { useAuth } from "@/contexts/auth";

export function HeroSection() {
  const { isAuthenticated } = useAuth();

  return (
    <section className="relative overflow-hidden py-24 md:py-32 bg-gradient-to-br from-white via-gray-50 to-rfof-lightGrey">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-24 -right-24 w-96 h-96 bg-rfof-green/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 -left-48 w-96 h-96 bg-rfof-blue/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-24 right-1/4 w-64 h-64 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container px-4 mx-auto relative z-10">
        <div className="flex flex-col md:flex-row items-center gap-12 md:gap-16">
          <div className="md:w-1/2 md:pr-6 mb-10 md:mb-0 animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-rfof-darkBlue">
              <span className="text-rfof-blue">رفوف</span> - كل مشروك{" "}
              <span className="text-rfof-green">مبروك</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              رفوف منصة رقمية تربط بين البائعين والمحلات التجارية، عبر تخصيص
              مساحات عرض داخل المحلات التجارية لبيع المنتجات دون تكاليف إيجار
              باهظة.{" "}
            </p>
            <div className="flex flex-wrap gap-5">
              {isAuthenticated ? (
                <Button
                  asChild
                  size="xl"
                  variant="rfof"
                  className="relative overflow-hidden group"
                >
                  <Link to="/dashboard" className="flex items-center gap-2">
                    <span className="relative z-10">
                      الذهاب إلى لوحة التحكم
                    </span>
                    <ArrowLeft className="relative z-10 h-5 w-5" />
                  </Link>
                </Button>
              ) : (
                <>
                  <Button
                    asChild
                    size="xl"
                    variant="rfof"
                    className="relative overflow-hidden group"
                  >
                    <Link to="/register" className="flex items-center gap-2">
                      <span className="relative z-10">إنشاء حساب</span>
                      <ArrowLeft className="relative z-10 h-5 w-5" />
                    </Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    size="xl"
                    className="hover:bg-gray-100 transition-colors border-2 shadow-md"
                  >
                    <Link to="/login">تسجيل الدخول</Link>
                  </Button>
                </>
              )}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-6 mt-12 text-center">
              <div className="bg-white p-4 rounded-xl shadow-md">
                <p className="text-2xl font-bold text-rfof-blue">+300</p>
                <p className="text-gray-600 text-sm">متجر إلكتروني</p>
              </div>
              <div className="bg-white p-4 rounded-xl shadow-md">
                <p className="text-2xl font-bold text-rfof-green">+100</p>
                <p className="text-gray-600 text-sm">محل تجاري</p>
              </div>
              <div className="bg-white p-4 rounded-xl shadow-md col-span-2 sm:col-span-1">
                <p className="text-2xl font-bold text-indigo-500">+1.2K</p>
                <p className="text-gray-600 text-sm">عملية تسليم شهرياً</p>
              </div>
            </div>
          </div>

          <div className="md:w-1/2 relative">
            {/* Background decorative elements */}
            <div className="absolute -top-10 -left-10 w-40 h-40 bg-rfof-green/10 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-rfof-blue/10 rounded-full blur-2xl"></div>

            {/* Main image with animation */}
            <div className="relative bg-white p-3 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
              <img
                src="/lovable-uploads/13977aac-280c-4799-82bc-88db32a8dcb5.png"
                alt="منصة رفوف"
                className="w-full h-auto rounded-xl"
              />

              {/* Floating elements */}
              <div className="absolute -top-6 -right-6 bg-white p-3 rounded-xl shadow-lg animate-bounce-slow">
                <Package className="h-10 w-10 text-rfof-green" />
              </div>
              <div className="absolute -bottom-6 -left-6 bg-white p-3 rounded-xl shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <p className="text-sm font-medium">باب رزق إضافي</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
