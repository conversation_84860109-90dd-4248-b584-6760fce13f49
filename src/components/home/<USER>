
import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card";
import { motion } from 'framer-motion';

interface StepCardProps {
  number: number;
  title: string;
  description: string;
}

export function StepCard({ number, title, description }: StepCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: number * 0.1 }}
      viewport={{ once: true }}
    >
      <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group h-full flex flex-col relative overflow-hidden bg-white rounded-xl transform hover:-translate-y-2">
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-rfof-green to-rfof-blue opacity-10 rounded-bl-3xl"></div>
        
        <div className="absolute top-4 right-4 w-10 h-10 bg-gradient-to-r from-rfof-green to-rfof-blue rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
          {number}
        </div>
        
        <CardHeader className="pt-16 pb-2">
          <CardTitle className="text-xl font-bold text-rfof-darkBlue">{title}</CardTitle>
        </CardHeader>
        
        <CardContent className="flex-grow">
          <p className="text-gray-600 leading-relaxed">{description}</p>
        </CardContent>
        
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-rfof-blue via-rfof-green to-rfof-blue transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
      </Card>
    </motion.div>
  );
}
