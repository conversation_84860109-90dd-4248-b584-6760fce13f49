
import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTit<PERSON>,
} from "@/components/ui/card";

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
}

export function FeatureCard({ title, description, icon }: FeatureCardProps) {
  return (
    <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden bg-white rounded-xl transform hover:-translate-y-2">
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-rfof-blue via-rfof-green to-indigo-500 transform origin-left transition-transform duration-300 scale-x-0 group-hover:scale-x-100"></div>
      <CardHeader className="pt-6 pb-2">
        <div className="mb-4 transform group-hover:scale-110 transition-transform duration-300 p-3 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 inline-block">{icon}</div>
        <CardTitle className="text-xl font-bold text-rfof-darkBlue">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 leading-relaxed">{description}</p>
      </CardContent>
    </Card>
  );
}
