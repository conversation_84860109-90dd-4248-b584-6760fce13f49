import React from "react";
import { motion } from "framer-motion";
import {
  ArrowRight,
  PackageOpen,
  Store,
  ShoppingBag,
  Handshake,
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

// New component for the improved workflow steps
const WorkflowStep = ({
  number,
  title,
  description,
  icon,
}: {
  number: number;
  title: string;
  description: string;
  icon: React.ReactNode;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: number * 0.1 }}
      viewport={{ once: true }}
      className="relative flex flex-col items-center"
    >
      {/* Step number with gradient background */}
      <div className="relative mb-6">
        <div className="absolute inset-0 bg-gradient-to-br from-rfof-blue via-rfof-green to-indigo-500 rounded-full opacity-20 blur-md transform scale-110"></div>
        <div className="relative z-10 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-rfof-blue to-rfof-green shadow-xl">
          <span className="text-2xl font-bold text-white">{number}</span>
        </div>
      </div>

      {/* Icon with animation */}
      <div className="mb-4 p-4 bg-white/90 rounded-2xl shadow-lg w-16 h-16 flex items-center justify-center transform hover:scale-110 transition-all duration-300">
        {icon}
      </div>

      {/* Title and description */}
      <h3 className="text-xl font-bold mb-2 mt-2 text-rfof-darkBlue">
        {title}
      </h3>
      <p className="text-gray-600 text-center max-w-xs">{description}</p>

      {/* Connecting line between steps (except for the last one) */}
      {number < 4 && (
        <div className="hidden md:block absolute right-0 top-16 -translate-y-1/2 transform translate-x-1/2">
          <ArrowRight className="w-8 h-8 text-rfof-green opacity-70" />
        </div>
      )}
    </motion.div>
  );
};

export function HowItWorksSection() {
  const isMobile = useIsMobile();

  const workflowSteps = [
    {
      number: 1,
      title: "التسجيل وإعداد الحساب",
      description: "يسجّل البائع أو المحل التجاري ويُدخل بياناته الأساسية.",
      icon: <PackageOpen className="w-8 h-8 text-rfof-blue" />,
    },
    {
      number: 2,
      title: "الربط بين البائع والمحل",
      description:
        "يرسل البائع طلب عرض منتجاته داخل المحل، ويقوم المحل بالموافقة أو الرفض.",
      icon: <Store className="w-8 h-8 text-rfof-green" />,
    },
    {
      number: 3,
      title: "توريد المنتجات والبيع",
      description:
        "يتم توريد المنتجات للمحل، وتُعرض للبيع مباشرة للعملاء داخل المحل.",
      icon: <Handshake className="w-8 h-8 text-indigo-500" />,
    },
    {
      number: 4,
      title: " المتابعة والمحاسبة",
      description: "متابعة مبيعات المتجر , وتحصيل الإيرادات",
      icon: <ShoppingBag className="w-8 h-8 text-amber-500" />,
    },
  ];

  return (
    <section id="how-it-works" className="py-24 relative overflow-hidden">
      {/* Gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-rfof-lightGrey to-gray-50 -z-10"></div>

      {/* Decorative elements */}
      <div className="absolute top-1/4 right-10 w-64 h-64 bg-rfof-green/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-10 w-64 h-64 bg-rfof-blue/10 rounded-full blur-3xl"></div>

      <div className="container px-4 mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          {/* Section title with animated badge */}
          <motion.span
            initial={{ scale: 0.9 }}
            whileInView={{ scale: 1 }}
            transition={{
              duration: 0.5,
              type: "spring",
              stiffness: 200,
            }}
            viewport={{ once: true }}
            className="inline-block py-1 px-5 rounded-full bg-gradient-to-r from-rfof-green/20 to-rfof-blue/20 text-rfof-darkBlue text-sm font-medium mb-4"
          >
            كيف تعمل المنصة
          </motion.span>

          <h2 className="text-3xl md:text-5xl font-bold mb-5 text-rfof-darkBlue bg-clip-text text-transparent bg-gradient-to-r from-rfof-darkBlue to-rfof-blue">
            خطوات العمل
          </h2>

          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            عملية بسيطة وفعالة تسمح للمتاجر الإلكترونية والمحلات التجارية
            بالتعاون بسهولة.
          </p>
        </motion.div>

        {/* Workflow visualization - Animated connection diagram */}
        <div className="mb-24 flex justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="relative max-w-5xl w-full overflow-hidden py-10"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-rfof-blue/5 via-rfof-green/5 to-indigo-500/5 rounded-3xl"></div>

            {/* Connection lines - Animated path for desktop */}
            <div className="hidden md:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3/4 h-1 bg-gradient-to-r from-rfof-blue via-rfof-green to-amber-500">
              <motion.div
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                transition={{ duration: 1.5 }}
                viewport={{ once: true }}
                className="h-full bg-white/50"
                style={{ transformOrigin: "left" }}
              ></motion.div>
            </div>

            {/* Grid layout for workflow steps */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-4 relative z-10 px-6">
              {workflowSteps.map((step) => (
                <WorkflowStep
                  key={step.number}
                  number={step.number}
                  title={step.title}
                  description={step.description}
                  icon={step.icon}
                />
              ))}
            </div>
          </motion.div>
        </div>

        {/* Interactive process cards - Mobile-friendly layout */}
        {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="overflow-hidden rounded-2xl bg-white shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="relative h-48 bg-gradient-to-br from-rfof-blue to-rfof-green/70 overflow-hidden">
              <div className="absolute inset-0 opacity-20 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.5),transparent)]"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <PackageOpen className="w-20 h-20 text-white" />
              </div>
            </div>
            <div className="p-6">
              <h3 className="text-xl font-bold mb-3 text-rfof-darkBlue">
                للمتاجر الإلكترونية
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-rfof-green/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-rfof-green" />
                  </div>
                  <span>إنشاء حساب وتعريف منتجاتك</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-rfof-green/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-rfof-green" />
                  </div>
                  <span>البحث عن محلات تجارية مناسبة</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-rfof-green/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-rfof-green" />
                  </div>
                  <span>إرسال طلبات الاستضافة وتوقيع العقود</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-rfof-green/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-rfof-green" />
                  </div>
                  <span>تتبع المبيعات وتوسيع شبكة التوزيع</span>
                </li>
              </ul>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="overflow-hidden rounded-2xl bg-white shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="relative h-48 bg-gradient-to-br from-indigo-500 to-purple-600 overflow-hidden">
              <div className="absolute inset-0 opacity-20 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.5),transparent)]"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Store className="w-20 h-20 text-white" />
              </div>
            </div>
            <div className="p-6">
              <h3 className="text-xl font-bold mb-3 text-rfof-darkBlue">
                للمحلات التجارية
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-indigo-500/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-indigo-500" />
                  </div>
                  <span>تسجيل متجرك والتعريف بموقعه وإمكانياته</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-indigo-500/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-indigo-500" />
                  </div>
                  <span>استعراض المنتجات المتاحة للاستضافة</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-indigo-500/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-indigo-500" />
                  </div>
                  <span>قبول الطلبات المناسبة وتوقيع العقود</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="rounded-full bg-indigo-500/20 p-1 mt-1">
                    <ArrowRight className="w-3 h-3 text-indigo-500" />
                  </div>
                  <span>تسليم المنتجات وتحصيل العمولات</span>
                </li>
              </ul>
            </div>
          </motion.div>
        </div> */}
      </div>
    </section>
  );
}
