import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, ShoppingBag, Store, PanelRight } from "lucide-react";
import { useAuth } from "@/contexts/auth";

export function CallToAction() {
  const { isAuthenticated } = useAuth();

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-rfof-darkBlue via-rfof-blue to-rfof-darkBlue"></div>

      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="absolute -top-20 -left-20 w-96 h-96 rounded-full bg-white/30 blur-3xl"></div>
          <div className="absolute top-1/3 right-0 w-80 h-80 rounded-full bg-rfof-green/40 blur-3xl"></div>
          <div className="absolute -bottom-20 left-1/3 w-64 h-64 rounded-full bg-white/20 blur-3xl"></div>
        </div>
      </div>

      <div className="container px-4 mx-auto relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-white leading-tight">
              ابدأ الآن مع منصة <span className="text-rfof-green">رفوف</span>
            </h2>
            <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
              انضم إلى شبكتنا المتنامية من المتاجر الإلكترونية والمحلات التجارية
              وكن جزءاً من ثورة التجارة الإلكترونية في المملكة.
            </p>

            {isAuthenticated ? (
              <Button
                asChild
                size="xl"
                variant="rfof-white"
                className="px-10 py-4 text-lg shadow-xl hover:shadow-2xl transition-all"
              >
                <Link to="/dashboard" className="flex items-center gap-2">
                  الذهاب إلى لوحة التحكم
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
            ) : (
              <div className="flex flex-wrap justify-center gap-5">
                <Button
                  asChild
                  size="xl"
                  variant="rfof-white"
                  className="shadow-xl hover:shadow-2xl transition-all px-10 py-4 text-lg"
                >
                  <Link to="/register">إنشاء حساب</Link>
                </Button>
                <Button
                  asChild
                  variant="rfof-outline"
                  size="xl"
                  className="shadow-xl hover:shadow-2xl transition-all px-10 py-4 text-lg"
                >
                  <Link to="/login">تسجيل الدخول</Link>
                </Button>
              </div>
            )}
          </div>

          {/* User types cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-16">
            <div className="bg-white/10 backdrop-filter backdrop-blur-lg p-6 rounded-2xl hover:bg-white/15 transition-all duration-300 text-white text-center">
              <div className="bg-white/10 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4">
                <ShoppingBag className="h-8 w-8 text-rfof-green" />
              </div>
              <h3 className="text-xl font-bold mb-3">للمتاجر الإلكترونية</h3>
              <p className="text-white/80">
                انضم كمتجر إلكتروني واستفد من شبكة واسعة من نقاط الاستلام
              </p>
            </div>

            <div className="bg-white/10 backdrop-filter backdrop-blur-lg p-6 rounded-2xl hover:bg-white/15 transition-all duration-300 text-white text-center">
              <div className="bg-white/10 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Store className="h-8 w-8 text-rfof-green" />
              </div>
              <h3 className="text-xl font-bold mb-3">للمحلات التجارية</h3>
              <p className="text-white/80">
                انضم كمحل تجاري وحول مساحتك الفائضة إلى مصدر دخل إضافي
              </p>
            </div>

            {/*   <div className="bg-white/10 backdrop-filter backdrop-blur-lg p-6 rounded-2xl hover:bg-white/15 transition-all duration-300 text-white text-center">
              <div className="bg-white/10 w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4">
                <PanelRight className="h-8 w-8 text-rfof-green" />
              </div>
              <h3 className="text-xl font-bold mb-3">للإدارة</h3>
              <p className="text-white/80">إدارة متكاملة لجميع عمليات التسليم والمدفوعات بين المتاجر والمحلات</p>
            </div> */}
          </div>
        </div>
      </div>
    </section>
  );
}
