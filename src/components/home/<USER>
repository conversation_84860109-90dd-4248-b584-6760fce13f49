import React from "react";
import { <PERSON> } from "react-router-dom";
import RfofLogo from "@/components/RfofLogo";
import {
  Instagram,
  Twitter,
  Linkedin,
  Facebook,
  MapPin,
  Phone,
  Mail,
  Factory,
  MessageCircleCode,
} from "lucide-react";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Top wave decoration */}
      <div className="absolute top-0 left-0 w-full">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
          className="w-full h-auto fill-white"
        >
          <path
            fillOpacity="0.05"
            d="M0,224L60,229.3C120,235,240,245,360,229.3C480,213,600,171,720,165.3C840,160,960,192,1080,181.3C1200,171,1320,117,1380,90.7L1440,64L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z"
          ></path>
        </svg>
      </div>

      <div className="container px-4 mx-auto py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo and description */}
          <div className="md:col-span-1">
            <div className="flex flex-col items-start">
              <RfofLogo className="mb-4" size="md" />
              <p className="text-gray-400 text-sm mb-6 leading-relaxed">
                منصة رفوف تربط المتاجر الإلكترونية بالمحلات التجارية لتوفير
                تجربة استلام أفضل للعملاء وتنويع مصادر الدخل للمحلات.
              </p>
              <div className="flex space-x-4 rtl:space-x-reverse">
                <a
                  href="https://www.instagram.com/rafof_sa/"
                  target="_blank"
                  className="text-gray-400 hover:text-rfof-green transition-colors"
                  aria-label="Instagram"
                >
                  <Instagram size={20} />
                </a>
                <a
                  href="https://x.com/rofof_app"
                  target="_blank"
                  className="text-gray-400 hover:text-rfof-green transition-colors"
                  aria-label="Twitter"
                >
                  <Twitter size={20} />
                </a>
                <a
                  href="https://www.linkedin.com/company/rafof/?originalSubdomain=sa"
                  target="_blank"
                  className="text-gray-400 hover:text-rfof-green transition-colors"
                  aria-label="LinkedIn"
                >
                  <Linkedin size={20} />
                </a>
                {/* <a
                  href="#"
                  className="text-gray-400 hover:text-rfof-green transition-colors"
                  aria-label="Facebook"
                >
                  <Facebook size={20} />
                </a> */}
                <a
                  href="https://wa.me/966536232515"
                  target="_blank"
                  aria-label="WhatsApp"
                  className="text-gray-400 hover:text-rfof-green transition-colors"
                >
                  <MessageCircleCode />
                </a>
              </div>
            </div>
          </div>

          {/* Quick links */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-6 text-white relative inline-block">
              روابط سريعة
              <span className="absolute bottom-0 left-0 w-1/3 h-0.5 bg-rfof-green"></span>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/"
                  className="text-gray-400 hover:text-white transition-colors inline-block"
                >
                  الرئيسية
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="text-gray-400 hover:text-white transition-colors inline-block"
                >
                  من نحن
                </Link>
              </li>
              {/* <li>
                <Link
                  to="/contact"
                  className="text-gray-400 hover:text-white transition-colors inline-block"
                >
                  اتصل بنا
                </Link>
              </li> */}
            </ul>
          </div>

          {/* Account */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-6 text-white relative inline-block">
              الحساب
              <span className="absolute bottom-0 left-0 w-1/3 h-0.5 bg-rfof-green"></span>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/login"
                  className="text-gray-400 hover:text-white transition-colors inline-block"
                >
                  تسجيل الدخول
                </Link>
              </li>
              <li>
                <Link
                  to="/register"
                  className="text-gray-400 hover:text-white transition-colors inline-block"
                >
                  إنشاء حساب
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal and contact */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-6 text-white relative inline-block">
              تواصل معنا
              <span className="absolute bottom-0 left-0 w-1/3 h-0.5 bg-rfof-green"></span>
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <MapPin
                  size={20}
                  className="text-rfof-green flex-shrink-0 mt-1"
                />
                <span className="text-gray-400">
                  الرياض، المملكة العربية السعودية
                </span>
              </li>
              <li className="flex items-center gap-3">
                <Phone size={20} className="text-rfof-green flex-shrink-0" />
                <span className="text-gray-400" dir="ltr">
                  +966 536232515
                </span>
              </li>
              <li className="flex items-center gap-3">
                <Mail size={20} className="text-rfof-green flex-shrink-0" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © {currentYear} رفوف. جميع الحقوق محفوظة. منصة رفوف علامة تجارية
            مسجلة , س.ت 1009141858
          </p>
          <div className="flex flex-wrap gap-5 mt-4 md:mt-0">
            <Link
              to="/terms"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              شروط الاستخدام
            </Link>
            <Link
              to="/privacy-policy"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              سياسة الخصوصية
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
