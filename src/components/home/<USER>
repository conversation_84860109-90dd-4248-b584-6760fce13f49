import React from "react";
import {
  ShoppingCart,
  Store,
  Package,
  TrendingUp,
  Map,
  Award,
} from "lucide-react";
import { FeatureCard } from "./FeatureCard";

export function FeaturesSection() {
  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* Decorative shape */}
      <div className="absolute left-0 top-0 w-full h-24 bg-gradient-to-b from-gray-50 to-white"></div>

      <div className="container px-4 mx-auto">
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <span className="inline-block py-1 px-3 rounded-full bg-rfof-blue/10 text-rfof-blue text-sm font-medium mb-4">
            منصة رفوف
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-5 text-rfof-darkBlue">
            مميزات المنصة
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            رفوف توفر حلاً مبتكراً للربط بين المتاجر الإلكترونية والمحلات
            التجارية، مما يساهم في تطوير تجربة التسوق للمستهلكين وزيادة
            الإيرادات للأطراف المشاركة.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <FeatureCard
            title="للمتاجر الإلكترونية"
            description="وسّع انتشارك وقدم خدمة استلام سريعة للعملاء من خلال شبكة من المحلات التجارية المنتشرة في مختلف المناطق."
            icon={<ShoppingCart className="h-12 w-12 text-rfof-green" />}
          />
          {/* 
          <FeatureCard
            title="للمحلات التجارية"
            description="استفد من المساحات غير المستخدمة في متجرك وحولها إلى مصدر دخل إضافي من خلال استضافة منتجات المتاجر الإلكترونية."
            icon={<Store className="h-12 w-12 text-rfof-blue" />}
          />

          <FeatureCard
            title="للمستهلكين"
            description="احصل على منتجاتك بشكل أسرع وأسهل من خلال نقاط استلام قريبة من موقعك دون الحاجة للانتظار طويلاً."
            icon={<Package className="h-12 w-12 text-indigo-500" />}
          />

          <FeatureCard
            title="زيادة الإيرادات"
            description="فرص دخل جديدة للمحلات والموردين من خلال نموذج البيع بالتصريف."
            icon={<TrendingUp className="h-12 w-12 text-emerald-500" />}
          /> */}

          <FeatureCard
            title="تغطية واسعة"
            description="منصة آمنة وموثوقة تضمن انسيابية تجربة البيع والشراء لجميع الأطراف."
            icon={<Map className="h-12 w-12 text-amber-500" />}
          />

          <FeatureCard
            title="تجربة موثوقة"
            description="منصة آمنة وموثوقة تضمن انسيابية تجربة البيع والشراء لجميع الأطراف."
            icon={<Award className="h-12 w-12 text-rose-500" />}
          />
        </div>

        {/* Added CTA button */}
        <div className="text-center mt-16">
          <Button
            asChild
            size="lg"
            className="px-8 py-6 text-lg bg-rfof-blue hover:bg-rfof-darkBlue"
          >
            <Link to="/register">انضم إلى منصة رفوف الآن</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

// Import Button and Link at the top
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
