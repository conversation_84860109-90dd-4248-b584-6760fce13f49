import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Transaction } from "@/types";
import { formatCurrency, formatDate } from "@/lib/transactionUtils";
import { Loading } from "@/components/ui/loading";
import { useUserName } from "@/hooks/useUserName";
import { TransactionTypeDisplay } from "@/components/transactions/TransactionTypeDisplay";
import { TransactionStatusBadge } from "@/components/transactions/TransactionStatusBadge";
import {
  TransactionReceiptPreview,
  TransactionReceiptButton,
} from "@/components/transactions/TransactionReceiptPreview";
import { TransactionActionButtons } from "@/components/transactions/TransactionActionButtons";

// Helper component to display username for IDs
function RenderUserName({ userId }: { userId?: string }) {
  const { data: userName, isLoading } = useUserName(userId);
  if (!userId) return <span>-</span>;
  if (isLoading) return <span className="text-gray-400">•••</span>;
  return <span>{userName || "-"}</span>;
}

interface TransactionsListProps {
  transactions: Transaction[];
  showReceipt?: boolean;
  isLoadingAction?: boolean;
  isLoading?: boolean;
  onApprove?: (id: string) => void;
  onReject?: (id: string, reason?: string) => void;
  emptyStateMessage: string;
}

export function TransactionsList({
  transactions,
  showReceipt = false,
  onApprove,
  onReject,
  isLoadingAction = false,
  isLoading = false,
  emptyStateMessage = "لا توجد معاملات متاحة حالياً.",
}: TransactionsListProps) {
  const [imagePreviewOpen, setImagePreviewOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);

  const handleViewReceipt = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setImagePreviewOpen(true);
  };

  if (isLoading) {
    return (
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>السبب</TableHead>
              <TableHead>نوع</TableHead>
              <TableHead>من</TableHead>
              <TableHead>إلى</TableHead>
              <TableHead>المالك</TableHead>
              <TableHead>المبلغ</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>التاريخ</TableHead>
              {showReceipt && <TableHead>إيصال</TableHead>}
              {(onApprove || onReject) && <TableHead>إجراءات</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell
                colSpan={showReceipt || onApprove || onReject ? 10 : 8}
                className="h-24 text-center"
              >
                <div className="flex items-center justify-center">
                  <Loading />
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    );
  }

  if (!transactions || transactions.length === 0) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">{emptyStateMessage}</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>السبب</TableHead>
            <TableHead>نوع</TableHead>
            <TableHead>من</TableHead>
            <TableHead>إلى</TableHead>
            <TableHead>المالك</TableHead>
            <TableHead>المبلغ</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>التاريخ</TableHead>
            {showReceipt && <TableHead>إيصال</TableHead>}
            {(onApprove || onReject) && <TableHead>إجراءات</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell>{transaction.description || "-"}</TableCell>
              <TableCell>
                <TransactionTypeDisplay type={transaction.type} />
              </TableCell>
              <TableCell>
                <RenderUserName userId={transaction.from_user_id} />
              </TableCell>
              <TableCell>
                <RenderUserName userId={transaction.to_user_id} />
              </TableCell>
              <TableCell>{transaction.user_name || "-"}</TableCell>
              <TableCell>{formatCurrency(transaction.amount)}</TableCell>
              <TableCell>
                <TransactionStatusBadge status={transaction.status} />
              </TableCell>
              <TableCell>
                <span className="whitespace-nowrap">
                  {formatDate(transaction.created_at)}
                </span>
              </TableCell>
              {showReceipt && (
                <TableCell>
                  <TransactionReceiptButton
                    transaction={transaction}
                    onViewReceipt={handleViewReceipt}
                  />
                </TableCell>
              )}
              {(onApprove || onReject) && (
                <TableCell>
                  <TransactionActionButtons
                    status={transaction.status}
                    isLoading={isLoadingAction}
                    onApprove={
                      onApprove ? () => onApprove(transaction.id) : undefined
                    }
                    onReject={
                      onReject ? () => onReject(transaction.id) : undefined
                    }
                  />
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <TransactionReceiptPreview
        transaction={selectedTransaction}
        open={imagePreviewOpen}
        onOpenChange={setImagePreviewOpen}
      />
    </div>
  );
}
