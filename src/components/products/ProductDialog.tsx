import React from "react";
import { Product } from "@/types";
import { ProductForm, ProductFormValues } from "./ProductForm";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ProductFormValues, imageFiles: File[]) => void;
  editingProduct: Product | null;
}

export const ProductDialog = ({
  open,
  onOpenChange,
  onSubmit,
  editingProduct,
}: ProductDialogProps) => {
  const isEditing = !!editingProduct;

  const initialValues = editingProduct
    ? {
        name: editingProduct.name,
        description: editingProduct.description,
        price: editingProduct.price,
        quantity: editingProduct.quantity,
        category: editingProduct.category || "",
        images: editingProduct.images || [],
      }
    : undefined;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "تعديل منتج" : "إضافة منتج جديد"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "قم بتعديل معلومات المنتج"
              : "أدخل تفاصيل المنتج الجديد"}
          </DialogDescription>
        </DialogHeader>
        <ProductForm
          initialValues={initialValues}
          onSubmit={onSubmit}
          isEditing={isEditing}
        />
      </DialogContent>
    </Dialog>
  );
};
