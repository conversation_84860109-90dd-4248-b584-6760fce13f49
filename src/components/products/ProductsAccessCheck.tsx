
import React from 'react';
import { Package } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface ProductsAccessCheckProps {
  hasAccess: boolean;
}

export const ProductsAccessCheck = ({ hasAccess }: ProductsAccessCheckProps) => {
  const isMobile = useIsMobile();
  
  if (hasAccess) {
    return null;
  }

  return (
    <div className="h-[60vh] flex flex-col items-center justify-center px-4 text-center">
      <Package className={`${isMobile ? 'h-12 w-12' : 'h-16 w-16'} text-muted-foreground mb-4`} />
      <h3 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold`}>ليس لديك صلاحية الوصول</h3>
      <p className="text-muted-foreground mt-2 text-sm sm:text-base">
        فقط المتاجر الإلكترونية يمكنها إدارة المنتجات
      </p>
    </div>
  );
};
