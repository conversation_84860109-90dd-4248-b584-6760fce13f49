
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Plus } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ProductsErrorDisplayProps {
  error: any;
  isRetrying: boolean;
  onRetry: () => void;
  onAddProduct: () => void;
}

export const ProductsErrorDisplay = ({
  error,
  isRetrying,
  onRetry,
  onAddProduct,
}: ProductsErrorDisplayProps) => {
  return (
    <Alert variant="destructive" className="mb-6">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>حدث خطأ أثناء تحميل المنتجات</AlertTitle>
      <AlertDescription className="mt-2">
        {typeof error === 'string' ? error : error?.message || 'تعذر الاتصال بالخادم. الرجاء المحاولة مرة أخرى.'}
      </AlertDescription>
      <div className="mt-4 flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          disabled={isRetrying}
          className="bg-white"
        >
          <RefreshCw className={`h-4 w-4 ml-2 ${isRetrying ? 'animate-spin' : ''}`} />
          إعادة المحاولة
        </Button>
        <Button size="sm" asChild>
          <Link to="/dashboard/products/add">
            <Plus className="h-4 w-4 ml-2" />
            إضافة منتج جديد
          </Link>
        </Button>
      </div>
    </Alert>
  );
};
