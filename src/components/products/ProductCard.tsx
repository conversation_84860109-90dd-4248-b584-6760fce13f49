import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Product } from "@/types";
import { formatPrice } from "@/utils/formatters";
import { Link } from "react-router-dom";
import { Edit, Eye, Trash2 } from "lucide-react";

interface ProductCardProps {
  product: Product;
  onEdit?: (product: Product) => void;
  onDelete?: (product: Product) => void;
  onSelect?: (product: Product) => void;
  selectable?: boolean;
  selected?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  onSelect,
  selectable = false,
  selected = false,
}) => {
  const handleEdit = () => {
    if (onEdit) onEdit(product);
  };

  const handleDelete = () => {
    if (onDelete) onDelete(product);
  };

  const handleSelect = () => {
    if (onSelect) onSelect(product);
  };

  // Use either quantity or stock property based on what's available
  const stockValue = product.quantity ?? product.stock ?? 0;

  console.log(product);

  const STATUSES = ["active", "inactive", "deleted"] as const;
  type Status = (typeof STATUSES)[number];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 hover:bg-green-600";
      case "inactive":
        return "bg-gray-400 hover:bg-gray-500";
      case "deleted":
        return "bg-black hover:bg-gray-800";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  return (
    <Card
      className={`overflow-hidden ${selected ? "ring-2 ring-primary" : ""}`}
    >
      <div className="relative h-48 overflow-hidden">
        <img
          src={product.images?.[0] || "/placeholder.svg"}
          alt={product.name}
          className="w-full h-full object-cover"
        />
        {product.status && (
          <Badge
            className={`absolute top-2 left-2 ${getStatusColor(
              product.status
            )}`}
          >
            {product.status}
          </Badge>
        )}
      </div>

      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <h3 className="font-medium text-lg truncate">{product.name}</h3>
          <span className="font-bold text-primary">
            {formatPrice(product.price)}
          </span>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {product.description || "لا يوجد وصف للمنتج"}
        </p>
        <div className="mt-2 flex justify-between text-sm">
          <span>المخزون: {stockValue}</span>
          {product.store_name && <span>المتجر: {product.store_name}</span>}
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex gap-2">
        {selectable ? (
          <Button
            variant={selected ? "default" : "outline"}
            onClick={handleSelect}
            className="w-full"
          >
            {selected ? "تم الاختيار" : "اختيار"}
          </Button>
        ) : (
          <>
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link to={`/dashboard/products/${product.id}`}>
                <Eye className="h-4 w-4 mr-2" />
              </Link>
            </Button>
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="flex-1"
              >
                <Edit className="h-4 w-4 mr-2" />
                تعديل
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                className="flex-1"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                حذف
              </Button>
            )}
          </>
        )}
      </CardFooter>
    </Card>
  );
};

export default ProductCard;
