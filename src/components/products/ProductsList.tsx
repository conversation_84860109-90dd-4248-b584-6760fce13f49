import React from "react";
import { Product } from "@/types";
import ProductCard from "./ProductCard";
import EmptyState from "@/components/EmptyState";
import { ShoppingCart, Plus } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";

interface ProductsListProps {
  products: Product[];
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
  onAddNew: () => void;
  isFiltered: boolean;
}

export const ProductsList = ({
  products,
  onEdit,
  onDelete,
  onAddNew,
  isFiltered,
}: ProductsListProps) => {
  const isMobile = useIsMobile();

  if (products.length === 0) {
    return (
      <EmptyState
        title={isFiltered ? "لا توجد نتائج" : "لا توجد منتجات"}
        description={
          isFiltered
            ? "لم يتم العثور على منتجات تطابق معايير البحث."
            : "لم تقم بإضافة أي منتجات بعد. أضف منتجاً جديداً للبدء."
        }
        icon={ShoppingCart}
        action={{
          label: "إضافة منتج",
          onClick: onAddNew,
        }}
      />
    );
  }

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-lg font-medium">
          {isFiltered
            ? `نتائج البحث (${products.length})`
            : `المنتجات (${products.length})`}
        </h2>
        {/*        <Button 
          onClick={onAddNew} 
          size={isMobile ? "sm" : "default"}
          className="bg-primary hover:bg-primary/90"
        >
          <Plus className={`h-4 w-4 ${isMobile ? 'mr-1' : 'mr-2'}`} />
          {isMobile ? "إضافة" : "إضافة منتج"}
        </Button> */}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
      </div>
    </>
  );
};
