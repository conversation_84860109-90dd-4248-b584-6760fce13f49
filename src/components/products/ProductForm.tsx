import React, { useState } from "react";
import { Product } from "@/types";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { X, ImagePlus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loading } from "@/components/ui/loading";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const productFormSchema = z.object({
  name: z.string().min(3, "يجب أن يتكون اسم المنتج من 3 أحرف على الأقل"),
  description: z
    .string()
    .min(10, "يجب أن يتكون وصف المنتج من 10 أحرف على الأقل"),
  price: z.coerce.number().positive("يجب أن يكون السعر أكبر من صفر"),
  quantity: z.coerce
    .number()
    .int()
    .positive("يجب أن تكون الكمية عدداً صحيحاً موجباً"),
  category: z.string().optional(),
  images: z.array(z.string()).optional(),
});

export type ProductFormValues = z.infer<typeof productFormSchema>;

interface ProductFormProps {
  initialValues?: ProductFormValues;
  onSubmit: (data: ProductFormValues, imageFiles: File[]) => void;
  isEditing?: boolean;
}

export const ProductForm = ({
  initialValues,
  onSubmit,
  isEditing = false,
}: ProductFormProps) => {
  // Store image previews (for UI display)
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>(
    initialValues?.images || []
  );
  // Store actual image files to be uploaded
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imageUploadLoading, setImageUploadLoading] = useState(false);

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: initialValues || {
      name: "",
      description: "",
      price: 0,
      quantity: 1,
      category: "",
      images: [],
    },
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setImageUploadLoading(true);

    const newPreviewUrls: string[] = [];
    const newFiles: File[] = [];
    let loadedCount = 0;

    Array.from(files).forEach((file) => {
      newFiles.push(file);
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          newPreviewUrls.push(e.target.result as string);
        }
        loadedCount++;
        if (loadedCount === files.length) {
          setImagePreviewUrls((prev) => [...prev, ...newPreviewUrls]);
          setImageFiles((prev) => [...prev, ...newFiles]);
          form.setValue("images", [...imagePreviewUrls, ...newPreviewUrls]);
          setImageUploadLoading(false);
        }
      };

      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImagePreviewUrls((prev) => prev.filter((_, i) => i !== index));
    setImageFiles((prev) => prev.filter((_, i) => i !== index));
    form.setValue(
      "images",
      imagePreviewUrls.filter((_, i) => i !== index)
    );
  };

  const handleFormSubmit = (data: ProductFormValues) => {
    onSubmit(data, imageFiles);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم المنتج</FormLabel>
              <FormControl>
                <Input placeholder="أدخل اسم المنتج" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>وصف المنتج</FormLabel>
              <FormControl>
                <Textarea placeholder="أدخل وصفاً تفصيلياً للمنتج" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>السعر (ريال)</FormLabel>
                <FormControl>
                  <Input type="number" min="0" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الكمية</FormLabel>
                <FormControl>
                  <Input type="number" min="1" step="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>التصنيف</FormLabel>
              <FormControl>
                <Input placeholder="أدخل تصنيف المنتج" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>صور المنتج</FormLabel>
          <div className="mt-2 grid grid-cols-3 gap-2">
            {imagePreviewUrls.map((image, index) => (
              <div
                key={index}
                className="relative h-24 rounded-md overflow-hidden border"
              >
                <img
                  src={image}
                  alt={`صورة المنتج ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-1 right-1 h-5 w-5 rounded-full"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
            <div className="h-24 flex items-center justify-center rounded-md border border-dashed">
              <label className="cursor-pointer flex flex-col items-center justify-center w-full h-full">
                <ImagePlus className="h-8 w-8 mb-1 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  إضافة صورة
                </span>
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleImageUpload}
                  disabled={imageUploadLoading}
                  multiple
                />
              </label>
            </div>
          </div>
          {imageUploadLoading && (
            <div className="mt-2 text-center">
              <Loading size="sm" text="جاري تحميل الصور..." />
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button type="submit">
            {isEditing ? "تحديث المنتج" : "إضافة المنتج"}
          </Button>
        </div>
      </form>
    </Form>
  );
};
