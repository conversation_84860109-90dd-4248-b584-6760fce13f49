
import React from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface StoreType {
  id: string;
  name: string;
}

interface ProductFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onSearchSubmit: () => void;
  storeFilter: string | 'all';
  onStoreFilterChange: (value: string | 'all') => void;
  stores: StoreType[];
}

export const ProductFilter: React.FC<ProductFilterProps> = ({
  searchTerm,
  onSearchChange,
  onSearchSubmit,
  storeFilter,
  onStoreFilterChange,
  stores = []
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearchSubmit();
    }
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 mb-6">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="ابحث عن اسم المنتج..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-10"
        />
      </div>
      
      {stores.length > 0 && (
        <div className="w-full sm:w-auto">
          <Select value={storeFilter} onValueChange={onStoreFilterChange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="تصفية حسب المتجر" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع المتاجر</SelectItem>
              {stores.map((store) => (
                <SelectItem key={store.id} value={store.id}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      
      <Button onClick={onSearchSubmit} className="w-full sm:w-auto">
        بحث
      </Button>
    </div>
  );
};
