import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, Eye } from "lucide-react";

export interface ProductData {
  id: string;
  name: string;
  price: number;
  quantity: number;
  product_name?: string;
  image?: string;
  description?: string;
  category?: string;
  status?: string;
}

interface ProductTableProps {
  products: ProductData[];
  isLoading?: boolean;
  onEdit?: (product: ProductData) => void;
  onDelete?: (productId: string) => void;
  onView?: (product: ProductData) => void;
  showActions?: boolean;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  isLoading = false,
  onEdit,
  onDelete,
  onView,
  showActions = true,
}) => {
  if (isLoading) {
    return <div className="text-center py-8">جاري التحميل...</div>;
  }

  if (!products || products.length === 0) {
    return <div className="text-center py-8">لا توجد منتجات متاحة</div>;
  }

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} ريال`;
  };

  console.log(products);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>-</TableHead>
          <TableHead>الإسم</TableHead>
          <TableHead>السعر</TableHead>
          <TableHead>الكمية</TableHead>
          <TableHead>الحالة</TableHead>
          {showActions && (
            <TableHead className="text-right">الإجراءات</TableHead>
          )}
        </TableRow>
      </TableHeader>
      <TableBody>
        {products.map((product) => (
          <TableRow key={product.id}>
            <TableCell className="w-[100px]">
              {product.image ? (
                <a
                  href={product.image}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src={product.image}
                    alt={product.name || product.product_name}
                    className="min-w-16 min-h-16 object-cover rounded"
                  />
                </a>
              ) : (
                <div className="w-16 h-16 bg-gray-200 rounded"></div>
              )}
            </TableCell>
            <TableCell className="font-medium">
              {product.name || product.product_name}
            </TableCell>
            <TableCell>{formatPrice(product.price)}</TableCell>
            <TableCell>{product.quantity}</TableCell>
            <TableCell>
              {product.status ? (
                <Badge
                  variant={
                    product.status === "active" ? "default" : "secondary"
                  }
                >
                  {product.status === "active" ? "متاح" : "غير متاح"}
                </Badge>
              ) : (
                <Badge variant="default">متاح</Badge>
              )}
            </TableCell>
            {showActions && (
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  {onView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(product)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(product)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700"
                      onClick={() => onDelete(product.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default ProductTable;
