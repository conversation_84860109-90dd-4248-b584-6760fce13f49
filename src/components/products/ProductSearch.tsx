
import React from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface ProductSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

export const ProductSearch = ({ searchQuery, setSearchQuery }: ProductSearchProps) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="mb-4 sm:mb-6 w-full">
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="البحث عن منتج..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={`pr-10 border-gray-300 focus:border-primary focus:ring focus:ring-primary/20 transition-all duration-200 ${isMobile ? 'text-sm py-2' : ''}`}
        />
      </div>
    </div>
  );
};
