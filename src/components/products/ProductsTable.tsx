import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, Eye } from "lucide-react";
import { useProductOperations } from "@/hooks/useProductOperations";
import { Product } from "@/types";
import { formatCurrency } from "@/lib/utils";
import { Link } from "react-router-dom";
import { Loading } from "@/components/ui/loading";
import { ProductsErrorDisplay } from "@/components/products/ProductsErrorDisplay";

export const ProductsTable: React.FC = () => {
  const { products, loadProducts, isLoading, error } = useProductOperations();
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  const handleRetry = async () => {
    setIsRetrying(true);
    await loadProducts();
    setIsRetrying(false);
  };

  if (isLoading && !isRetrying) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <ProductsErrorDisplay
        error={error}
        isRetrying={isRetrying}
        onRetry={handleRetry}
        onAddProduct={() => {}}
      />
    );
  }

  if (!products?.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">لا توجد منتجات لعرضها</p>
        <Button asChild>
          <Link to="/dashboard/products/add">إضافة منتج جديد</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="rounded-md border overflow-hidden">
      <Table className="text-center">
        <TableHeader>
          <TableRow>
            <TableHead>المنتج</TableHead>
            <TableHead>السعر</TableHead>
            <TableHead>الكمية</TableHead>
            <TableHead>الحالة</TableHead>
            {/* <TableHead className="text-left">الإجراءات</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  {product.images && product.images[0] ? (
                    <div className="h-12 w-12 rounded overflow-hidden bg-gray-100">
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="h-12 w-12 rounded bg-gray-100 flex items-center justify-center text-gray-400">
                      صورة
                    </div>
                  )}
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                      {product.description || "لا يوجد وصف"}
                    </p>
                  </div>
                </div>
              </TableCell>
              <TableCell>{formatCurrency(product.price)}</TableCell>
              <TableCell>{product.quantity}</TableCell>
              <TableCell>
                <Badge
                  className={
                    product.status === "active"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }
                >
                  {product.status === "active" ? "نشط" : "غير نشط"}
                </Badge>
              </TableCell>
              {/* <TableCell>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="icon" asChild>
                    <Link to={`/dashboard/products/${product.id}`}>
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">عرض</span>
                    </Link>
                  </Button>
                  <Button variant="ghost" size="icon" asChild>
                    <Link to={`/dashboard/products/edit/${product.id}`}>
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">تعديل</span>
                    </Link>
                  </Button>
                  <Button variant="ghost" size="icon" className="text-destructive">
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">حذف</span>
                  </Button>
                </div>
              </TableCell> */}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
