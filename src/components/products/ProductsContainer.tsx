import React, { useState } from "react";
import { DashboardLayout } from "@/layouts/DashboardLayout";
import { useProducts } from "@/contexts/ProductContext";
import { useAuth } from "@/contexts/auth";
import { PageHeader } from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Loading } from "@/components/ui/loading";
import { Plus, ShoppingCart } from "lucide-react";
import { Product, ProductStatus } from "@/types";
import { ProductDialog } from "@/components/products/ProductDialog";
import { DeleteProductDialog } from "@/components/products/DeleteProductDialog";
import { ProductsAccessCheck } from "@/components/products/ProductsAccessCheck";
import { ProductSearch } from "@/components/products/ProductSearch";
import { ProductsList } from "@/components/products/ProductsList";
import { ProductsErrorDisplay } from "@/components/products/ProductsErrorDisplay";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";

export const ProductsContainer = () => {
  const {
    userProducts,
    addProduct,
    updateProduct,
    deleteProduct,
    isLoading,
    error,
    loadProducts,
  } = useProducts();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const isMobile = useIsMobile();

  const filteredProducts = userProducts.filter(
    (product) =>
      product.status !== "deleted" &&
      (product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        product.category?.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleAddDialogOpen = (open: boolean) => {
    if (!open) {
      setEditingProduct(null);
    }
    setShowAddDialog(open);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowAddDialog(true);
  };

  const confirmDelete = (product: Product) => {
    setSelectedProduct(product);
    setIsDeleting(true);
  };

  const handleDeleteProduct = async () => {
    if (!selectedProduct) return;

    try {
      await deleteProduct(selectedProduct.id);
      toast.success("تم حذف المنتج بنجاح");
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("حدث خطأ أثناء حذف المنتج");
    } finally {
      setIsDeleting(false);
      setSelectedProduct(null);
    }
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await loadProducts();
    } finally {
      setIsRetrying(false);
    }
  };

  const handleProductSubmit = async (data: any, imageFiles: File[]) => {
    const toastId = toast.loading("جاري حفظ بيانات المنتج...");

    if (!user) {
      toast.error("يجب تسجيل الدخول لإضافة منتج", {
        id: toastId,
      });
      return;
    }

    try {
      if (editingProduct) {
        await updateProduct(
          editingProduct.id,
          {
            ...data,
            quantity: data.quantity || 0,
          },
          imageFiles
        );
        toast.success("تم تحديث المنتج بنجاح", {
          id: toastId,
        });
      } else {
        await addProduct({
          productData: {
            name: data.name,
            description: data.description,
            price: data.price,
            quantity: data.quantity || 0,
            category: data.category,
            user_id: user?.id || "",
            seller_id: user?.id || "",
            seller_name: user?.name || "",
            status: "active" as ProductStatus,
          },
          imageFiles,
        });
        toast.success("تمت إضافة المنتج بنجاح", {
          id: toastId,
        });
      }

      setShowAddDialog(false);
    } catch (error) {
      console.error("Error submitting product:", error);
      toast.error("حدث خطأ أثناء حفظ بيانات المنتج", {
        id: toastId,
      });
    }
  };

  const hasAccess = user?.role === "ecommerce" || user?.role === "admin";

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <ProductsAccessCheck hasAccess={false} />
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto">
          <PageHeader
            title="المنتجات"
            description="إدارة منتجاتك وإضافة منتجات جديدة"
            icon={ShoppingCart}
          />

          <ProductsErrorDisplay
            error={error}
            isRetrying={isRetrying}
            onRetry={handleRetry}
            onAddProduct={() => handleAddDialogOpen(true)}
          />

          {filteredProducts.length > 0 && (
            <>
              <ProductSearch
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
              />

              <ProductsList
                products={filteredProducts}
                onEdit={handleEditProduct}
                onDelete={confirmDelete}
                onAddNew={() => handleAddDialogOpen(true)}
                isFiltered={searchQuery.length > 0}
              />
            </>
          )}

          <ProductDialog
            open={showAddDialog}
            onOpenChange={handleAddDialogOpen}
            onSubmit={handleProductSubmit}
            editingProduct={editingProduct}
          />

          <DeleteProductDialog
            open={isDeleting}
            onOpenChange={setIsDeleting}
            onConfirm={handleDeleteProduct}
          />
        </div>
      </DashboardLayout>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="h-[60vh] flex items-center justify-center">
          <Loading
            text="جاري تحميل المنتجات..."
            size={isMobile ? "md" : "lg"}
          />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <PageHeader
          title="المنتجات"
          description="إدارة منتجاتك وإضافة منتجات جديدة"
          icon={ShoppingCart}
          actions={
            <Button
              onClick={() => handleAddDialogOpen(true)}
              size={isMobile ? "sm" : "default"}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus
                className={`h-4 w-4 ml-1 ${isMobile ? "sm:ml-2" : "ml-2"}`}
              />
              {isMobile ? "إضافة" : "إضافة منتج"}
            </Button>
          }
        />

        <ProductSearch
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />

        <ProductsList
          products={filteredProducts}
          onEdit={handleEditProduct}
          onDelete={confirmDelete}
          onAddNew={() => handleAddDialogOpen(true)}
          isFiltered={searchQuery.length > 0}
        />

        <ProductDialog
          open={showAddDialog}
          onOpenChange={handleAddDialogOpen}
          onSubmit={handleProductSubmit}
          editingProduct={editingProduct}
        />

        <DeleteProductDialog
          open={isDeleting}
          onOpenChange={setIsDeleting}
          onConfirm={handleDeleteProduct}
        />
      </div>
    </DashboardLayout>
  );
};
