import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { AuthProvider } from "@/contexts/auth";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { QueryProvider } from "@/providers/QueryProvider";
import App from "./App";
import "./index.css";

// Wrap the app with the required providers
ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryProvider>
        <AuthProvider>
          <>
            <App />
          </>
        </AuthProvider>
      </QueryProvider>
    </BrowserRouter>
  </React.StrictMode>
);
