
import { supabase } from '@/integrations/supabase/client';

/**
 * هذه الوظيفة تتحقق مما إذا كان عمود 'active' موجودًا في جدول 'profiles'
 * وإذا لم يكن موجودًا، فستقوم بإضافته
 */
export async function addProfileActiveColumn() {
  try {
    console.log("Checking if 'active' column exists in profiles table...");
    
    // Get column information for the profiles table
    const { data: columnData, error: columnError } = await supabase.functions.invoke(
      "get-column-names",
      { 
        body: { table_name: 'profiles' } 
      }
    );
    
    if (columnError) {
      console.error("Error getting column info:", columnError);
      
      // Try alternative approach - create function if it doesn't exist
      const { error: fnError } = await supabase.functions.invoke(
        "create-get-column-names-function",
        { body: {} }
      );
      
      if (fnError) {
        console.error("Error creating function:", fnError);
        return { success: false, message: "Failed to check column existence", error: columnError };
      }
      
      // Retry after creating function
      const { data: retryData, error: retryError } = await supabase.functions.invoke(
        "get-column-names",
        { body: { table_name: 'profiles' } }
      );
      
      if (retryError) {
        console.error("Error on retry:", retryError);
        return { success: false, message: "Failed to check column existence on retry", error: retryError };
      }
      
      // Assign retry data if successful
      const columnInfo = retryData || [];
      
      // Check if active column exists
      const hasActiveColumn = Array.isArray(columnInfo) ? 
        columnInfo.some(colName => colName === 'active') : 
        false;
        
      if (hasActiveColumn) {
        console.log("'active' column already exists, no migration needed.");
        return { success: true, message: "Column already exists", added: false };
      }
    } else {
      // If no error, use the original data
      const columnInfo = columnData || [];
      
      // Check if active column exists
      const hasActiveColumn = Array.isArray(columnInfo) ? 
        columnInfo.some(colName => colName === 'active') : 
        false;
        
      if (hasActiveColumn) {
        console.log("'active' column already exists, no migration needed.");
        return { success: true, message: "Column already exists", added: false };
      }
    }
    
    console.log("'active' column doesn't exist, adding it...");
    
    // Add active column with default value true
    const { error: alterError } = await supabase.functions.invoke("add-column-to-table", {
      body: { 
        table: "profiles",
        column: "active",
        type: "boolean",
        default: true
      }
    });
    
    if (alterError) {
      console.error("Error adding column:", alterError);
      
      // Try direct query as fallback
      const { error: directError } = await supabase.functions.invoke("execute-sql", {
        body: { 
          sql: "ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS active boolean DEFAULT true" 
        }
      });
      
      if (directError) {
        console.error("Error with direct SQL:", directError);
        return { success: false, message: "Failed to add column", error: directError };
      }
    }
    
    console.log("Successfully added 'active' column to profiles table");
    return { success: true, message: "Column added successfully", added: true };
    
  } catch (error) {
    console.error("Exception in addProfileActiveColumn:", error);
    return { success: false, message: "Exception occurred", error };
  }
}
