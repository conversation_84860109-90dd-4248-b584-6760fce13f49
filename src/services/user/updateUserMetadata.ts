
import { supabase } from "@/integrations/supabase/client";
import { UserRole, AdminPermission } from "@/types";

/**
 * تحديث البيانات الوصفية للمستخدم (metadata)
 */
export async function updateUserMetadata(
  userId: string, 
  metadata: {
    role?: UserRole;
    name?: string;
    permissions?: AdminPermission[];
    active?: boolean;
    [key: string]: any;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log("تحديث البيانات الوصفية للمستخدم:", userId, metadata);
    
    // استدعاء edge function لتحديث البيانات الوصفية
    const { data, error } = await supabase.functions.invoke("update-user-metadata", {
      body: {
        userId,
        metadata,
      },
    });

    if (error) {
      console.error("خطأ في تحديث البيانات الوصفية:", error);
      return { success: false, error: error.message || error };
    }

    // تحديث جدول profiles أيضًا للحفاظ على التزامن
    const profileUpdates: Record<string, any> = {};
    
    if (metadata.name) profileUpdates.name = metadata.name;
    if (metadata.role) profileUpdates.role = metadata.role;
    if (metadata.active !== undefined) profileUpdates.active = metadata.active;
    
    // تحديث البيانات في جدول profiles إذا كان هناك تعديلات
    if (Object.keys(profileUpdates).length > 0) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', userId);
        
      if (profileError) {
        console.error("خطأ في تحديث بيانات الملف الشخصي:", profileError);
        return { success: true, error: "تم تحديث البيانات الوصفية لكن حدث خطأ في تحديث الملف الشخصي" };
      }
    }

    return { success: true };
  } catch (error: any) {
    console.error("خطأ في تحديث البيانات الوصفية:", error);
    return { success: false, error: error.message };
  }
}

/**
 * إنشاء أو تحديث مدير النظام
 */
export async function createOrUpdateAdmin(
  email: string,
  name: string = "مدير النظام"
): Promise<{ success: boolean; error?: string }> {
  try {
    // البحث عن المستخدم بالبريد الإلكتروني
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single();
      
    if (userError && userError.code !== 'PGRST116') {
      // خطأ غير متوقع
      return { success: false, error: userError.message };
    }
    
    if (!userData) {
      // المستخدم غير موجود، يجب إنشاؤه أولاً عبر واجهة الـ Admin في Supabase
      return { 
        success: false, 
        error: "المستخدم غير موجود. يرجى إنشاء المستخدم أولاً باستخدام لوحة تحكم Supabase" 
      };
    }
    
    // تحديث البيانات الوصفية للمستخدم ليصبح مدير
    return await updateUserMetadata(userData.id, {
      role: 'admin',
      name: name,
      permissions: ['manage_users', 'approve_payments', 'manage_transactions', 'manage_admins'],
      active: true
    });
    
  } catch (error: any) {
    console.error("خطأ في إنشاء/تحديث المدير:", error);
    return { success: false, error: error.message };
  }
}
