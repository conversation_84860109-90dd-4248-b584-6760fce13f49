
import { supabase } from "@/integrations/supabase/client";

/**
 * Delete a user
 */
export async function deleteUser(id: string): Promise<{ success: boolean, error?: string }> {
  try {
    // Note: We're deleting the auth user, which will cascade to delete the profile
    const { error } = await supabase.auth.admin.deleteUser(id);

    if (error) {
      console.error('Error deleting user:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in deleteUser:', error);
    return { success: false, error: error.message };
  }
}
