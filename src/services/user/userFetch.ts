import { supabase } from "@/integrations/supabase/client";
import { User, UserRole } from "@/types";
import { mapProfileToUser } from "./userMappers";

/**
 * Fetch all users with pagination support
 */
export async function fetchUsers(
  page = 1,
  limit = 10,
  search = "",
  role?: UserRole
): Promise<{ users: User[]; count: number }> {
  try {
    let query = supabase.from("profiles").select("*", { count: "exact" });

    // Apply search filter if provided
    if (search) {
      query = query.or(
        `name.ilike.%${search}%,email.ilike.%${search}%,phone_number.ilike.%${search}%`
      );
    }

    // Apply role filter if provided
    if (role) {
      query = query.eq("role", role);
    }

    // Apply pagination
    const start = (page - 1) * limit;
    const end = start + limit - 1;
    query = query.range(start, end);

    const { data: profiles, error, count } = await query;

    if (error) {
      console.error("Error fetching users:", error);
      throw new Error(error.message);
    }

    const profileIds = profiles.map((profile) => profile.id);
    console.log("Fetched profiles:", profiles);
    const { data: legalDetails, error: legalError } = await supabase
      .from("legal_details")
      .select("*")
      .in("profile_id", profileIds);

    if (legalError) {
      console.error("Error fetching legal details:", legalError);
      throw new Error(legalError.message);
    }
    // Map legal details by profile_id
    const legalDetailsMap = new Map(
      (legalDetails || []).map((detail) => [detail.profile_id, detail])
    );

    // Merge legal_details into profiles
    const users = profiles.map((profile) => {
      const legal_detail = legalDetailsMap.get(profile.id) || null;
      if (!legal_detail) {
        return mapProfileToUser(profile);
      }
      console.log("Legal detail:", legal_detail);

      return mapProfileToUser({
        ...profile,
        phone_number: legal_detail.contact_phone || "",
        legal_detail,
      });
    });

    console.log(users);

    return {
      users,
      count: count || 0,
    };
  } catch (error) {
    console.error("Error in fetchUsers:", error);
    throw error;
  }
}

/**
 * Fetch a user by their ID
 */
export async function fetchUserById(id: string): Promise<User | null> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching user:", error);
      return null;
    }

    if (!data) {
      return null;
    }

    return mapProfileToUser(data);
  } catch (error) {
    console.error("Error in fetchUserById:", error);
    return null;
  }
}
