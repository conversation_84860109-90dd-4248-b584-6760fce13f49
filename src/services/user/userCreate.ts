
import { supabase } from "@/integrations/supabase/client";
import { UserRole } from "@/types";

/**
 * Create a new user
 */
export async function createUser(userData: {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  phoneNumber?: string;
  address?: string;
}): Promise<{ success: boolean, error?: string, userId?: string }> {
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        name: userData.name,
        role: userData.role,
        phone_number: userData.phoneNumber || '',
        address: userData.address || '',
      },
    });

    if (error) {
      console.error('Error creating user:', error);
      return { success: false, error: error.message };
    }

    return { success: true, userId: data.user.id };
  } catch (error: any) {
    console.error('Error in createUser:', error);
    return { success: false, error: error.message };
  }
}
