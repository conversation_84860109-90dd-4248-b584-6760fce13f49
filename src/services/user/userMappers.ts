
import { User, UserRole, AdminPermission } from "@/types";

/**
 * Map a profile database record to a User object
 */
export function mapProfileToUser(profile: any): User {
  return {
    id: profile.id,
    name: profile.name,
    email: profile.email,
    role: profile.role as UserRole,
    balance: profile.balance || 0,
    created_at: profile.created_at,
    phone_number: profile.phone_number || '',
    address: profile.address || '',
    bank_account: profile.bank_account || '',
    avatar: profile.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.name)}&background=random`,
    legal_activity: profile.legal_activity || '',
    permissions: profile.permissions as AdminPermission[] || [],
    active: profile.active !== undefined ? profile.active : true,  // Ensure we handle active status correctly
  };
}
