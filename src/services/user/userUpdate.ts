
import { supabase } from '../../integrations/supabase/client';
import { User } from '@/types';

export interface UpdateUserResult {
  success: boolean;
  error?: string;
}

export const updateUser = async (userId: string, userData: Partial<User>): Promise<UpdateUserResult> => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const { error } = await supabase
      .from('profiles')
      .update(userData)
      .eq('id', userId);

    if (error) throw new Error(`Failed to update user: ${error.message}`);
    
    return { success: true };
  } catch (error: any) {
    console.error('Error updating user:', error);
    return { success: false, error: error.message };
  }
};
