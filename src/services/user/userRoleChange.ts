
import { supabase } from '../../integrations/supabase/client';
import { UserRole } from '@/types';

export const changeUserRole = async (userId: string, role: UserRole) => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    // Update the user's role in the profiles table
    const { error } = await supabase
      .from('profiles')
      .update({ role })
      .eq('id', userId);

    if (error) throw new Error(`Failed to update user role: ${error.message}`);
    
    return { success: true };
  } catch (error: any) {
    console.error('Error changing user role:', error);
    return { success: false, error: error.message };
  }
};
