import { supabase } from '@/integrations/supabase/client';
import { 
  OnboardingProgress, 
  OnboardingStep, 
  ProfileCompletionData, 
  WelcomeScreenData,
  OnboardingConfig,
  UserOnboardingState
} from '@/types/onboarding';
import { UserRole } from '@/types/user';

export const onboardingService = {
  /**
   * Get onboarding configuration based on user type
   */
  getOnboardingConfig(userType: 'store' | 'ecommerce'): OnboardingConfig {
    const commonSteps: OnboardingStep[] = [
      {
        id: 'welcome',
        title: 'مرحباً بك',
        description: 'تعرف على منصة رفوف وما تقدمه لك',
        icon: 'Heart',
        component: 'WelcomeStep',
        completed: false,
        required: true,
        order: 1,
        userTypes: ['store', 'ecommerce']
      },
      {
        id: 'personal_info',
        title: 'المعلومات الشخصية',
        description: 'أكمل معلوماتك الشخصية الأساسية',
        icon: 'User',
        component: 'PersonalInfoStep',
        completed: false,
        required: true,
        order: 2,
        userTypes: ['store', 'ecommerce']
      },
      {
        id: 'business_info',
        title: 'معلومات النشاط التجاري',
        description: 'أضف تفاصيل نشاطك التجاري',
        icon: 'Building',
        component: 'BusinessInfoStep',
        completed: false,
        required: true,
        order: 3,
        userTypes: ['store', 'ecommerce']
      },
      {
        id: 'location_info',
        title: 'معلومات الموقع',
        description: 'حدد موقع نشاطك التجاري',
        icon: 'MapPin',
        component: 'LocationInfoStep',
        completed: false,
        required: true,
        order: 4,
        userTypes: ['store', 'ecommerce']
      }
    ];

    const storeSpecificSteps: OnboardingStep[] = [
      {
        id: 'store_details',
        title: 'تفاصيل المحل',
        description: 'أضف معلومات محلك التجاري',
        icon: 'Store',
        component: 'StoreDetailsStep',
        completed: false,
        required: true,
        order: 5,
        userTypes: ['store']
      },
      {
        id: 'inventory_setup',
        title: 'إعداد المخزون',
        description: 'قم بإعداد نظام إدارة المخزون',
        icon: 'Package',
        component: 'InventorySetupStep',
        completed: false,
        required: false,
        order: 6,
        userTypes: ['store']
      }
    ];

    const ecommerceSpecificSteps: OnboardingStep[] = [
      {
        id: 'online_store_setup',
        title: 'إعداد المتجر الإلكتروني',
        description: 'قم بإعداد متجرك الإلكتروني',
        icon: 'Globe',
        component: 'OnlineStoreSetupStep',
        completed: false,
        required: true,
        order: 5,
        userTypes: ['ecommerce']
      },
      {
        id: 'payment_setup',
        title: 'إعداد وسائل الدفع',
        description: 'أضف وسائل الدفع المتاحة',
        icon: 'CreditCard',
        component: 'PaymentSetupStep',
        completed: false,
        required: false,
        order: 6,
        userTypes: ['ecommerce']
      }
    ];

    const finalSteps: OnboardingStep[] = [
      {
        id: 'preferences',
        title: 'التفضيلات',
        description: 'اختر تفضيلاتك للمنصة',
        icon: 'Settings',
        component: 'PreferencesStep',
        completed: false,
        required: false,
        order: 7,
        userTypes: ['store', 'ecommerce']
      },
      {
        id: 'completion',
        title: 'اكتمال الإعداد',
        description: 'تهانينا! لقد أكملت إعداد حسابك',
        icon: 'CheckCircle',
        component: 'CompletionStep',
        completed: false,
        required: true,
        order: 8,
        userTypes: ['store', 'ecommerce']
      }
    ];

    let steps = [...commonSteps];
    
    if (userType === 'store') {
      steps = [...steps, ...storeSpecificSteps];
    } else {
      steps = [...steps, ...ecommerceSpecificSteps];
    }
    
    steps = [...steps, ...finalSteps];

    // Filter steps for the specific user type and sort by order
    const filteredSteps = steps
      .filter(step => step.userTypes.includes(userType))
      .sort((a, b) => a.order - b.order);

    const welcomeData: WelcomeScreenData = {
      user_type: userType,
      welcome_message: userType === 'store' 
        ? 'مرحباً بك في منصة رفوف للمحلات التجارية!' 
        : 'مرحباً بك في منصة رفوف للمتاجر الإلكترونية!',
      subtitle: 'نحن سعداء بانضمامك إلينا. دعنا نساعدك في إعداد حسابك خطوة بخطوة.',
      features: userType === 'store' ? [
        {
          title: 'إدارة المخزون',
          description: 'تتبع منتجاتك ومخزونك بسهولة',
          icon: 'Package',
          color: 'blue'
        },
        {
          title: 'نقاط البيع',
          description: 'نظام نقاط بيع متطور وسهل الاستخدام',
          icon: 'CreditCard',
          color: 'green'
        },
        {
          title: 'التقارير والتحليلات',
          description: 'احصل على تقارير مفصلة عن مبيعاتك',
          icon: 'BarChart',
          color: 'purple'
        }
      ] : [
        {
          title: 'متجر إلكتروني متكامل',
          description: 'أنشئ متجرك الإلكتروني بسهولة',
          icon: 'Globe',
          color: 'blue'
        },
        {
          title: 'إدارة الطلبات',
          description: 'تتبع وإدارة طلبات عملائك',
          icon: 'ShoppingCart',
          color: 'green'
        },
        {
          title: 'التسويق الرقمي',
          description: 'أدوات تسويقية لزيادة مبيعاتك',
          icon: 'Megaphone',
          color: 'orange'
        }
      ],
      next_steps: [
        {
          title: 'أكمل ملفك الشخصي',
          description: 'أضف معلوماتك الأساسية',
          action: 'start_onboarding',
          icon: 'User'
        },
        {
          title: 'استكشف المنصة',
          description: 'تعرف على جميع الميزات المتاحة',
          action: 'explore_features',
          icon: 'Compass'
        },
        {
          title: 'احصل على المساعدة',
          description: 'فريق الدعم جاهز لمساعدتك',
          action: 'contact_support',
          icon: 'HelpCircle'
        }
      ],
      statistics: {
        total_users: 10000,
        total_stores: 5000,
        success_rate: '95%'
      }
    };

    return {
      steps: filteredSteps,
      welcome_screen: welcomeData,
      completion_rewards: {
        points: 100,
        badges: ['مرحب جديد', 'مكتمل الملف'],
        features_unlocked: ['تقارير متقدمة', 'دعم أولوية']
      }
    };
  },

  /**
   * Get user's onboarding progress
   */
  async getUserOnboardingProgress(userId: string): Promise<{ data: OnboardingProgress | null; error: any }> {
    const { data, error } = await supabase
      .from('onboarding_progress')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    return { data, error };
  },

  /**
   * Save or update user's onboarding progress
   */
  async saveOnboardingProgress(progress: OnboardingProgress): Promise<{ data: any; error: any }> {
    const { data: existingProgress } = await this.getUserOnboardingProgress(progress.user_id);

    if (existingProgress) {
      const { data, error } = await supabase
        .from('onboarding_progress')
        .update({
          current_step: progress.current_step,
          completed_steps: progress.completed_steps,
          total_steps: progress.total_steps,
          is_completed: progress.is_completed,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', progress.user_id)
        .select();

      return { data, error };
    } else {
      const { data, error } = await supabase
        .from('onboarding_progress')
        .insert([progress])
        .select();

      return { data, error };
    }
  },

  /**
   * Mark a step as completed
   */
  async completeStep(userId: string, stepId: string): Promise<{ data: any; error: any }> {
    const { data: progress } = await this.getUserOnboardingProgress(userId);
    
    if (progress) {
      const updatedCompletedSteps = [...progress.completed_steps];
      if (!updatedCompletedSteps.includes(stepId)) {
        updatedCompletedSteps.push(stepId);
      }

      const { data, error } = await supabase
        .from('onboarding_progress')
        .update({
          completed_steps: updatedCompletedSteps,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select();

      return { data, error };
    }

    return { data: null, error: 'No progress found' };
  },

  /**
   * Save profile completion data
   */
  async saveProfileData(userId: string, profileData: ProfileCompletionData): Promise<{ data: any; error: any }> {
    const { data, error } = await supabase
      .from('profile_completion')
      .upsert([
        {
          user_id: userId,
          ...profileData,
          updated_at: new Date().toISOString()
        }
      ])
      .select();

    return { data, error };
  },

  /**
   * Get profile completion data
   */
  async getProfileData(userId: string): Promise<{ data: ProfileCompletionData | null; error: any }> {
    const { data, error } = await supabase
      .from('profile_completion')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    return { data, error };
  }
};
