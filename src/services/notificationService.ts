
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

export interface EmailNotification {
  to: string;
  subject: string;
  message: string;
  name?: string;
}

export const sendEmailNotification = async (notification: EmailNotification) => {
  try {
    const { data, error } = await supabase.functions.invoke('send-admin-email', {
      body: {
        email: notification.to,
        subject: notification.subject,
        message: notification.message,
        name: notification.name || ''
      }
    });

    if (error) {
      console.error("Error sending notification email:", error);
      toast.error("فشل في إرسال البريد الإلكتروني");
      return false;
    }

    console.log("Email notification sent:", data);
    return true;
  } catch (error) {
    console.error("Exception sending email notification:", error);
    toast.error("حدث خطأ أثناء إرسال البريد الإلكتروني");
    return false;
  }
};

export const createSystemNotification = async (
  userId: string,
  title: string,
  message: string,
  type: 'info' | 'success' | 'error' | 'warning' = 'info',
  link?: string
) => {
  try {
    const { error } = await supabase.from('notifications').insert({
      id: uuidv4(),
      user_id: userId,
      title,
      message,
      type,
      link,
      read: false,
      created_at: new Date().toISOString()
    });

    if (error) {
      console.error("Error creating notification:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Exception creating notification:", error);
    return false;
  }
};

export const sendHostingRequestNotification = async (
  requestId: string,
  status: string,
  storeName: string,
  ecommerceId: string,
  ecommerceEmail: string,
  ecommerceName: string
) => {
  let subject = '';
  let message = '';
  
  if (status === 'pending') {
    subject = `طلب استضافة جديد في ${storeName}`;
    message = `تم إرسال طلب استضافة منتجاتك في متجر ${storeName} بنجاح. سيتم مراجعة الطلب والرد عليه قريبًا.`;
  } else if (status === 'accepted') {
    subject = `تم قبول طلب الاستضافة في ${storeName}`;
    message = `مبروك! تم قبول طلب استضافة منتجاتك في متجر ${storeName}. يمكنك الآن متابعة حالة طلبك من خلال لوحة التحكم.`;
  } else if (status === 'rejected') {
    subject = `تم رفض طلب الاستضافة في ${storeName}`;
    message = `نأسف لإبلاغك أنه تم رفض طلب استضافة منتجاتك في متجر ${storeName}.`;
  }

  if (subject && message) {
    // Create in-app notification
    await createSystemNotification(
      ecommerceId,
      subject,
      message,
      status === 'accepted' ? 'success' : status === 'rejected' ? 'error' : 'info',
      `/dashboard/hosting-requests/${requestId}`
    );

    // Send email notification using edge function
    const { data: { session: { access_token } } } = await supabase.auth.getSession();
    const { data, error } = await supabase.functions.invoke('send-admin-email', {
      body: {
        email: ecommerceEmail,
        subject,
        message,
        name: ecommerceName
      },
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    });

    if (error) {
      console.error("Error sending email notification:", error);
      toast.error("فشل في إرسال البريد الإلكتروني");
      return false;
    }

    console.log("Email notification sent:", data);
    return true;
  }

  return false;
};
