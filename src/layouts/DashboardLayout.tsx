import React, {
  ReactNode,
  useState,
  useEffect,
  Suspense,
  useLayoutEffect,
} from "react";
import { TopNavbar } from "@/components/TopNavbar";
import { Sidebar } from "@/components/Sidebar";
import { DirectionProvider } from "@/components/ui/direction-provider";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/contexts/auth";
import { Navigate, useNavigate } from "react-router-dom";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { Loading } from "@/components/ui/loading";
import { supabase } from "@/integrations/supabase/client";
import useLegalDetails from "@/utils/useLegalDetails";

interface DashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const { isAuthenticated, isLoading, user } = useAuth();
  const navigate = useNavigate();
  const { legalDetails, isLoading: legalLoading } = useLegalDetails();

  // Update sidebar state when screen size changes
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  const closeSidebar = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  useLayoutEffect(() => {
    if (legalLoading) return;
    if (!user || user.role === "admin") return;
    if (!user) {
      navigate("/auth/login");
      return;
    }
    if (!legalDetails) {
      navigate("/dashboard/legal-details");
    }
  }, [legalDetails, legalLoading, navigate, user]);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loading size="lg" />
      </div>
    );
  }

  // Use consistent rendering pattern to avoid hooks issues

  return (
    <NotificationProvider>
      <DirectionProvider defaultDirection="rtl">
        <div className="min-h-screen flex flex-col bg-background">
          <TopNavbar onMenuClick={toggleSidebar} />
          <div className="flex-1 flex flex-col sm:flex-row">
            <Sidebar
              className={cn(
                "w-full sm:w-64 sm:shrink-0 relative z-30",
                isMobile &&
                  "fixed inset-y-0 right-0 transform transition-transform duration-300 ease-in-out",
                isMobile && !sidebarOpen && "translate-x-full"
              )}
              isOpen={sidebarOpen}
              onClose={closeSidebar}
              isMobile={isMobile}
            />
            {isMobile && sidebarOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-20"
                onClick={closeSidebar}
                aria-hidden="true"
              />
            )}
            <main
              className={cn(
                "flex-1 p-3 sm:p-4 md:p-6 overflow-x-hidden transition-all duration-300",
                className
              )}
            >
              <Suspense fallback={<Loading />}>{children}</Suspense>
            </main>
          </div>
        </div>
      </DirectionProvider>
    </NotificationProvider>
  );
}

export default DashboardLayout;
