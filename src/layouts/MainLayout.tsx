import React, { ReactNode } from "react";
import { TopNavbar } from "@/components/TopNavbar";
import { DirectionProvider } from "@/components/ui/direction-provider";
import { NotificationProvider } from "@/contexts/NotificationContext";

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <DirectionProvider defaultDirection="rtl">
      <NotificationProvider>
        <div className="min-h-screen flex flex-col bg-background">
          <TopNavbar />
          <main className="flex-1">{children}</main>
        </div>
      </NotificationProvider>
    </DirectionProvider>
  );
}

export default MainLayout;
