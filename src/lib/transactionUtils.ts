
import { TransactionStatus } from '@/types';

// Format currency to Saudi Riyal
export const formatCurrency = (amount: number): string => {
  return `${amount.toLocaleString('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })} ريال`;
};

// Format date for consistent display
export const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return dateString;
  }
};

// Convert transaction status to readable Arabic text
export const getTransactionStatusText = (status: TransactionStatus): string => {
  switch (status) {
    case 'pending':
      return 'قيد الانتظار';
    case 'completed':
      return 'مكتملة';
    case 'failed':
      return 'فاشلة';
    case 'cancelled':
      return 'ملغية';
    case 'approved':
      return 'معتمدة';
    case 'rejected':
      return 'مرفوضة';
    case 'processing':
      return 'قيد المعالجة';
    default:
      return status;
  }
};

// Get appropriate badge color based on status
export const getStatusBadgeColor = (status: TransactionStatus): string => {
  switch (status) {
    case 'completed':
    case 'approved':
      return 'bg-green-100 text-green-800 border-green-300';
    case 'pending':
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 'rejected':
    case 'failed':
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300';
  }
};

// Get transaction type in Arabic
export const getTransactionTypeText = (type: string): string => {
  switch (type) {
    case 'deposit':
      return 'إيداع';
    case 'withdrawal':
    case 'withdraw':
      return 'سحب';
    case 'transfer':
      return 'تحويل';
    case 'payment':
      return 'دفع';
    case 'refund':
      return 'استرداد';
    case 'commission':
      return 'عمولة';
    case 'fee':
      return 'رسوم';
    default:
      return type;
  }
};

// This is the same as getStatusBadgeColor but exported with a different name
// for compatibility with existing imports
export const getTransactionStatusColor = (status: TransactionStatus): string => {
  return getStatusBadgeColor(status);
};
