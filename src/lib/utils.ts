
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format, formatDistanceToNow, isToday, isYesterday } from "date-fns";
import { ar } from "date-fns/locale";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: string | Date): string {
  if (!date) return "";
  
  const dateObj = typeof date === "string" ? new Date(date) : date;
  
  if (isToday(dateObj)) {
    return `اليوم، ${format(dateObj, "h:mm a", { locale: ar })}`;
  }
  
  if (isYesterday(dateObj)) {
    return `الأمس، ${format(dateObj, "h:mm a", { locale: ar })}`;
  }
  
  // If it's within the last 7 days
  if (dateObj > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
    return formatDistanceToNow(dateObj, { addSuffix: true, locale: ar });
  }
  
  // For older dates
  return format(dateObj, "dd/MM/yyyy", { locale: ar });
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
    maximumFractionDigits: 2,
  }).format(amount);
}
