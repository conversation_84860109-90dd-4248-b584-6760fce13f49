export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      branches: {
        Row: {
          active: boolean | null;
          address: string;
          city: string;
          created_at: string;
          id: string;
          images: string[] | null;
          legal_activity: string | null;
          manager_name: string | null;
          name: string;
          phone_number: string;
          store_id: string;
          updated_at: string;
          working_hours: string;
        };
        Insert: {
          active?: boolean | null;
          address: string;
          city: string;
          created_at?: string;
          id?: string;
          images?: string[] | null;
          legal_activity?: string | null;
          manager_name?: string | null;
          name: string;
          phone_number: string;
          store_id: string;
          updated_at?: string;
          working_hours: string;
        };
        Update: {
          active?: boolean | null;
          address?: string;
          city?: string;
          created_at?: string;
          id?: string;
          images?: string[] | null;
          legal_activity?: string | null;
          manager_name?: string | null;
          name?: string;
          phone_number?: string;
          store_id?: string;
          updated_at?: string;
          working_hours?: string;
        };
        Relationships: [
          {
            foreignKeyName: "branches_store_id_fkey";
            columns: ["store_id"];
            isOneToOne: false;
            referencedRelation: "stores";
            referencedColumns: ["id"];
          }
        ];
      };
      hosting_products: {
        Row: {
          hosting_request_id: string;
          id: string;
          image: string | null;
          price: number;
          product_id: string;
          product_name: string;
          quantity: number;
        };
        Insert: {
          hosting_request_id: string;
          id?: string;
          image?: string | null;
          price: number;
          product_id: string;
          product_name: string;
          quantity: number;
        };
        Update: {
          hosting_request_id?: string;
          id?: string;
          image?: string | null;
          price?: number;
          product_id?: string;
          product_name?: string;
          quantity?: number;
        };
        Relationships: [
          {
            foreignKeyName: "hosting_products_hosting_request_id_fkey";
            columns: ["hosting_request_id"];
            isOneToOne: false;
            referencedRelation: "hosting_requests";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "hosting_products_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          }
        ];
      };
      hosting_requests: {
        Row: {
          contract_id: string | null;
          created_at: string;
          ecommerce_id: string;
          ecommerce_legal_activity: string | null;
          ecommerce_name: string;
          expires_at: string | null;
          id: string;
          notes: string | null;
          receipt_confirmed: boolean | null;
          shipping_confirmed: boolean | null;
          status: string;
          store_id: string;
          store_name: string;
          subscription_type: Database["public"]["Enums"]["subscription"];
          updated_at: string;
        };
        Insert: {
          contract_id?: string | null;
          created_at?: string;
          ecommerce_id: string;
          ecommerce_legal_activity?: string | null;
          ecommerce_name: string;
          expires_at?: string | null;
          id?: string;
          notes?: string | null;
          receipt_confirmed?: boolean | null;
          shipping_confirmed?: boolean | null;
          status: string;
          store_id: string;
          store_name: string;
          subscription_type?: Database["public"]["Enums"]["subscription"];
          updated_at?: string;
        };
        Update: {
          contract_id?: string | null;
          created_at?: string;
          ecommerce_id?: string;
          ecommerce_legal_activity?: string | null;
          ecommerce_name?: string;
          expires_at?: string | null;
          id?: string;
          notes?: string | null;
          receipt_confirmed?: boolean | null;
          shipping_confirmed?: boolean | null;
          status?: string;
          store_id?: string;
          store_name?: string;
          subscription_type?: Database["public"]["Enums"]["subscription"];
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "hosting_requests_ecommerce_id_fkey";
            columns: ["ecommerce_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "hosting_requests_ecommerce_id_fkey";
            columns: ["ecommerce_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "hosting_requests_store_id_fkey";
            columns: ["store_id"];
            isOneToOne: false;
            referencedRelation: "stores";
            referencedColumns: ["id"];
          }
        ];
      };
      invoices: {
        Row: {
          amount: number;
          completed_at: string | null;
          created_at: string;
          due_date: string;
          ecommerce_id: string;
          exported_at: string | null;
          hosting_request_id: string;
          id: string;
          status: string;
          store_id: string;
          tax_amount: number;
          total_amount: number;
        };
        Insert: {
          amount: number;
          completed_at?: string | null;
          created_at?: string;
          due_date: string;
          ecommerce_id: string;
          exported_at?: string | null;
          hosting_request_id: string;
          id?: string;
          status: string;
          store_id: string;
          tax_amount: number;
          total_amount: number;
        };
        Update: {
          amount?: number;
          completed_at?: string | null;
          created_at?: string;
          due_date?: string;
          ecommerce_id?: string;
          exported_at?: string | null;
          hosting_request_id?: string;
          id?: string;
          status?: string;
          store_id?: string;
          tax_amount?: number;
          total_amount?: number;
        };
        Relationships: [
          {
            foreignKeyName: "invoices_ecommerce_id_fkey";
            columns: ["ecommerce_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoices_ecommerce_id_fkey";
            columns: ["ecommerce_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoices_hosting_request_id_fkey";
            columns: ["hosting_request_id"];
            isOneToOne: false;
            referencedRelation: "hosting_requests";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoices_store_id_fkey";
            columns: ["store_id"];
            isOneToOne: false;
            referencedRelation: "stores";
            referencedColumns: ["id"];
          }
        ];
      };
      legal_details: {
        Row: {
          bank_account: string | null;
          commercial_registry: string | null;
          contact_phone: string | null;
          created_at: string | null;
          id: string;
          legal_activity: string | null;
          legal_name: string | null;
          national_id: string | null;
          profile_id: string;
          registration_number: string | null;
          tax_certificate: string | null;
          tax_number: string | null;
          updated_at: string | null;
        };
        Insert: {
          bank_account?: string | null;
          commercial_registry?: string | null;
          contact_phone?: string | null;
          created_at?: string | null;
          id?: string;
          legal_activity?: string | null;
          legal_name?: string | null;
          national_id?: string | null;
          profile_id: string;
          registration_number?: string | null;
          tax_certificate?: string | null;
          tax_number?: string | null;
          updated_at?: string | null;
        };
        Update: {
          bank_account?: string | null;
          commercial_registry?: string | null;
          contact_phone?: string | null;
          created_at?: string | null;
          id?: string;
          legal_activity?: string | null;
          legal_name?: string | null;
          national_id?: string | null;
          profile_id?: string;
          registration_number?: string | null;
          tax_certificate?: string | null;
          tax_number?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "legal_details_profile_id_fkey";
            columns: ["profile_id"];
            isOneToOne: true;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "legal_details_profile_id_fkey";
            columns: ["profile_id"];
            isOneToOne: true;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      notifications: {
        Row: {
          created_at: string;
          id: string;
          link: string | null;
          message: string;
          read: boolean | null;
          title: string;
          type: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          link?: string | null;
          message: string;
          read?: boolean | null;
          title: string;
          type: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          link?: string | null;
          message?: string;
          read?: boolean | null;
          title?: string;
          type?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "notifications_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      onboarding_progress: {
        Row: {
          id: string;
          user_id: string;
          current_step: number;
          completed_steps: string[];
          total_steps: number;
          is_completed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          current_step?: number;
          completed_steps?: string[];
          total_steps?: number;
          is_completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          current_step?: number;
          completed_steps?: string[];
          total_steps?: number;
          is_completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "onboarding_progress_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "onboarding_progress_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      otps: {
        Row: {
          created_at: string;
          email: string;
          expires_at: string;
          id: number;
          otp: string;
          type: string;
        };
        Insert: {
          created_at?: string;
          email: string;
          expires_at: string;
          id?: number;
          otp: string;
          type: string;
        };
        Update: {
          created_at?: string;
          email?: string;
          expires_at?: string;
          id?: number;
          otp?: string;
          type?: string;
        };
        Relationships: [];
      };
      products: {
        Row: {
          category: string | null;
          created_at: string;
          description: string | null;
          id: string;
          images: string[] | null;
          name: string;
          price: number;
          quantity: number;
          seller_id: string;
          seller_name: string;
          status: string | null;
          updated_at: string;
          user_id: string | null;
        };
        Insert: {
          category?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          images?: string[] | null;
          name: string;
          price: number;
          quantity: number;
          seller_id: string;
          seller_name: string;
          status?: string | null;
          updated_at?: string;
          user_id?: string | null;
        };
        Update: {
          category?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          images?: string[] | null;
          name?: string;
          price?: number;
          quantity?: number;
          seller_id?: string;
          seller_name?: string;
          status?: string | null;
          updated_at?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "products_seller_id_fkey";
            columns: ["seller_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "products_seller_id_fkey";
            columns: ["seller_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          active: boolean | null;
          address: string | null;
          avatar: string | null;
          balance: number;
          bank_account: string | null;
          created_at: string;
          email: string;
          iban: string | null;
          id: string;
          legal_activity: string | null;
          name: string;
          permissions: string[] | null;
          phone_number: string | null;
          role: string;
          updated_at: string | null;
        };
        Insert: {
          active?: boolean | null;
          address?: string | null;
          avatar?: string | null;
          balance?: number;
          bank_account?: string | null;
          created_at?: string;
          email: string;
          iban?: string | null;
          id: string;
          legal_activity?: string | null;
          name: string;
          permissions?: string[] | null;
          phone_number?: string | null;
          role: string;
          updated_at?: string | null;
        };
        Update: {
          active?: boolean | null;
          address?: string | null;
          avatar?: string | null;
          balance?: number;
          bank_account?: string | null;
          created_at?: string;
          email?: string;
          iban?: string | null;
          id?: string;
          legal_activity?: string | null;
          name?: string;
          permissions?: string[] | null;
          phone_number?: string | null;
          role?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };

      store_employees: {
        Row: {
          active: boolean | null;
          branch_id: string | null;
          created_at: string;
          email: string;
          id: string;
          name: string;
          permissions: string[] | null;
          phone_number: string;
          position: string;
          store_id: string;
          updated_at: string;
        };
        Insert: {
          active?: boolean | null;
          branch_id?: string | null;
          created_at?: string;
          email: string;
          id?: string;
          name: string;
          permissions?: string[] | null;
          phone_number: string;
          position: string;
          store_id: string;
          updated_at?: string;
        };
        Update: {
          active?: boolean | null;
          branch_id?: string | null;
          created_at?: string;
          email?: string;
          id?: string;
          name?: string;
          permissions?: string[] | null;
          phone_number?: string;
          position?: string;
          store_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "store_employees_branch_id_fkey";
            columns: ["branch_id"];
            isOneToOne: false;
            referencedRelation: "branches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "store_employees_store_id_fkey";
            columns: ["store_id"];
            isOneToOne: false;
            referencedRelation: "stores";
            referencedColumns: ["id"];
          }
        ];
      };
      stores: {
        Row: {
          address: string;
          capacity: number | null;
          city: string;
          created_at: string;
          description: string | null;
          email: string;
          featured: boolean | null;
          gallery: string[] | null;
          hours: string;
          id: string;
          image: string | null;
          legal_activity: string | null;
          name: string;
          owner_id: string;
          phone: string;
          rating: number | null;
          shelf_space: string | null;
          subscription: string | null;
        };
        Insert: {
          address: string;
          capacity?: number | null;
          city: string;
          created_at?: string;
          description?: string | null;
          email: string;
          featured?: boolean | null;
          gallery?: string[] | null;
          hours: string;
          id?: string;
          image?: string | null;
          legal_activity?: string | null;
          name: string;
          owner_id: string;
          phone: string;
          rating?: number | null;
          shelf_space?: string | null;
          subscription?: string | null;
        };
        Update: {
          address?: string;
          capacity?: number | null;
          city?: string;
          created_at?: string;
          description?: string | null;
          email?: string;
          featured?: boolean | null;
          gallery?: string[] | null;
          hours?: string;
          id?: string;
          image?: string | null;
          legal_activity?: string | null;
          name?: string;
          owner_id?: string;
          phone?: string;
          rating?: number | null;
          shelf_space?: string | null;
          subscription?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "stores_owner_id_fkey";
            columns: ["owner_id"];
            isOneToOne: true;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stores_owner_id_fkey";
            columns: ["owner_id"];
            isOneToOne: true;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      transactions: {
        Row: {
          amount: number;
          created_at: string;
          description: string;
          from_user_id: string | null;
          id: string;
          receipt_url: string | null;
          reference: string | null;
          related_order_id: string | null;
          status: string;
          to_user_id: string | null;
          type: string;
          updated_at: string | null;
          user_id: string;
          user_name: string;
        };
        Insert: {
          amount: number;
          created_at?: string;
          description: string;
          from_user_id?: string | null;
          id?: string;
          receipt_url?: string | null;
          reference?: string | null;
          related_order_id?: string | null;
          status: string;
          to_user_id?: string | null;
          type: string;
          updated_at?: string | null;
          user_id: string;
          user_name: string;
        };
        Update: {
          amount?: number;
          created_at?: string;
          description?: string;
          from_user_id?: string | null;
          id?: string;
          receipt_url?: string | null;
          reference?: string | null;
          related_order_id?: string | null;
          status?: string;
          to_user_id?: string | null;
          type?: string;
          updated_at?: string | null;
          user_id?: string;
          user_name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "transactions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "admin_public_data";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "transactions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      admin_public_data: {
        Row: {
          bank_account: string | null;
          email: string | null;
          iban: string | null;
          id: string | null;
          name: string | null;
        };
        Insert: {
          bank_account?: string | null;
          email?: string | null;
          iban?: string | null;
          id?: string | null;
          name?: string | null;
        };
        Update: {
          bank_account?: string | null;
          email?: string | null;
          iban?: string | null;
          id?: string | null;
          name?: string | null;
        };
        Relationships: [];
      };
      onboarding_analytics: {
        Row: {
          id: string | null;
          user_id: string | null;
          current_step: number | null;
          completed_steps: string[] | null;
          total_steps: number | null;
          is_completed: boolean | null;
          created_at: string | null;
          updated_at: string | null;
          email: string | null;
          user_name: string | null;
          user_role: string | null;
          completion_percentage: number | null;
        };
        Insert: {
          id?: string | null;
          user_id?: string | null;
          current_step?: number | null;
          completed_steps?: string[] | null;
          total_steps?: number | null;
          is_completed?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          email?: string | null;
          user_name?: string | null;
          user_role?: string | null;
          completion_percentage?: number | null;
        };
        Update: {
          id?: string | null;
          user_id?: string | null;
          current_step?: number | null;
          completed_steps?: string[] | null;
          total_steps?: number | null;
          is_completed?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          email?: string | null;
          user_name?: string | null;
          user_role?: string | null;
          completion_percentage?: number | null;
        };
        Relationships: [];
      };
    };
    Functions: {
      create_hosting_request: {
        Args: { request_data: Json; products_data: Json[] };
        Returns: Json;
      };
      get_onboarding_completion_rate: {
        Args: { user_id_param: string };
        Returns: number;
      };
      get_user_role: {
        Args: { user_id: string };
        Returns: string;
      };
      is_admin: {
        Args: { user_id: string };
        Returns: boolean;
      };
    };
    Enums: {
      subscription: "percentage" | "fixed";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      subscription: ["percentage", "fixed"],
    },
  },
} as const;
