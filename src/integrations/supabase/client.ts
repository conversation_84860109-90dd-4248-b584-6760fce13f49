// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dzhrjkuwvfqgljowmnsy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR6aHJqa3V3dmZxZ2xqb3dtbnN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NTE1ODgsImV4cCI6MjA1OTIyNzU4OH0.6eV_VzYbqQVpGVJpU_aORmFzQLAd7rbb-k-ZuNVw8Gk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);