
/**
 * Utility for monitoring and reporting performance metrics
 */

// Measure component render time
export const measureRenderTime = (componentName: string) => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log slow renders (over 50ms)
    if (duration > 50) {
      console.warn(`Slow render: ${componentName} took ${duration.toFixed(2)}ms to render`);
    }
    
    // You could send this to an analytics service in production
    if (import.meta.env.PROD) {
      // Example: sendToAnalytics(`render_${componentName}`, duration);
    }
  };
};

// Track data fetching performance
export const trackQueryPerformance = (queryName: string) => {
  const startTime = performance.now();
  
  return {
    success: () => {
      const duration = performance.now() - startTime;
      console.log(`Query: ${queryName} completed in ${duration.toFixed(2)}ms`);
      
      // Track very slow queries
      if (duration > 1000) {
        console.warn(`Slow query: ${queryName} took over 1 second`);
      }
    },
    error: (error: any) => {
      const duration = performance.now() - startTime;
      console.error(`Query: ${queryName} failed after ${duration.toFixed(2)}ms`, error);
    }
  };
};

// Add marks and measures for performance analysis
export const markStart = (name: string) => {
  performance.mark(`${name}-start`);
};

export const markEnd = (name: string) => {
  if (performance) {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const entries = performance.getEntriesByName(name);
    if (entries.length > 0) {
      console.log(`${name}: ${entries[0].duration.toFixed(2)}ms`);
    }
  }
};

// Helper to detect slow interactions
export const monitorInteraction = (actionName: string, callback: Function) => {
  return async (...args: any[]) => {
    const start = performance.now();
    try {
      const result = await callback(...args);
      const duration = performance.now() - start;
      
      if (duration > 300) {
        console.warn(`Slow interaction: ${actionName} took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      console.error(`Interaction ${actionName} failed after ${duration.toFixed(2)}ms`, error);
      throw error;
    }
  };
};
