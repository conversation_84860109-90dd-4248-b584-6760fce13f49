
// Helper functions for branch operations
import { supabase } from '@/integrations/supabase/client';
import { Branch } from '@/types/branch';
import { toast } from 'sonner';

// Create a new branch
export const createBranch = async (branch: Omit<Branch, 'id'>) => {
  try {
    const { data, error } = await supabase.from('branches').insert({
      name: branch.name,
      city: branch.city,
      address: branch.address,
      store_id: branch.store_id,
      working_hours: branch.working_hours,
      phone_number: branch.phone_number,
      manager_name: branch.manager_name,
      legal_activity: branch.legal_activity,
      images: branch.images,
      active: branch.active,
    }).select().single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating branch:', error);
    toast.error('Failed to create branch');
    return null;
  }
};

// Update an existing branch
export const updateBranch = async (branch: Branch) => {
  try {
    const { data, error } = await supabase.from('branches').update({
      name: branch.name,
      city: branch.city,
      address: branch.address,
      store_id: branch.store_id,
      working_hours: branch.working_hours,
      phone_number: branch.phone_number,
      manager_name: branch.manager_name,
      legal_activity: branch.legal_activity,
      images: branch.images,
      active: branch.active,
    }).eq('id', branch.id).select().single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating branch:', error);
    toast.error('Failed to update branch');
    return null;
  }
};

// Delete a branch
export const deleteBranch = async (id: string) => {
  try {
    const { error } = await supabase.from('branches').delete().eq('id', id);
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting branch:', error);
    toast.error('Failed to delete branch');
    return false;
  }
};

// Get store ID from branch ID
export const getStoreId = async (branchId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('branches')
      .select('store_id')
      .eq('id', branchId)
      .single();
    
    if (error) {
      console.error('Error fetching store ID:', error);
      return null;
    }
    
    return data ? data.store_id : null;
  } catch (error) {
    console.error('Error fetching store ID:', error);
    return null;
  }
};

// Get a branch by ID
export const getBranchById = async (id: string): Promise<Branch | null> => {
  try {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error('Error fetching branch:', error);
      return null;
    }
    
    return data as Branch;
  } catch (error) {
    console.error('Error fetching branch:', error);
    return null;
  }
};

// Save branch function - make sure to use phone_number consistently
export const saveBranch = async (branch: any, userId: string) => {
  try {
    // Convert phone to phone_number if it exists
    const phone_number = branch.phone || branch.phone_number || '';
    
    const updatedBranch = {
      ...branch,
      store_id: userId,
      phone_number: phone_number,
      active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Remove the phone property if it exists
    if ('phone' in updatedBranch) {
      delete updatedBranch.phone;
    }
    
    // For new branches
    if (!updatedBranch.id) {
      const { data: newBranch, error } = await supabase
        .from('branches')
        .insert(updatedBranch)
        .select()
        .single();
      
      if (error) throw error;
      return newBranch;
    } 
    // For existing branches
    else {
      const { data: updatedBranchResult, error } = await supabase
        .from('branches')
        .update({
          name: updatedBranch.name,
          city: updatedBranch.city,
          address: updatedBranch.address,
          working_hours: updatedBranch.working_hours,
          phone_number: updatedBranch.phone_number,
          manager_name: updatedBranch.manager_name,
          legal_activity: updatedBranch.legal_activity,
          images: updatedBranch.images,
          active: updatedBranch.active,
          updated_at: new Date().toISOString()
        })
        .eq('id', updatedBranch.id)
        .select()
        .single();
      
      if (error) throw error;
      return updatedBranchResult;
    }
  } catch (error) {
    console.error('Error saving branch:', error);
    toast.error('Failed to save branch');
    throw error;
  }
};
