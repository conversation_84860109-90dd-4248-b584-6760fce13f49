
import { RetailStoreDetails } from '@/types';

export const updateStoreDetails = async (
  setStoreDetails: React.Dispatch<React.SetStateAction<RetailStoreDetails | null>>,
  updates: Partial<RetailStoreDetails>
): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  setStoreDetails(prev => prev ? { ...prev, ...updates } : null);
  return true;
};
