
import { UserRole, AdminPermission } from '@/types';

/**
 * Translates the user role to Arabic
 * @param role The user role
 * @returns The Arabic translation of the role
 */
export function translateRoleToArabic(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'مدير النظام';
    case 'sub-admin':
      return 'مدير فرعي';
    case 'store':
      return 'متجر تقليدي';
    case 'ecommerce':
      return 'متجر إلكتروني';
    default:
      return 'مستخدم';
  }
}

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userPermissions: AdminPermission[] | undefined, requiredPermission: AdminPermission): boolean {
  if (!userPermissions || userPermissions.length === 0) return false;
  return userPermissions.includes(requiredPermission);
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(userPermissions: AdminPermission[] | undefined, requiredPermissions: AdminPermission[]): boolean {
  if (!userPermissions || userPermissions.length === 0) return false;
  return requiredPermissions.some(permission => userPermissions.includes(permission));
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(userPermissions: AdminPermission[] | undefined, requiredPermissions: AdminPermission[]): boolean {
  if (!userPermissions || userPermissions.length === 0) return false;
  return requiredPermissions.every(permission => userPermissions.includes(permission));
}

/**
 * Get the color for a role
 */
export function getRoleColor(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'sub-admin':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'store':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'ecommerce':
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

/**
 * Check if the user can manage finances
 */
export function canManageFinances(userRole: UserRole, userPermissions?: AdminPermission[]): boolean {
  if (userRole === 'admin') return true;
  
  // Sub-admins need specific permissions
  if (userRole === 'sub-admin') {
    return hasPermission(userPermissions, 'approve_payments') || 
           hasPermission(userPermissions, 'manage_transactions');
  }
  
  return false;
}

/**
 * Check if the user can manage admins
 */
export function canManageAdmins(userRole: UserRole, userPermissions?: AdminPermission[]): boolean {
  if (userRole === 'admin') return true;
  
  // Sub-admins need specific permissions
  if (userRole === 'sub-admin') {
    return hasPermission(userPermissions, 'manage_admins');
  }
  
  return false;
}

/**
 * Check if the user can manage users
 */
export function canManageUsers(userRole: UserRole, userPermissions?: AdminPermission[]): boolean {
  if (userRole === 'admin') return true;
  
  // Sub-admins need specific permissions
  if (userRole === 'sub-admin') {
    return hasPermission(userPermissions, 'manage_users');
  }
  
  return false;
}

/**
 * Check if the user can manage products
 */
export function canManageProducts(userRole: UserRole, userPermissions?: AdminPermission[]): boolean {
  if (userRole === 'admin') return true;
  
  // Sub-admins with manage_users permission can also manage products
  if (userRole === 'sub-admin') {
    return hasPermission(userPermissions, 'manage_users');
  }
  
  return false;
}

/**
 * Get available permissions for a role
 */
export function getAvailablePermissions(role: UserRole): AdminPermission[] {
  if (role === 'admin') {
    return ['manage_users', 'approve_payments', 'manage_transactions', 'manage_admins'];
  }
  
  if (role === 'sub-admin') {
    return ['manage_users', 'approve_payments', 'manage_transactions'];
  }
  
  return [];
}

/**
 * Translate permission to Arabic
 */
export function translatePermissionToArabic(permission: AdminPermission): string {
  switch (permission) {
    case 'manage_users':
      return 'إدارة المستخدمين';
    case 'approve_payments':
      return 'اعتماد المدفوعات';
    case 'manage_transactions':
      return 'إدارة المعاملات المالية';
    case 'manage_admins':
      return 'إدارة المسؤولين';
    default:
      return permission;
  }
}

export default {
  translateRoleToArabic,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getRoleColor,
  canManageFinances,
  canManageAdmins,
  canManageUsers,
  canManageProducts,
  getAvailablePermissions,
  translatePermissionToArabic
};
