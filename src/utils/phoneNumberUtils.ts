
/**
 * Utilities for Saudi phone number validation and formatting
 */

// Validate if a string is a valid Saudi phone number
export const isValidSaudiPhoneNumber = (phoneNumber: string): boolean => {
  // Remove any non-digit characters for validation
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid Saudi mobile number
  // Valid formats: 05XXXXXXXX or 5XXXXXXXX or +9665XXXXXXXX or 9665XXXXXXXX
  const validSaudiMobileRegex = /^(05\d{8}|\+9665\d{8}|9665\d{8}|5\d{8})$/;
  
  return validSaudiMobileRegex.test(digitsOnly);
};

// Format a phone number to Saudi format
export const formatSaudiPhoneNumber = (phoneNumber: string): string => {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // Handle different input formats
  if (digitsOnly.startsWith('966') && digitsOnly.length >= 12) {
    // If input is like 9665XXXXXXXX, format to 05XXXXXXXX
    return `0${digitsOnly.substring(3, 12)}`;
  } else if (digitsOnly.startsWith('5') && digitsOnly.length >= 9) {
    // If input is like 5XXXXXXXX, format to 05XXXXXXXX
    return `0${digitsOnly.substring(0, 9)}`;
  } else if (digitsOnly.startsWith('05') && digitsOnly.length >= 10) {
    // If input is already like 05XXXXXXXX, just return the first 10 digits
    return digitsOnly.substring(0, 10);
  }
  
  // If it doesn't match expected patterns, return the original input
  return phoneNumber;
};

// Normalize phone number for storage or API calls
export const normalizeSaudiPhoneNumber = (phoneNumber: string): string => {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // If it starts with 0, remove it and add +966
  if (digitsOnly.startsWith('0') && digitsOnly.length >= 10) {
    return `+966${digitsOnly.substring(1, 10)}`;
  } 
  // If it starts with 5, add +966
  else if (digitsOnly.startsWith('5') && digitsOnly.length >= 9) {
    return `+966${digitsOnly.substring(0, 9)}`;
  }
  // If it already contains 966, ensure it has + prefix
  else if (digitsOnly.startsWith('966') && digitsOnly.length >= 12) {
    return `+${digitsOnly.substring(0, 12)}`;
  }
  
  // If no pattern matched, return the formatted number
  return formatSaudiPhoneNumber(phoneNumber);
};
