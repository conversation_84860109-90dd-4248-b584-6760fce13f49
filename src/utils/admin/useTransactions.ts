
import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Transaction, TransactionStatus } from "@/types";
import { useAdminTransactionOperations } from "@/hooks/useAdminTransactionOperations";

// Filter interface
export interface TransactionFilters {
  type: string;
  status: string;
  search: string;
  dateFrom: string;
  dateTo: string;
}

export const useTransactions = () => {
  const { approveTransaction, rejectTransaction, isApproving, isRejecting } = useAdminTransactionOperations();

  // Filters state
  const [filters, setFilters] = useState<TransactionFilters>({
    type: "all",
    status: "all",
    search: "",
    dateFrom: "",
    dateTo: "",
  });

  // Fetch all transactions
  const {
    data: transactions = [],
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["adminTransactions", filters],
    queryFn: async () => {
      console.log("Fetching admin transactions with filters:", filters);
      
      let query = supabase
        .from("transactions")
        .select("*");
      
      // Apply database-side filters where possible
      if (filters.type !== "all") {
        query = query.eq("type", filters.type);
      }
      
      if (filters.status !== "all") {
        query = query.eq("status", filters.status);
      }
      
      if (filters.dateFrom) {
        query = query.gte("created_at", filters.dateFrom);
      }
      
      if (filters.dateTo) {
        const toDate = new Date(filters.dateTo);
        toDate.setHours(23, 59, 59, 999); // End of day
        query = query.lte("created_at", toDate.toISOString());
      }
      
      // Order by most recent first
      query = query.order("created_at", { ascending: false });
      
      const { data, error } = await query;
      
      if (error) {
        console.error("Error fetching transactions:", error);
        throw new Error(error.message);
      }
      
      console.log(`Fetched ${data?.length || 0} transactions`);
      return data as Transaction[];
    },
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true
  });

  // Filter transactions based on search (client-side)
  const filteredTransactions = useMemo(() => {
    if (!transactions) return [];
    
    let filtered = [...transactions];
    
    // Filter by search term (client-side)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(
        (tx) =>
          tx.user_name?.toLowerCase().includes(searchTerm) ||
          tx.reference?.toLowerCase().includes(searchTerm) ||
          tx.description?.toLowerCase().includes(searchTerm)
      );
    }

    return filtered;
  }, [transactions, filters.search]);

  // Update a specific filter
  const updateFilter = (key: keyof TransactionFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      type: "all",
      status: "all",
      search: "",
      dateFrom: "",
      dateTo: "",
    });
  };

  // Handle approve
  const handleApproveTransaction = async (id: string) => {
    try {
      console.log(`Approving transaction: ${id}`);
      return await approveTransaction(id);
    } catch (error) {
      console.error("Error in handleApproveTransaction:", error);
      throw error;
    }
  };

  // Handle reject
  const handleRejectTransaction = async (id: string, reason: string = "") => {
    try {
      console.log(`Rejecting transaction: ${id}, reason: ${reason}`);
      return await rejectTransaction({ id, reason });
    } catch (error) {
      console.error("Error in handleRejectTransaction:", error);
      throw error;
    }
  };

  return {
    transactions,
    filteredTransactions,
    isLoading,
    isError,
    refetch,
    filters,
    updateFilter,
    resetFilters,
    handleApproveTransaction,
    handleRejectTransaction,
    isApproving,
    isRejecting,
  };
};
