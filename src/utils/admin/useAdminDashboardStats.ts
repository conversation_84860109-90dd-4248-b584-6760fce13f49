import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";

export function useAdminStats() {
  const fetchDashboardStats = async () => {
    // Fetch all stats in parallel using Promise.all
    const [usersData, storesData, productsData, transactionsData] =
      await Promise.all([
        supabase.from("profiles").select("*", { count: "exact", head: true }),
        supabase.from("stores").select("*", { count: "exact", head: true }),
        supabase.from("products").select("*", { count: "exact", head: true }),
        supabase
          .from("transactions")
          .select("*", { count: "exact", head: true }),
      ]);

    return {
      users: usersData.count || 0,
      stores: storesData.count || 0,
      products: productsData.count || 0,
      transactions: transactionsData.count || 0,
    };
  };

  return useQuery({
    queryKey: ["adminStats"],
    queryFn: fetchDashboardStats,
  });
}
