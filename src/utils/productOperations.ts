
import { Product, ProductStatus } from '../types';
import { toast } from '@/hooks/toast';
import { MOCK_PRODUCTS } from './mockProductData';

// المنتجات العامة (كل المنتجات)
export const fetchProducts = async (): Promise<Product[]> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 1000));
    return MOCK_PRODUCTS;
  } catch (error) {
    console.error('Error loading products:', error);
    toast.error('حدث خطأ أثناء تحميل المنتجات');
    return [];
  }
};

// إضافة منتج جديد
export const addProduct = async (
  productData: Omit<Product, 'id' | 'seller_id' | 'seller_name' | 'created_at' | 'updated_at' | 'status'>,
  userId: string,
  userName: string
): Promise<Product | null> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newProduct: Product = {
      ...productData,
      id: `p-${Date.now()}`,
      seller_id: userId,
      seller_name: userName,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: 'active',
      user_id: userId,
      quantity: productData.quantity || 0 // Ensure quantity is set
    };
    
    toast.success('تمت إضافة المنتج بنجاح');
    return newProduct;
  } catch (error) {
    console.error('Error adding product:', error);
    toast.error('حدث خطأ أثناء إضافة المنتج');
    return null;
  }
};

// تحديث منتج
export const updateProduct = async (
  id: string,
  data: Partial<Product>,
  products: Product[],
  userId: string,
  userRole: string
): Promise<{ success: boolean; updatedProduct?: Product }> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود المنتج وأن المستخدم هو المالك
    const productIndex = products.findIndex(p => p.id === id);
    
    if (productIndex === -1) {
      toast.error('المنتج غير موجود');
      return { success: false };
    }
    
    if (products[productIndex].seller_id !== userId && userRole !== 'admin') {
      toast.error('ليس لديك صلاحية تحديث هذا المنتج');
      return { success: false };
    }
    
    // تحديث المنتج
    const updatedProduct = { 
      ...products[productIndex],
      ...data,
      updated_at: new Date().toISOString()
    };
    
    // Make sure user_id is consistent with seller_id if it was updated
    if (data.seller_id && !data.user_id) {
      updatedProduct.user_id = data.seller_id;
    }
    
    toast.success('تم تحديث المنتج بنجاح');
    return { success: true, updatedProduct };
  } catch (error) {
    console.error('Error updating product:', error);
    toast.error('حدث خطأ أثناء تحديث المنتج');
    return { success: false };
  }
};

// حذف منتج (تغيير الحالة إلى محذوف)
export const deleteProduct = async (
  id: string,
  products: Product[],
  userId: string,
  userRole: string
): Promise<boolean> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود المنتج وأن المستخدم هو المالك
    const product = products.find(p => p.id === id);
    
    if (!product) {
      toast.error('المنتج غير موجود');
      return false;
    }
    
    if (product.seller_id !== userId && userRole !== 'admin') {
      toast.error('ليس لديك صلاحية حذف هذا المنتج');
      return false;
    }
    
    // في بيئة حقيقية، قد نقوم بالحذف الفعلي، لكن هنا سنقوم فقط بتحديث الحالة
    const result = await updateProduct(id, { status: 'deleted' as ProductStatus }, products, userId, userRole);
    
    if (result.success) {
      toast.success('تم حذف المنتج بنجاح');
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error('Error deleting product:', error);
    toast.error('حدث خطأ أثناء حذف المنتج');
    return false;
  }
};

// الحصول على منتج محدد
export const getProduct = (id: string, products: Product[]): Product | undefined => {
  return products.find(p => p.id === id);
};
