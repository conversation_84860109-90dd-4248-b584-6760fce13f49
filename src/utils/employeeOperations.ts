
import { StoreEmployee } from '../types';
import { toast } from '@/hooks/toast';
import { v4 as uuidv4 } from 'uuid';

// Instead of importing MOCK_EMPLOYEES directly, we'll create a mock array here
// since it seems the export name in mockData.ts might be different
const MOCK_EMPLOYEES: StoreEmployee[] = [
  {
    id: "employee-1",
    store_id: "store-1",
    name: "أحمد محمد",
    email: "<EMAIL>",
    phone_number: "0512345678",
    phone: "0512345678", // Added for compatibility
    position: "مدير فرع",
    permissions: ["manage_employees", "manage_products"],
    active: true,
    branch_id: "branch-1",
    role: "manager", // Added for compatibility
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: "user-1", // Added required user_id field
  },
  {
    id: "employee-2",
    store_id: "store-1",
    name: "ليلى سعيد",
    email: "<EMAIL>",
    phone_number: "**********",
    phone: "**********", // Added for compatibility
    position: "محاسب",
    permissions: ["view_transactions", "create_invoices"],
    active: true,
    branch_id: "branch-1",
    role: "accountant", // Added for compatibility
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    user_id: "user-2", // Added required user_id field
  },
];

// إضافة موظف جديد
export const addEmployee = async (
  employeeData: Omit<StoreEmployee, 'id' | 'created_at' | 'updated_at' | 'store_id'>,
  storeId: string
): Promise<StoreEmployee | null> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newEmployee: StoreEmployee = {
      id: uuidv4(),
      store_id: storeId,
      name: employeeData.name,
      email: employeeData.email,
      phone_number: employeeData.phone_number || '',
      phone: employeeData.phone_number || '', // For compatibility
      position: employeeData.position || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      permissions: employeeData.permissions || [],
      active: employeeData.active !== undefined ? employeeData.active : true,
      branch_id: employeeData.branch_id,
      role: employeeData.role || 'employee', // Added for compatibility
      user_id: employeeData.user_id || uuidv4(), // Added the required user_id field
    };
    
    toast.success('تمت إضافة الموظف بنجاح');
    return newEmployee;
  } catch (error) {
    console.error('Error adding employee:', error);
    toast.error('حدث خطأ أثناء إضافة الموظف');
    return null;
  }
};

// تحديث معلومات موظف
export const updateEmployee = async (
  id: string,
  data: Partial<StoreEmployee>,
  employees: StoreEmployee[],
  storeId: string
): Promise<{ success: boolean; updatedEmployee?: StoreEmployee }> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود الموظف
    const employeeIndex = employees.findIndex(e => e.id === id);
    
    if (employeeIndex === -1) {
      toast.error('الموظف غير موجود');
      return { success: false };
    }
    
    // تحديث بيانات الموظف
    const updatedEmployee: StoreEmployee = {
      ...employees[employeeIndex],
      ...data,
      updated_at: new Date().toISOString(),
    };
    
    toast.success('تم تحديث معلومات الموظف بنجاح');
    return { success: true, updatedEmployee };
  } catch (error) {
    console.error('Error updating employee:', error);
    toast.error('حدث خطأ أثناء تحديث معلومات الموظف');
    return { success: false };
  }
};

// حذف موظف
export const deleteEmployee = async (
  id: string,
  employees: StoreEmployee[],
  storeId: string
): Promise<boolean> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود الموظف
    const employeeIndex = employees.findIndex(e => e.id === id);
    
    if (employeeIndex === -1) {
      toast.error('الموظف غير موجود');
      return false;
    }
    
    // هنا يمكننا إرسال طلب لحذف الموظف من قاعدة البيانات
    toast.success('تم حذف الموظف بنجاح');
    return true;
  } catch (error) {
    console.error('Error deleting employee:', error);
    toast.error('حدث خطأ أثناء حذف الموظف');
    return false;
  }
};

// الحصول على قائمة الموظفين
export const fetchEmployees = async (storeId: string): Promise<StoreEmployee[]> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // هنا يجب استبدال البيانات الوهمية ببيانات حقيقية من قاعدة البيانات
    const filteredEmployees = MOCK_EMPLOYEES.filter(employee => employee.store_id === storeId);
    return filteredEmployees;
  } catch (error) {
    console.error('Error fetching employees:', error);
    toast.error('حدث خطأ أثناء جلب قائمة الموظفين');
    return [];
  }
};

// تعديل بيانات الموظف
export const editEmployee = async (
  id: string,
  data: Partial<StoreEmployee>,
  employees: StoreEmployee[],
  storeId: string
): Promise<{ success: boolean; updatedEmployee?: StoreEmployee }> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود الموظف
    const employeeIndex = employees.findIndex(e => e.id === id);
    
    if (employeeIndex === -1) {
      toast.error('الموظف غير موجود');
      return { success: false };
    }
    
    const employee = employees.find(emp => emp.id === id);
    
    if (!employee) {
      toast.error('الموظف غير موجود');
      return { success: false };
    }
    
    const dbEmployee: Partial<StoreEmployee> = {};
    
    if (data.phone_number !== undefined) dbEmployee.phone_number = data.phone_number;
    if (data.position !== undefined) dbEmployee.position = data.position;
    
    if (data.branch_id !== undefined) {
      dbEmployee.branch_id = data.branch_id;
    }
    
    // تحديث بيانات الموظف
    const updatedEmployee: StoreEmployee = {
      ...employee,
      ...dbEmployee,
      updated_at: new Date().toISOString(),
    };
    
    toast.success('تم تحديث معلومات الموظف بنجاح');
    return { success: true, updatedEmployee };
  } catch (error) {
    console.error('Error updating employee:', error);
    toast.error('حدث خطأ أثناء تحديث معلومات الموظف');
    return { success: false };
  }
};

// جلب معلومات الموظف
export const getEmployee = async (
  id: string,
  employees: StoreEmployee[],
  storeId: string
): Promise<StoreEmployee | undefined> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // البحث عن الموظف في قائمة الموظفين
    const employee = employees.find(emp => emp.id === id && emp.store_id === storeId);
    
    if (!employee) {
      toast.error('الموظف غير موجود');
      return undefined;
    }
    
    return employee;
  } catch (error) {
    console.error('Error fetching employee:', error);
    toast.error('حدث خطأ أثناء جلب معلومات الموظف');
    return undefined;
  }
};

// تعديل حالة الموظف
export const updateEmployeeStatus = async (
  id: string,
  active: boolean,
  employees: StoreEmployee[],
  storeId: string
): Promise<{ success: boolean; updatedEmployee?: StoreEmployee }> => {
  try {
    // محاكاة طلب API
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // التحقق من وجود الموظف
    const employeeIndex = employees.findIndex(e => e.id === id);
    
    if (employeeIndex === -1) {
      toast.error('الموظف غير موجود');
      return { success: false };
    }
    
    const employee = employees.find(emp => emp.id === id);
    
    if (!employee) {
      toast.error('الموظف غير موجود');
      return { success: false };
    }
    
    // تحديث حالة الموظف
    const updatedEmployee: StoreEmployee = {
      ...employee,
      active: active,
      updated_at: new Date().toISOString(),
    };
    
    toast.success('تم تحديث حالة الموظف بنجاح');
    return { success: true, updatedEmployee };
  } catch (error) {
    console.error('Error updating employee status:', error);
    toast.error('حدث خطأ أثناء تحديث حالة الموظف');
    return { success: false };
  }
}
