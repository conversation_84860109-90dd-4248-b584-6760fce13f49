import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useCallback } from "react";
import { toast } from "sonner";

export interface LegalDetailsState {
  legalName: string;
  registrationNumber: string;
  taxNumber: string;
  contactPhone: string;
  bankAccount: string;
  legalActivity: string;
  national_id?: string;
  tax_certificate?: string;
  commercial_registry?: string;
}

function useLegalDetails() {
  const { user } = useAuth();

  const fetchLegalDetails = useCallback(async () => {
    try {
      if (!user?.id) return;

      const { data } = await supabase
        .from("legal_details")
        .select("*")
        .eq("profile_id", user.id)
        .single();
      console.log(data);

      return data;
    } catch (error) {
      console.error("Error fetching legal details:", error);
      toast.error("Failed to load legal details");
    }
  }, [user]);

  const { data, isLoading, error } = useQuery({
    queryKey: ["legalDetails", user?.id],
    queryFn: fetchLegalDetails,
    enabled: !!user?.id,
    refetchOnWindowFocus: false,
  });

  const uploadDocumentMutation = useMutation({
    mutationFn: async ({
      documentType,
      file,
    }: {
      documentType: string;
      file: File;
    }) => {
      try {
        const filePath = `documents/${
          user?.id
        }/${documentType}-${Date.now()}.${file.name.split(".").pop()}`;

        const { error: uploadError } = await supabase.storage
          .from("documents")
          .upload(filePath, file, {
            cacheControl: "3600",
            upsert: false,
          });

        if (uploadError) {
          throw uploadError;
        }

        const { data: urlData } = supabase.storage
          .from("documents")
          .getPublicUrl(filePath);

        let updatedField = "";
        if (documentType === "commercialRegistry") {
          updatedField = "commercial_registry";
        } else if (documentType === "taxCertificate") {
          updatedField = "tax_certificate";
        } else if (documentType === "nationalId") {
          updatedField = "national_id";
        }

        return {
          documentType,
          publicUrl: urlData.publicUrl,
          field: updatedField,
        };
      } catch (error) {
        console.error("Error uploading document:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast.success(`Document ${data.documentType} uploaded successfully`);
    },
    onError: (error, variables) => {
      console.error("Error uploading document:", error);
      toast.error(`Failed to upload document ${variables.documentType}`);
    },
  });

  const removeDocumentMutation = useMutation({
    mutationFn: async (documentType: string) => {
      try {
        let updatedField = "";
        if (documentType === "commercialRegistry") {
          updatedField = "commercial_registry";
        } else if (documentType === "taxCertificate") {
          updatedField = "tax_certificate";
        } else if (documentType === "nationalId") {
          updatedField = "national_id";
        }

        return {
          documentType,
          field: updatedField,
        };
      } catch (error) {
        console.error("Error removing document:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      toast.success(`Document removed successfully`);
    },
    onError: (error, variables) => {
      console.error("Error removing document:", error);
      toast.error(`Failed to remove document`);
    },
  });

  const saveLegalDetailsMutation = useMutation({
    mutationFn: async (details: LegalDetailsState) => {
      try {
        if (!user?.id) throw new Error("User not authenticated");

        const { error } = await supabase
          .from("legal_details")
          .upsert(
            {
              profile_id: user.id,
              contact_phone: details.contactPhone,
              tax_number: details.taxNumber,
              bank_account: details.bankAccount,
              legal_activity: details.legalActivity,
              legal_name: details.legalName,
              registration_number: details.registrationNumber,
              national_id: details.national_id,
              tax_certificate: details.tax_certificate,
              commercial_registry: details.commercial_registry,
            },
            { onConflict: "profile_id" }
          )
          .eq("profile_id", user.id)
          .single();

        if (error) {
          throw error;
        }

        return details;
      } catch (error) {
        console.error("Error saving legal details:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success("Legal details saved successfully");
    },
    onError: (error) => {
      console.error("Error saving legal details:", error);
      toast.error("Failed to save legal details");
    },
  });

  return {
    legalDetails: data,
    isLoading,
    error,
    uploadDocument: uploadDocumentMutation.mutate,
    uploadDocumentStatus: uploadDocumentMutation.status,
    isUploadingDocument: uploadDocumentMutation.isPending,
    removeDocument: removeDocumentMutation.mutate,
    removeDocumentStatus: removeDocumentMutation.status,
    isRemovingDocument: removeDocumentMutation.isPending,
    saveLegalDetails: saveLegalDetailsMutation.mutate,
    saveLegalDetailsStatus: saveLegalDetailsMutation.status,
    isSavingLegalDetails: saveLegalDetailsMutation.isPending,
  };
}

export default useLegalDetails;
