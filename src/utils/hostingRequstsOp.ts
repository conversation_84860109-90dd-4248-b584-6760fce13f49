
import { HostingProduct, HostingRequest } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { sendEmailNotification } from "@/services/notificationService";

export async function createRequest(
    request: Omit<HostingRequest, 'id'>,
    products: Omit<HostingProduct, 'id' | 'hosting_request_id'>[]
): Promise<{request_id: string, products_count: number} | null> {
  try {
    // Prepare request data
    const requestData = {
      ecommerce_id: request.ecommerce_id,
      ecommerce_name: request.ecommerce_name,
      store_id: request.store_id,
      store_name: request.store_name,
      status: request.status || 'pending',
      contract_id: request.contract_id,
      notes: request.notes,
      created_at: request.created_at || new Date().toISOString(),
      updated_at: request.updated_at || new Date().toISOString(),
      expires_at: request.expires_at,
      shipping_confirmed: request.shipping_confirmed,
      receipt_confirmed: request.receipt_confirmed,
      ecommerce_legal_activity: request.ecommerce_legal_activity,
      subscription_type: request.subscription_type
    };

    // Prepare products data
    const productsData = products.map(product => ({
      product_id: product.product_id,
      product_name: product.product_name,
      price: product.price,
      quantity: product.quantity,
      image: product.image
    }));

    // Call the stored function
    const { data, error } = await supabase
        .rpc('create_hosting_request', {
          request_data: requestData,
          products_data: productsData
        });
    console.log(data, error);
    if (error) {
      throw new Error(error?.message || 'Unknown error');
    }

    // Send notification to the store owner after creating the request
    try {
      // Get store owner details
      const { data: storeData, error: storeError } = await supabase
        .from('stores')
        .select('owner_id')
        .eq('id', request.store_id)
        .single();
      
      if (!storeError && storeData?.owner_id) {
        // Get owner email
        const { data: ownerData, error: ownerError } = await supabase
          .from('profiles')
          .select('email, name')
          .eq('id', storeData.owner_id)
          .single();

        if (!ownerError && ownerData?.email) {
          // Send email notification to store owner
          const subject = `طلب استضافة جديد من ${request.ecommerce_name}`;
          const message = `
            <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
              <h2>طلب استضافة جديد</h2>
              <p>مرحباً،</p>
              <p>لقد تلقيت طلب استضافة جديد من ${request.ecommerce_name} لمتجرك ${request.store_name}.</p>
              <p>يرجى مراجعة الطلب من خلال لوحة التحكم الخاصة بك.</p>
              <p>شكراً لك،</p>
              <p>فريق RFOF</p>
            </div>
          `;
          
          await sendEmailNotification({
            to: ownerData.email,
            subject,
            message,
            name: ownerData.name || '',
          });
        }
      }
    } catch (notificationError) {
      // Continue even if notification fails
      console.error("Error sending notification:", notificationError);
    }

    toast.success('تم إنشاء طلب الاستضافة بنجاح');
    return {
      request_id: data?.request_id || '',
      products_count: data?.products_count || products.length,
    };
    
  } catch (error) {
    console.error('Error creating hosting request:', error);
    toast.error('فشل في إنشاء طلب الاستضافة');
    return null;
  }
}
