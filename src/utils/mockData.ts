
import { faker } from '@faker-js/faker';

// أنواع بيانات وهمية
export interface RetailStoreDetails {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  contact_name: string;
  contact_phone: string;
  contact_email: string;
  business_type: string;
  tax_id: string;
  created_at: string;
  updated_at: string;
}

// تعريف بيانات المنتج الوهمية
export interface MockProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  quantity: number;
  status: string;
  seller_id: string;
  seller_name: string;
}

// دوال مساعدة لإنشاء بيانات وهمية
const generateRandomId = (): string => faker.string.uuid();
const generateRandomStoreName = (): string => faker.company.name();
const generateRandomAddress = (): string => faker.location.streetAddress();
const generateRandomCity = (): string => faker.location.city();
const generateRandomState = (): string => faker.location.state();
const generateRandomPostalCode = (): string => faker.location.zipCode();
const generateRandomPersonName = (): string => faker.person.fullName();
const generateRandomPhoneNumber = (): string => faker.phone.number();
const generateRandomEmail = (): string => faker.internet.email();
const generateRandomTaxId = (): string => faker.string.numeric(15);
const generateRandomProductName = (): string => faker.commerce.productName();
const generateRandomProductDescription = (): string => faker.commerce.productDescription();
const generateRandomPrice = (): number => parseFloat(faker.commerce.price({ min: 10, max: 500 }));
const generateRandomImage = (): string => faker.image.url();

// دالة للحصول على عنصر عشوائي من مصفوفة
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// وظيفة لإنشاء بيانات متجر تجزئة وهمية
export const createMockRetailStoreDetails = (): RetailStoreDetails => {
  return {
    id: generateRandomId(),
    name: generateRandomStoreName(),
    address: generateRandomAddress(),
    city: generateRandomCity(),
    state: generateRandomState(),
    postal_code: generateRandomPostalCode(),
    country: 'المملكة العربية السعودية',
    contact_name: generateRandomPersonName(),
    contact_phone: generateRandomPhoneNumber(),
    contact_email: generateRandomEmail(),
    business_type: getRandomElement(['محل تجاري', 'سوبر ماركت', 'متجر ملابس', 'متجر إلكترونيات']),
    tax_id: generateRandomTaxId(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
};

// إنشاء مجموعة من بيانات المتاجر الوهمية
export const createMockRetailStoreDetailsList = (count: number): RetailStoreDetails[] => {
  const mockRetailStoreDetailsList: RetailStoreDetails[] = [];
  for (let i = 0; i < count; i++) {
    mockRetailStoreDetailsList.push(createMockRetailStoreDetails());
  }
  return mockRetailStoreDetailsList;
};

// إنشاء منتج وهمي
export const createMockProduct = (userId?: string, userName?: string): MockProduct => {
  const sellerId = userId || generateRandomId();
  const sellerName = userName || generateRandomPersonName();
  
  return {
    id: generateRandomId(),
    name: generateRandomProductName(),
    description: generateRandomProductDescription(),
    price: generateRandomPrice(),
    image: generateRandomImage(),
    category: getRandomElement(['إلكترونيات', 'ملابس', 'أثاث', 'طعام', 'أدوات منزلية']),
    quantity: faker.number.int({ min: 5, max: 100 }),
    status: getRandomElement(['متاح', 'غير متاح', 'منخفض المخزون']),
    seller_id: sellerId,
    seller_name: sellerName
  };
};

// إنشاء قائمة من المنتجات الوهمية
export const createMockProductsList = (count: number, userId?: string, userName?: string): MockProduct[] => {
  const products: MockProduct[] = [];
  for (let i = 0; i < count; i++) {
    products.push(createMockProduct(userId, userName));
  }
  return products;
};

// تصدير مجموعة من المنتجات الوهمية للاستخدام المباشر
export const MOCK_PRODUCTS = createMockProductsList(20);
