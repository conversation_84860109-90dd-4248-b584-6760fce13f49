import { supabase } from "@/integrations/supabase/client.ts";
import { toast } from "sonner";

export const uploadImage = async ({
  storageName,
  file,
  path,
}: {
  storageName: string;
  file: File | null;
  path?: string;
}) => {
  if (!file) return "";
  const toastId = toast.loading("Uploading image...");
  const random = () => Math.random().toString(36).substring(2, 15);

  // Remove Arabic characters (Unicode range 0600-06FF) from filename
  const sanitizeFileName = (fileName: string) => {
    return fileName
      .replace(/[\u0600-\u06FF]/g, "") // Remove Arabic characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/[^\w\-.]/g, ""); // Remove other special characters
  };

  const sanitizedFileName = sanitizeFileName(file.name);

  const { data, error } = await supabase.storage
    .from(storageName)
    .upload(`${path ? `${path}/` : ""}${random()}-${sanitizedFileName}`, file, {
      cacheControl: "3600",
      upsert: false,
    });

  if (error) {
    toast.error("Failed to upload image", { id: toastId });
    console.error("Error uploading image:", error);
    throw new Error("Failed to upload image");
  }
  toast.success("Image uploaded successfully", { id: toastId });

  return supabase.storage.from(storageName).getPublicUrl(data.path).data
    .publicUrl;
};

export const removeImage = async ({
  storageName,
  path,
}: {
  storageName: string;
  path?: string;
  fileName: string;
}) => {
  const toastId = toast.loading("Removing image...");

  const { error } = await supabase.storage.from(storageName).remove([path]);

  if (error) {
    toast.error("Failed to remove image", { id: toastId });
    console.error("Error removing image:", error);
    throw new Error("Failed to remove image");
  }

  toast.success("Image removed successfully", { id: toastId });
};
