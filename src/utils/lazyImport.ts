
import React from "react";

// Helper function for lazy imports with proper typing
export function lazyImport<
  T extends React.ComponentType<any>,
  I extends { [K2 in K]: T },
  K extends keyof I
>(factory: () => Promise<I>, name: K): I {
  return Object.create({
    [name]: React.lazy(() => factory().then((module) => ({ default: module[name] })))
  });
}

// Usage example:
// const { AdminDashboard } = lazyImport(() => import('@/pages/admin/Dashboard'), 'AdminDashboard');
