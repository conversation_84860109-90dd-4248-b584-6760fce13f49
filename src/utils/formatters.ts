
/**
 * تنسيق الأرقام بالعملة السعودية
 * @param amount المبلغ المراد تنسيقه
 * @returns المبلغ بعد التنسيق
 */
export function formatPrice(amount: number): string {
  return amount.toLocaleString('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

/**
 * تنسيق التاريخ باللغة العربية
 * @param dateString سلسلة التاريخ
 * @returns التاريخ المنسق
 */
export function formatDate(dateString: string | null): string {
  if (!dateString) return '-';
  
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * تنسيق الوقت باللغة العربية
 * @param dateString سلسلة التاريخ والوقت
 * @returns الوقت المنسق
 */
export function formatTime(dateString: string | null): string {
  if (!dateString) return '-';
  
  return new Date(dateString).toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * تنسيق التاريخ والوقت معًا باللغة العربية
 * @param dateString سلسلة التاريخ والوقت
 * @returns التاريخ والوقت المنسق
 */
export function formatDateTime(dateString: string | null): string {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  const dateFormatted = date.toLocaleDateString('ar-SA');
  const timeFormatted = date.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit',
  });
  
  return `${dateFormatted} - ${timeFormatted}`;
}

/**
 * تنسيق النص ليعرض أول N حرف فقط
 * @param text النص المراد تنسيقه
 * @param maxLength الحد الأقصى للطول
 * @returns النص بعد التنسيق
 */
export function truncateText(text: string | null, maxLength: number = 50): string {
  if (!text) return '';
  
  if (text.length <= maxLength) return text;
  
  return `${text.substring(0, maxLength)}...`;
}

/**
 * تحويل العدد إلى نسبة مئوية
 * @param value القيمة
 * @param total المجموع
 * @returns النسبة المئوية
 */
export function toPercentage(value: number, total: number): string {
  if (total === 0) return '0%';
  
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(1)}%`;
}
